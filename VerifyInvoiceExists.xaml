﻿<Activity mc:Ignorable="sap sap2010 sads" x:Class="RehostedWorkflowDesigner.Workflow"
 xmlns="http://schemas.microsoft.com/netfx/2009/xaml/activities"
 xmlns:iad="clr-namespace:Infor.Activities.Debug;assembly=Infor.Activities.Debug"
 xmlns:iai="clr-namespace:Infor.Activities.IONAPI;assembly=Infor.Activities.IONAPI"
 xmlns:ias="clr-namespace:Infor.Activities.Sys;assembly=Infor.Activities.Sys"
 xmlns:iru="clr-namespace:Infor.RPA.Utilities;assembly=Infor.RPA.Utilities"
 xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006"
 xmlns:mva="clr-namespace:Microsoft.VisualBasic.Activities;assembly=System.Activities"
 xmlns:njl="clr-namespace:Newtonsoft.Json.Linq;assembly=Newtonsoft.Json"
 xmlns:s="clr-namespace:System;assembly=mscorlib"
 xmlns:sads="http://schemas.microsoft.com/netfx/2010/xaml/activities/debugger"
 xmlns:sap="http://schemas.microsoft.com/netfx/2009/xaml/activities/presentation"
 xmlns:sap2010="http://schemas.microsoft.com/netfx/2010/xaml/activities/presentation"
 xmlns:scg="clr-namespace:System.Collections.Generic;assembly=mscorlib"
 xmlns:sco="clr-namespace:System.Collections.ObjectModel;assembly=mscorlib"
 xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml">
  <x:Members>
    <x:Property Name="logfile" Type="InArgument(x:String)" />
    <x:Property Name="invoiceExist" Type="OutArgument(x:Boolean)" />
    <x:Property Name="tenantID" Type="InArgument(x:String)" />
    <x:Property Name="SupInvoiceNo" Type="InArgument(x:String)" />
    <x:Property Name="division" Type="InArgument(x:String)" />
    <x:Property Name="AYear" Type="InArgument(x:String)" />
    <x:Property Name="vendorID" Type="InArgument(x:String)" />
    <x:Property Name="includeDatalake" Type="InArgument(x:Boolean)" />
  </x:Members>
  <mva:VisualBasic.Settings>
    <x:Null />
  </mva:VisualBasic.Settings>
  <sap2010:WorkflowViewState.IdRef>RehostedWorkflowDesigner.Workflow_1</sap2010:WorkflowViewState.IdRef>
  <TextExpression.NamespacesForImplementation>
    <sco:Collection x:TypeArguments="x:String">
      <x:String>System.Activities</x:String>
      <x:String>System.Activities.Statements</x:String>
      <x:String>System.Activities.Expressions</x:String>
      <x:String>System.Activities.Validation</x:String>
      <x:String>System.Activities.XamlIntegration</x:String>
      <x:String>System.Windows.Controls</x:String>
      <x:String>System.Windows.Documents</x:String>
      <x:String>System.Windows.Shapes</x:String>
      <x:String>System.Windows.Shell</x:String>
      <x:String>System.Windows.Navigation</x:String>
      <x:String>System.Windows.Data</x:String>
      <x:String>System.Windows</x:String>
      <x:String>System.Windows.Controls.Primitives</x:String>
      <x:String>System.Windows.Media.Animation</x:String>
      <x:String>System.Windows.Input</x:String>
      <x:String>System.Windows.Media</x:String>
      <x:String>System.Diagnostics</x:String>
      <x:String>System.Windows.Automation</x:String>
      <x:String>System.Windows.Media.TextFormatting</x:String>
      <x:String>System.Windows.Ink</x:String>
      <x:String>System.Windows.Media.Effects</x:String>
      <x:String>System.Windows.Media.Imaging</x:String>
      <x:String>System.Windows.Media.Media3D</x:String>
      <x:String>System.Windows.Controls.Ribbon</x:String>
      <x:String>System.Windows.Forms.Integration</x:String>
      <x:String>Infor.Activities.Email</x:String>
      <x:String>Infor.Activities.HTTPRequests</x:String>
      <x:String>Infor.Activities.IONAPI</x:String>
      <x:String>Infor.Activities.Sys</x:String>
      <x:String>Infor.Activities.Web</x:String>
      <x:String>Infor.RPA.Utilities</x:String>
      <x:String>Microsoft.VisualBasic.Activities</x:String>
      <x:String>Newtonsoft.Json</x:String>
      <x:String>Newtonsoft.Json.Linq</x:String>
      <x:String>System</x:String>
      <x:String>System.Activities.Debugger.Symbol</x:String>
      <x:String>System.Collections.Generic</x:String>
      <x:String>System.Windows.Markup</x:String>
      <x:String>Microsoft.VisualBasic.CompilerServices</x:String>
    </sco:Collection>
  </TextExpression.NamespacesForImplementation>
  <TextExpression.ReferencesForImplementation>
    <sco:Collection x:TypeArguments="AssemblyReference">
      <AssemblyReference>System.Activities</AssemblyReference>
      <AssemblyReference>PresentationFramework</AssemblyReference>
      <AssemblyReference>WindowsBase</AssemblyReference>
      <AssemblyReference>PresentationCore</AssemblyReference>
      <AssemblyReference>System.Windows.Controls.Ribbon</AssemblyReference>
      <AssemblyReference>WindowsFormsIntegration</AssemblyReference>
      <AssemblyReference>Infor.Activities.Email</AssemblyReference>
      <AssemblyReference>Infor.Activities.HTTPRequests</AssemblyReference>
      <AssemblyReference>Infor.Activities.IONAPI</AssemblyReference>
      <AssemblyReference>Infor.Activities.Sys</AssemblyReference>
      <AssemblyReference>Infor.Activities.Web</AssemblyReference>
      <AssemblyReference>Infor.RPA.Utilities</AssemblyReference>
      <AssemblyReference>Newtonsoft.Json</AssemblyReference>
      <AssemblyReference>mscorlib</AssemblyReference>
      <AssemblyReference>System.ServiceModel</AssemblyReference>
      <AssemblyReference>System</AssemblyReference>
      <AssemblyReference>System.Core</AssemblyReference>
      <AssemblyReference>System.Runtime.WindowsRuntime</AssemblyReference>
      <AssemblyReference>Microsoft.Bcl.AsyncInterfaces</AssemblyReference>
      <AssemblyReference>System.Xaml</AssemblyReference>
      <AssemblyReference>System.ComponentModel.Composition</AssemblyReference>
      <AssemblyReference>Microsoft.VisualBasic</AssemblyReference>
      <AssemblyReference>System.Memory</AssemblyReference>
    </sco:Collection>
  </TextExpression.ReferencesForImplementation>
  <Sequence DisplayName="VerifyInvoiceExists_Sequence_MainSequence_1" sap2010:WorkflowViewState.IdRef="Sequence_7">
    <Assign DisplayName="VerifyInvoiceExists_Assign_invoiceExist_2" sap2010:WorkflowViewState.IdRef="Assign_1">
      <Assign.To>
        <OutArgument x:TypeArguments="x:Boolean">[invoiceExist]</OutArgument>
      </Assign.To>
      <Assign.Value>
        <InArgument x:TypeArguments="x:Boolean">False</InArgument>
      </Assign.Value>
    </Assign>
    <TryCatch DisplayName="VerifyInvoiceExists_TryCatch_MainTryCatch_3" sap2010:WorkflowViewState.IdRef="TryCatch_1">
      <TryCatch.Variables>
        <Variable x:TypeArguments="x:String" Name="jobid" />
        <Variable x:TypeArguments="iru:ResponseObject" Name="resp2" />
        <Variable x:TypeArguments="x:Int32" Name="status2" />
        <Variable x:TypeArguments="njl:JToken" Name="out1" />
      </TryCatch.Variables>
      <TryCatch.Try>
        <Sequence DisplayName="VerifyInvoiceExists_Sequence_TrySequence_4" sap2010:WorkflowViewState.IdRef="Sequence_6">
          <Sequence.Variables>
            <Variable x:TypeArguments="x:String" Name="sinoReq" />
            <Variable x:TypeArguments="x:Int32" Name="invoiceCheckResponseCode" />
            <Variable x:TypeArguments="iru:ResponseObject" Name="invoiceResponse" />
            <Variable x:TypeArguments="x:String" Name="req1" />
            <Variable x:TypeArguments="iru:ResponseObject" Name="respObj1" />
            <Variable x:TypeArguments="x:Int32" Name="StatusCode1" />
          </Sequence.Variables>
          <Assign sap2010:WorkflowViewState.IdRef="Assign_118">
            <Assign.To>
              <OutArgument x:TypeArguments="x:String">[req1]</OutArgument>
            </Assign.To>
            <Assign.Value>
              <InArgument x:TypeArguments="x:String" xml:space="preserve">["* from FPLEDG where EPSINO = " + SupInvoiceNo +  " and EPSUNO = "+vendorID+" and EPDIVI = " +division]</InArgument>
            </Assign.Value>
          </Assign>
          <iai:IONAPIRequestWizard FileAttachments="{x:Null}" PostData="{x:Null}" ResponseCode="{x:Null}" ContentType="application_json" ContinueOnError="True" DisplayName="ExportMI_IONAPIRequestWizard_respObj1_4" HttpMethod="GET" sap2010:WorkflowViewState.IdRef="IONAPIRequestWizard_16" Response="[respObj1]" StatusCode="[StatusCode1]" Url="[TenantID + &quot;M3/m3api-rest/v2/execute/EXPORTMI/Select?SEPC=~&amp;HDRS=0&amp;dateformat=YMD8&amp;excludeempty=false&amp;righttrim=true&amp;format=PRETTY&amp;extendedresult=false&quot;]">
            <iai:IONAPIRequestWizard.Headers>
              <scg:List x:TypeArguments="scg:List(x:String)" Capacity="4">
                <scg:List x:TypeArguments="x:String" Capacity="4">
                  <x:String>Accept</x:String>
                </scg:List>
                <scg:List x:TypeArguments="x:String" Capacity="4">
                  <x:String>application/json</x:String>
                </scg:List>
              </scg:List>
            </iai:IONAPIRequestWizard.Headers>
            <iai:IONAPIRequestWizard.QueryParameters>
              <scg:List x:TypeArguments="scg:List(x:String)" Capacity="4">
                <scg:List x:TypeArguments="x:String" Capacity="4">
                  <x:String>QERY</x:String>
                  <x:String>SEPC</x:String>
                </scg:List>
                <scg:List x:TypeArguments="x:String" Capacity="4">
                  <x:String>req1</x:String>
                  <x:String>~</x:String>
                </scg:List>
              </scg:List>
            </iai:IONAPIRequestWizard.QueryParameters>
          </iai:IONAPIRequestWizard>
          <If Condition="[StatusCode1=200]" sap2010:WorkflowViewState.IdRef="If_53">
            <If.Then>
              <Sequence sap2010:WorkflowViewState.IdRef="Sequence_56">
                <Assign DisplayName="ExportMI_Assign_out1_7" sap2010:WorkflowViewState.IdRef="Assign_119">
                  <Assign.To>
                    <OutArgument x:TypeArguments="njl:JToken">[out1]</OutArgument>
                  </Assign.To>
                  <Assign.Value>
                    <InArgument x:TypeArguments="njl:JToken">[JToken.Parse(respObj1.ReadAsText)]</InArgument>
                  </Assign.Value>
                </Assign>
                <If Condition="[out1(&quot;results&quot;)(0)(&quot;records&quot;).ToString = &quot;[]&quot;]" sap2010:WorkflowViewState.IdRef="If_52">
                  <If.Else>
                    <Sequence DisplayName="VerifyInvoiceExists_Sequence_InvoiceExistsSequence_11" sap2010:WorkflowViewState.IdRef="Sequence_55">
                      <ias:Append_Line ErrorCode="{x:Null}" ContinueOnError="True" DisplayName="VerifyInvoiceExists_AppendLine_APS200Available_12" sap2010:WorkflowViewState.IdRef="Append_Line_4" Line="[&quot;Invoice number &quot;+SupInvoiceNo+&quot; available in EXPRTMI&quot;]" Source="[logfile]" />
                      <Assign DisplayName="VerifyInvoiceExists_Assign_invoiceExistTrue_13" sap2010:WorkflowViewState.IdRef="Assign_120">
                        <Assign.To>
                          <OutArgument x:TypeArguments="x:Boolean">[invoiceExist]</OutArgument>
                        </Assign.To>
                        <Assign.Value>
                          <InArgument x:TypeArguments="x:Boolean">True</InArgument>
                        </Assign.Value>
                      </Assign>
                    </Sequence>
                  </If.Else>
                </If>
              </Sequence>
            </If.Then>
          </If>
          <iad:CommentOut DisplayName="Comment Out" sap2010:WorkflowViewState.IdRef="CommentOut_5">
            <iad:CommentOut.Activities>
              <Assign DisplayName="VerifyInvoiceExists_Assign_sinoReq_5" sap2010:WorkflowViewState.IdRef="Assign_2">
                <Assign.To>
                  <OutArgument x:TypeArguments="x:String">[sinoReq]</OutArgument>
                </Assign.To>
                <Assign.Value>
                  <InArgument x:TypeArguments="x:String">["SINO : '"+SupInvoiceNo+"' AND DIVI : '" +division + "'" + " AND YEA4 : " + AYear + " AND SPYN : '" + vendorID + "'"]</InArgument>
                </Assign.Value>
              </Assign>
              <Assign DisplayName="VerifyInvoiceExists_Assign_sinoReq_5" sap2010:WorkflowViewState.IdRef="Assign_117">
                <Assign.To>
                  <OutArgument x:TypeArguments="x:String">[sinoReq]</OutArgument>
                </Assign.To>
                <Assign.Value>
                  <InArgument x:TypeArguments="x:String">["SINO : '"+SupInvoiceNo+"' AND DIVI : '" +division + "'" + " AND SPYN : '" + vendorID + "'"]</InArgument>
                </Assign.Value>
              </Assign>
              <iai:IONAPIRequestWizard FileAttachments="{x:Null}" PostData="{x:Null}" ResponseCode="{x:Null}" ContentType="application_json" ContinueOnError="True" DisplayName="VerifyInvoiceExists_IONAPIRequestWizard_invoiceResponse_6" HttpMethod="GET" sap2010:WorkflowViewState.IdRef="IONAPIRequestWizard_1" Response="[invoiceResponse]" StatusCode="[invoiceCheckResponseCode]" Url="[TenantID + &quot;M3/m3api-rest/v2/execute/APS200MI/SearchSupInvoic?dateformat=YMD8&amp;excludeempty=false&amp;righttrim=true&amp;format=PRETTY&amp;extendedresult=false&quot;]">
                <iai:IONAPIRequestWizard.Headers>
                  <scg:List x:TypeArguments="scg:List(x:String)" Capacity="4">
                    <scg:List x:TypeArguments="x:String" Capacity="4">
                      <x:String>Accept</x:String>
                    </scg:List>
                    <scg:List x:TypeArguments="x:String" Capacity="4">
                      <x:String>application/json</x:String>
                    </scg:List>
                  </scg:List>
                </iai:IONAPIRequestWizard.Headers>
                <iai:IONAPIRequestWizard.QueryParameters>
                  <scg:List x:TypeArguments="scg:List(x:String)" Capacity="4">
                    <scg:List x:TypeArguments="x:String" Capacity="4">
                      <x:String>SQRY</x:String>
                    </scg:List>
                    <scg:List x:TypeArguments="x:String" Capacity="4">
                      <x:String>sinoReq</x:String>
                    </scg:List>
                  </scg:List>
                </iai:IONAPIRequestWizard.QueryParameters>
              </iai:IONAPIRequestWizard>
              <If Condition="[invoiceCheckResponseCode = 200]" DisplayName="VerifyInvoiceExists_If_invoiceCheckResponseCode_7" sap2010:WorkflowViewState.IdRef="If_2">
                <If.Then>
                  <Sequence DisplayName="VerifyInvoiceExists_Sequence_APS200CheckSequence_8" sap2010:WorkflowViewState.IdRef="Sequence_3">
                    <If Condition="[not invoiceResponse.readasjson(&quot;results&quot;)(0)(&quot;records&quot;).ToString = &quot;[]&quot;]" DisplayName="VerifyInvoiceExists_If_invoiceResponseRecords_9" sap2010:WorkflowViewState.IdRef="If_1">
                      <If.Then>
                        <Sequence DisplayName="VerifyInvoiceExists_Sequence_InvoiceExistsSequence_11" sap2010:WorkflowViewState.IdRef="Sequence_1">
                          <ias:Append_Line ErrorCode="{x:Null}" ContinueOnError="True" DisplayName="VerifyInvoiceExists_AppendLine_APS200Available_12" sap2010:WorkflowViewState.IdRef="Append_Line_1" Line="[&quot;Invoice number &quot;+SupInvoiceNo+&quot; available in APS200&quot;]" Source="[logfile]" />
                          <Assign DisplayName="VerifyInvoiceExists_Assign_invoiceExistTrue_13" sap2010:WorkflowViewState.IdRef="Assign_3">
                            <Assign.To>
                              <OutArgument x:TypeArguments="x:Boolean">[invoiceExist]</OutArgument>
                            </Assign.To>
                            <Assign.Value>
                              <InArgument x:TypeArguments="x:Boolean">True</InArgument>
                            </Assign.Value>
                          </Assign>
                        </Sequence>
                      </If.Then>
                    </If>
                  </Sequence>
                </If.Then>
              </If>
            </iad:CommentOut.Activities>
          </iad:CommentOut>
          <If Condition="[not invoiceExist]" DisplayName="VerifyInvoiceExists_If_notInvoiceExist_14" sap2010:WorkflowViewState.IdRef="If_5">
            <If.Then>
              <Sequence DisplayName="VerifyInvoiceExists_Sequence_APS450CheckSequence_15" sap2010:WorkflowViewState.IdRef="Sequence_5">
                <Sequence.Variables>
                  <Variable x:TypeArguments="x:String" Name="checkInvoiceSqry" />
                  <Variable x:TypeArguments="iru:ResponseObject" Name="checkInvoiceExportMIResponse" />
                  <Variable x:TypeArguments="x:Int32" Name="checkInvoiceExportMIResponseCode" />
                  <Variable x:TypeArguments="x:String" Name="ivdate" />
                  <Variable x:TypeArguments="x:String" Name="AYear1" />
                </Sequence.Variables>
                <Assign DisplayName="VerifyInvoiceExists_Assign_checkInvoiceSqry_16" sap2010:WorkflowViewState.IdRef="Assign_4">
                  <Assign.To>
                    <OutArgument x:TypeArguments="x:String">[checkInvoiceSqry]</OutArgument>
                  </Assign.To>
                  <Assign.Value>
                    <InArgument x:TypeArguments="x:String">["E5IVDT,E5INBN from FAPIBH where E5SINO = '"+SupInvoiceNo+"'" + " and E5DIVI = '" + division + "'" + " and E5SUNO = '" + vendorID + "'"]</InArgument>
                  </Assign.Value>
                </Assign>
                <iai:IONAPIRequestWizard FileAttachments="{x:Null}" Headers="{x:Null}" PostData="{x:Null}" ResponseCode="{x:Null}" ContentType="application_json" ContinueOnError="True" DisplayName="VerifyInvoiceExists_IONAPIRequestWizard_checkInvoiceExportMIResponse_17" HttpMethod="GET" sap2010:WorkflowViewState.IdRef="IONAPIRequestWizard_2" Response="[checkInvoiceExportMIResponse]" StatusCode="[checkInvoiceExportMIResponseCode]" Url="[TenantID + &quot;M3/m3api-rest/v2/execute/EXPORTMI/Select?SEPC=~&amp;HDRS=0&amp;dateformat=YMD8&amp;excludeempty=false&amp;righttrim=true&amp;format=PRETTY&amp;extendedresult=false&quot;]">
                  <iai:IONAPIRequestWizard.QueryParameters>
                    <scg:List x:TypeArguments="scg:List(x:String)" Capacity="4">
                      <scg:List x:TypeArguments="x:String" Capacity="4">
                        <x:String>QERY</x:String>
                      </scg:List>
                      <scg:List x:TypeArguments="x:String" Capacity="4">
                        <x:String>checkInvoiceSqry</x:String>
                      </scg:List>
                    </scg:List>
                  </iai:IONAPIRequestWizard.QueryParameters>
                </iai:IONAPIRequestWizard>
                <If Condition="[checkInvoiceExportMIResponseCode=200]" DisplayName="VerifyInvoiceExists_If_checkInvoiceExportMIResponseCode_18" sap2010:WorkflowViewState.IdRef="If_4">
                  <If.Then>
                    <If Condition="[not checkInvoiceExportMIResponse.readasjson(&quot;results&quot;)(0)(&quot;records&quot;).ToString = &quot;[]&quot;]" DisplayName="VerifyInvoiceExists_If_checkInvoiceExportMIResponseRecords_19" sap2010:WorkflowViewState.IdRef="If_3">
                      <If.Then>
                        <Sequence DisplayName="VerifyInvoiceExists_Sequence_APS450FoundSequence_20" sap2010:WorkflowViewState.IdRef="Sequence_4">
                          <Sequence.Variables>
                            <Variable x:TypeArguments="x:String" Name="InYear" />
                            <Variable x:TypeArguments="iru:ResponseObject" Name="respForFiscalYear" />
                            <Variable x:TypeArguments="x:Int32" Name="respForFiscalYearCode" />
                          </Sequence.Variables>
                          <Sequence DisplayName="VerifyInvoiceExists_Sequence_DirectAPS450Sequence_36" sap2010:WorkflowViewState.IdRef="Sequence_12">
                            <ias:Append_Line ErrorCode="{x:Null}" ContinueOnError="True" DisplayName="VerifyInvoiceExists_AppendLine_APS450AvailableDirect_37" sap2010:WorkflowViewState.IdRef="Append_Line_3" Line="[&quot;Invoice number &quot;+SupInvoiceNo+&quot; available in APS450&quot;]" Source="[logfile]" />
                            <Assign DisplayName="VerifyInvoiceExists_Assign_invoiceExistTrueDirect_38" sap2010:WorkflowViewState.IdRef="Assign_10">
                              <Assign.To>
                                <OutArgument x:TypeArguments="x:Boolean">[invoiceExist]</OutArgument>
                              </Assign.To>
                              <Assign.Value>
                                <InArgument x:TypeArguments="x:Boolean">True</InArgument>
                              </Assign.Value>
                            </Assign>
                          </Sequence>
                        </Sequence>
                      </If.Then>
                    </If>
                  </If.Then>
                </If>
              </Sequence>
            </If.Then>
          </If>
          <If Condition="[not invoiceExist and Not includeDatalake]" DisplayName="VerifyInvoiceExists_If_notInvoiceExist_New" sap2010:WorkflowViewState.IdRef="If_51">
            <If.Then>
              <Sequence DisplayName="VerifyInvoiceExists_Sequence_ExceptionHandling_40" sap2010:WorkflowViewState.IdRef="Sequence_54">
                <Sequence.Variables>
                  <Variable x:TypeArguments="x:String" Name="checkInvoiceSqry" />
                  <Variable x:TypeArguments="iru:ResponseObject" Name="checkInvoiceExportMIResponse" />
                  <Variable x:TypeArguments="x:Int32" Name="checkInvoiceExportMIResponseCode" />
                  <Variable x:TypeArguments="x:String" Name="ivdate" />
                  <Variable x:TypeArguments="x:String" Name="AYear1" />
                  <Variable x:TypeArguments="iru:ResponseObject" Name="resp1" />
                  <Variable x:TypeArguments="x:Int32" Name="status1" />
                </Sequence.Variables>
                <Assign DisplayName="VerifyInvoiceExists_Assign_checkInvoiceSqry_41" sap2010:WorkflowViewState.IdRef="Assign_115">
                  <Assign.To>
                    <OutArgument x:TypeArguments="x:String">[checkInvoiceSqry]</OutArgument>
                  </Assign.To>
                  <Assign.Value>
                    <InArgument x:TypeArguments="x:String">["SELECT h.* FROM RPA_Process_Header h INNER JOIN RPA_Process_Attributes a ON a.AttributeValue = h.Invoice_Number WHERE a.AttributeName = 'Invoice_Number' AND a.AttributeValue = '" + SupInvoiceNo + "' AND UPPER(h.Status) NOT IN ('ARCHIVE','SUCCESS') GROUP BY h.*"]</InArgument>
                  </Assign.Value>
                </Assign>
                <iad:CommentOut DisplayName="Comment Out" sap2010:WorkflowViewState.IdRef="CommentOut_4">
                  <iad:CommentOut.Activities>
                    <Assign DisplayName="VerifyInvoiceExists_Assign_checkInvoiceSqry_41" sap2010:WorkflowViewState.IdRef="Assign_112">
                      <Assign.To>
                        <OutArgument x:TypeArguments="x:String">[checkInvoiceSqry]</OutArgument>
                      </Assign.To>
                      <Assign.Value>
                        <InArgument x:TypeArguments="x:String">["SELECT AttributeValue FROM RPA_Process_Attributes WHERE AttributeName = 'Invoice_Number' AND AttributeValue IN ('" &amp; SupInvoiceNo.Replace("'", "''") &amp; "','INVDEM78797877') GROUP BY 1"]</InArgument>
                      </Assign.Value>
                    </Assign>
                  </iad:CommentOut.Activities>
                </iad:CommentOut>
                <iai:IONAPIRequestWizard FileAttachments="{x:Null}" Headers="{x:Null}" ResponseCode="{x:Null}" ContentType="text_plain" ContinueOnError="False" DisplayName="Job query IONAPI Request" HttpMethod="POST" sap2010:WorkflowViewState.IdRef="IONAPIRequestWizard_15" PostData="[checkInvoiceSqry]" Response="[resp1]" StatusCode="[status1]" Url="[tenantID + &quot;DATAFABRIC/compass/v2/jobs/?records=0&quot;]">
                  <iai:IONAPIRequestWizard.QueryParameters>
                    <scg:List x:TypeArguments="scg:List(x:String)" Capacity="4">
                      <scg:List x:TypeArguments="x:String" Capacity="0" />
                      <scg:List x:TypeArguments="x:String" Capacity="0" />
                    </scg:List>
                  </iai:IONAPIRequestWizard.QueryParameters>
                </iai:IONAPIRequestWizard>
                <Switch x:TypeArguments="x:Boolean" Expression="[status1 = 200 or status1 = 202 OR status1 = 201]" sap2010:WorkflowViewState.IdRef="Switch`1_2">
                  <Sequence x:Key="True" DisplayName="1Sequence" sap2010:WorkflowViewState.IdRef="Sequence_53">
                    <Sequence.Variables>
                      <Variable x:TypeArguments="x:String" Name="division" />
                      <Variable x:TypeArguments="scg:List(x:String)" Name="accountingDimArr" />
                    </Sequence.Variables>
                    <Assign DisplayName="2Assign" sap2010:WorkflowViewState.IdRef="Assign_113">
                      <Assign.To>
                        <OutArgument x:TypeArguments="x:String">[jobid]</OutArgument>
                      </Assign.To>
                      <Assign.Value>
                        <InArgument x:TypeArguments="x:String">[JToken.Parse(resp1.ReadAsText)("queryId").ToString]</InArgument>
                      </Assign.Value>
                    </Assign>
                    <Delay DisplayName="3Delay" Duration="00:00:10" sap2010:WorkflowViewState.IdRef="Delay_3" />
                    <iai:IONAPIRequestWizard FileAttachments="{x:Null}" PostData="{x:Null}" ResponseCode="{x:Null}" ContentType="application_json" ContinueOnError="True" DisplayName="4Status IONAPI Request" HttpMethod="GET" sap2010:WorkflowViewState.IdRef="IONAPIRequestWizard_12" Response="[resp2]" StatusCode="[status2]" Url="[tenantID + &quot;DATAFABRIC/compass/v2/jobs/&quot;+jobId+&quot;/status/?timeout=0&quot;]">
                      <iai:IONAPIRequestWizard.Headers>
                        <scg:List x:TypeArguments="scg:List(x:String)" Capacity="4">
                          <scg:List x:TypeArguments="x:String" Capacity="4">
                            <x:String>Accept</x:String>
                          </scg:List>
                          <scg:List x:TypeArguments="x:String" Capacity="4">
                            <x:String>application/json</x:String>
                          </scg:List>
                        </scg:List>
                      </iai:IONAPIRequestWizard.Headers>
                      <iai:IONAPIRequestWizard.QueryParameters>
                        <scg:List x:TypeArguments="scg:List(x:String)" Capacity="4">
                          <scg:List x:TypeArguments="x:String" Capacity="4">
                            <x:String>queryid</x:String>
                          </scg:List>
                          <scg:List x:TypeArguments="x:String" Capacity="4">
                            <x:String>str1</x:String>
                          </scg:List>
                        </scg:List>
                      </iai:IONAPIRequestWizard.QueryParameters>
                    </iai:IONAPIRequestWizard>
                    <If Condition="[status2 = 200 OR status2 = 202 OR status2 = 201]" DisplayName="4If" sap2010:WorkflowViewState.IdRef="If_50">
                      <If.Then>
                        <Sequence DisplayName="5Sequence" sap2010:WorkflowViewState.IdRef="Sequence_52">
                          <Sequence.Variables>
                            <Variable x:TypeArguments="x:String" Name="variationID" />
                            <Variable x:TypeArguments="x:String" Name="statusComments" />
                            <Variable x:TypeArguments="x:String" Name="status" />
                            <Variable x:TypeArguments="x:String" Name="failureCountStr" />
                            <Variable x:TypeArguments="iru:ResponseObject" Name="resp3" />
                            <Variable x:TypeArguments="x:Int32" Name="status3" />
                          </Sequence.Variables>
                          <While DisplayName="6While" sap2010:WorkflowViewState.IdRef="While_3" Condition="[JToken.Parse(resp2.ReadAsText)(&quot;status&quot;).ToString = &quot;RUNNING&quot;]">
                            <Sequence DisplayName="7Sequence" sap2010:WorkflowViewState.IdRef="Sequence_50">
                              <Delay DisplayName="8Delay" Duration="00:00:10" sap2010:WorkflowViewState.IdRef="Delay_4" />
                              <iai:IONAPIRequestWizard FileAttachments="{x:Null}" PostData="{x:Null}" ResponseCode="{x:Null}" ContentType="application_json" ContinueOnError="True" DisplayName="9Status IONAPI Request" HttpMethod="GET" sap2010:WorkflowViewState.IdRef="IONAPIRequestWizard_13" Response="[resp2]" StatusCode="[status2]" Url="[&quot;https://mingle-ionapi.inforcloudsuite.com/DEVMRKT_DEV/&quot; + &quot;DATAFABRIC/compass/v2/jobs/&quot;+jobId+&quot;/status/?timeout=0&quot;]">
                                <iai:IONAPIRequestWizard.Headers>
                                  <scg:List x:TypeArguments="scg:List(x:String)" Capacity="4">
                                    <scg:List x:TypeArguments="x:String" Capacity="4">
                                      <x:String>Accept</x:String>
                                    </scg:List>
                                    <scg:List x:TypeArguments="x:String" Capacity="4">
                                      <x:String>application/json</x:String>
                                    </scg:List>
                                  </scg:List>
                                </iai:IONAPIRequestWizard.Headers>
                                <iai:IONAPIRequestWizard.QueryParameters>
                                  <scg:List x:TypeArguments="scg:List(x:String)" Capacity="4">
                                    <scg:List x:TypeArguments="x:String" Capacity="4">
                                      <x:String>queryid</x:String>
                                    </scg:List>
                                    <scg:List x:TypeArguments="x:String" Capacity="4">
                                      <x:String>str1</x:String>
                                    </scg:List>
                                  </scg:List>
                                </iai:IONAPIRequestWizard.QueryParameters>
                              </iai:IONAPIRequestWizard>
                            </Sequence>
                          </While>
                          <If Condition="[JToken.Parse(resp2.ReadAsText)(&quot;status&quot;).ToString = &quot;FINISHED&quot;]" DisplayName="10If" sap2010:WorkflowViewState.IdRef="If_48">
                            <If.Then>
                              <iai:IONAPIRequestWizard FileAttachments="{x:Null}" PostData="{x:Null}" ResponseCode="{x:Null}" ContentType="text_plain" ContinueOnError="True" DisplayName="11Result IONAPI Request" HttpMethod="GET" sap2010:WorkflowViewState.IdRef="IONAPIRequestWizard_14" Response="[resp3]" StatusCode="[status3]" Url="[&quot;https://mingle-ionapi.inforcloudsuite.com/DEVMRKT_DEV/&quot; + &quot;DATAFABRIC/compass/v2/jobs/&quot;+jobId+&quot;/result/?offset=0&amp;limit=10000&quot;]">
                                <iai:IONAPIRequestWizard.Headers>
                                  <scg:List x:TypeArguments="scg:List(x:String)" Capacity="4">
                                    <scg:List x:TypeArguments="x:String" Capacity="4">
                                      <x:String>Accept</x:String>
                                    </scg:List>
                                    <scg:List x:TypeArguments="x:String" Capacity="4">
                                      <x:String>application/json</x:String>
                                    </scg:List>
                                  </scg:List>
                                </iai:IONAPIRequestWizard.Headers>
                                <iai:IONAPIRequestWizard.QueryParameters>
                                  <scg:List x:TypeArguments="scg:List(x:String)" Capacity="4">
                                    <scg:List x:TypeArguments="x:String" Capacity="0" />
                                    <scg:List x:TypeArguments="x:String" Capacity="0" />
                                  </scg:List>
                                </iai:IONAPIRequestWizard.QueryParameters>
                              </iai:IONAPIRequestWizard>
                            </If.Then>
                          </If>
                          <If Condition="[status3 = 200 OR status3 = 202 OR status3 = 201]" DisplayName="13If" sap2010:WorkflowViewState.IdRef="If_49">
                            <If.Then>
                              <Sequence DisplayName="14Sequence" sap2010:WorkflowViewState.IdRef="Sequence_51">
                                <Sequence.Variables>
                                  <Variable x:TypeArguments="s:DateTime" Name="today" />
                                  <Variable x:TypeArguments="s:DateTime" Name="dt1" />
                                  <Variable x:TypeArguments="x:String" Name="lastRunTime" />
                                  <Variable x:TypeArguments="x:Int32" Name="failureCount" />
                                  <Variable x:TypeArguments="x:String" Name="vendorName" />
                                  <Variable x:TypeArguments="scg:IDictionary(x:String, x:Object)" Name="poInvoiceResponseDictionary" />
                                  <Variable x:TypeArguments="x:String" Name="accountingDim" />
                                </Sequence.Variables>
                                <Assign DisplayName="15Assign" sap2010:WorkflowViewState.IdRef="Assign_114">
                                  <Assign.To>
                                    <OutArgument x:TypeArguments="njl:JToken">[out1]</OutArgument>
                                  </Assign.To>
                                  <Assign.Value>
                                    <InArgument x:TypeArguments="njl:JToken">[JToken.Parse(resp3.ReadAsText)]</InArgument>
                                  </Assign.Value>
                                </Assign>
                                <Assign DisplayName="Assign - invoiceExist IN Exception Handling" sap2010:WorkflowViewState.IdRef="Assign_116">
                                  <Assign.To>
                                    <OutArgument x:TypeArguments="x:Boolean">[invoiceExist]</OutArgument>
                                  </Assign.To>
                                  <Assign.Value>
                                    <InArgument x:TypeArguments="x:Boolean">[(CType(out1, JArray).Count)&gt;0]</InArgument>
                                  </Assign.Value>
                                </Assign>
                              </Sequence>
                            </If.Then>
                          </If>
                        </Sequence>
                      </If.Then>
                    </If>
                  </Sequence>
                </Switch>
              </Sequence>
            </If.Then>
          </If>
        </Sequence>
      </TryCatch.Try>
      <TryCatch.Catches>
        <Catch x:TypeArguments="s:Exception" sap2010:WorkflowViewState.IdRef="Catch`1_1">
          <ActivityAction x:TypeArguments="s:Exception">
            <ActivityAction.Argument>
              <DelegateInArgument x:TypeArguments="s:Exception" Name="exception" />
            </ActivityAction.Argument>
            <Assign DisplayName="VerifyInvoiceExists_Assign_invoiceExistFalseException_39" sap2010:WorkflowViewState.IdRef="Assign_11">
              <Assign.To>
                <OutArgument x:TypeArguments="x:Boolean">[invoiceExist]</OutArgument>
              </Assign.To>
              <Assign.Value>
                <InArgument x:TypeArguments="x:Boolean">False</InArgument>
              </Assign.Value>
            </Assign>
          </ActivityAction>
        </Catch>
      </TryCatch.Catches>
    </TryCatch>
    <sads:DebugSymbol.Symbol>d19DOlxVc2Vyc1xkbWl0dGFwYWxsaVxBcHBEYXRhXExvY2FsXEluZm9yUlBBXE0zSW52b2ljZVByb2Nlc3NpbmdHZW5BSVYzXFZlcmlmeUludm9pY2VFeGlzdHMueGFtbGhlA/sDDgIBAWYFbQ4DAZ4BbgX5AxACAQJrMWs2AwGhAWgyaEADAZ8BdgnmAxQCAQfuAw31AxYCAQN/C4YBFAMBlwGHAQueASUDAZABnwELuwEQAgF8vAEL+gEcAgF7+wELtQIQAgFZtgIL5QMQAgEI8wM58wM+AgEG8AM68ANIAgEEgQE3gQE9AwGYAYcBqgKHAbYCAwGVAYcB1gKHAZQEAwGTAYcBwgKHAdECAwGRAZ8BGZ8BLAIBfaEBD7kBGgIBf/sBGfsBLQIBWv0BD7MCGgIBXLYCGbYCRQIBCbgCD+MDGgIBDKIBEakBGgMBjAGqARG4ARYDAYABhQIRjAIaAgF0jQIRmAIrAgFtmQIRsgIWAgFdwgIRyQIaAgFUygIR1QIiAgFT1gIR3QIrAgFK3gIR4gMaAgENpwE+pwFhAwGPAaQBP6QBRQMBjQGqAR+qAW4DAYEBrAEVtgEgAwGCAYoCPIoCxQECAXeHAj2HAk8CAXWNAuICjQKCAwIBco0CtwONAvUEAgFwjQKOA40CsgMCAW6ZAh+ZAkcCAV6bAhWwAhoCAWDHAjzHAsQCAgFXxAI9xAJPAgFV1gKcAtYCsAICAVHWAroC1gLDAgIBT9YC3wLWAp8DAgFN1gLPAtYC2gICAUveAkDeAnMCAQ7fAhPhAx4CARKtARetAa0CAwGHAa4BF7UBIAMBgwGbAiObApkBAgFhnQIZrgIkAgFi5AIV6wIeAgFG7AIV7AJxAgFE7QIVggMvAgE8gwMV4AMaAgETrQHJAa0BlwIDAYoBrQGfAq0BqgIDAYgBswFDswFHAwGGAbABRLABUgMBhAGjAhutAiYCAWPpAkDpAnQCAUnmAkHmAkgCAUfsAjrsAkQCAUXtAqMC7QKsAgIBQu0CyALtAqMDAgE/7QK4Au0CwwICAT2DAyODA1YCARSFAxneAyQCARikAh2kArgCAgFopQIdrAImAgFkjgMbqAMjAgEwqQMbvgMgAgEnvwMb3QMgAgEZpALVAaQCogICAWukAqoCpAK1AgIBaaoCSaoCTQIBZ6cCSqcCWAIBZY8DHacDKAIBMo4Da44DwAECATGpAympA38CASirAx+8AzkCASm/Aym/A1wCARrBAx/bAyoCAR6QAx+QA3sCATqRAx+mAzkCATOrA6gCqwOxAgIBLqsDzQKrA/EDAgEsqwO9AqsDyAICASrLAyHSAyoCASPTAyHaAyoCAR+QA0SQA04CATuRA60CkQO2AgIBOJED0gKRA+cDAgE2kQPCApEDzQICATTQA07QA24CASbNA0/NA1UCASTYA03YA28CASLVA07VA1wCASA=</sads:DebugSymbol.Symbol>
  </Sequence>
  <sap2010:WorkflowViewState.ViewStateManager>
    <sap2010:ViewStateManager>
      <sap2010:ViewStateData Id="Assign_1" sap:VirtualizedContainerService.HintSize="651,60" />
      <sap2010:ViewStateData Id="Assign_118" sap:VirtualizedContainerService.HintSize="611,60" />
      <sap2010:ViewStateData Id="IONAPIRequestWizard_16" sap:VirtualizedContainerService.HintSize="611,22" />
      <sap2010:ViewStateData Id="Assign_119" sap:VirtualizedContainerService.HintSize="464,60" />
      <sap2010:ViewStateData Id="Append_Line_4" sap:VirtualizedContainerService.HintSize="242,22" />
      <sap2010:ViewStateData Id="Assign_120" sap:VirtualizedContainerService.HintSize="242,60" />
      <sap2010:ViewStateData Id="Sequence_55" sap:VirtualizedContainerService.HintSize="264,246">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">True</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="If_52" sap:VirtualizedContainerService.HintSize="464,394" />
      <sap2010:ViewStateData Id="Sequence_56" sap:VirtualizedContainerService.HintSize="486,618">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">True</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="If_53" sap:VirtualizedContainerService.HintSize="611,766" />
      <sap2010:ViewStateData Id="Assign_2" sap:VirtualizedContainerService.HintSize="611,60" />
      <sap2010:ViewStateData Id="Assign_117" sap:VirtualizedContainerService.HintSize="611,60" />
      <sap2010:ViewStateData Id="IONAPIRequestWizard_1" sap:VirtualizedContainerService.HintSize="611,22" />
      <sap2010:ViewStateData Id="Append_Line_1" sap:VirtualizedContainerService.HintSize="242,22" />
      <sap2010:ViewStateData Id="Assign_3" sap:VirtualizedContainerService.HintSize="242,60" />
      <sap2010:ViewStateData Id="Sequence_1" sap:VirtualizedContainerService.HintSize="264,246">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">True</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="If_1" sap:VirtualizedContainerService.HintSize="464,394">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">True</x:Boolean>
            <x:Boolean x:Key="IsPinned">False</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="Sequence_3" sap:VirtualizedContainerService.HintSize="486,518">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">True</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="If_2" sap:VirtualizedContainerService.HintSize="611,666">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">True</x:Boolean>
            <x:Boolean x:Key="IsPinned">False</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="CommentOut_5" sap:VirtualizedContainerService.HintSize="611,58">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">False</x:Boolean>
            <x:Boolean x:Key="IsPinned">False</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="Assign_4" sap:VirtualizedContainerService.HintSize="589,60" />
      <sap2010:ViewStateData Id="IONAPIRequestWizard_2" sap:VirtualizedContainerService.HintSize="589,22" />
      <sap2010:ViewStateData Id="Append_Line_3" sap:VirtualizedContainerService.HintSize="242,22" />
      <sap2010:ViewStateData Id="Assign_10" sap:VirtualizedContainerService.HintSize="242,60" />
      <sap2010:ViewStateData Id="Sequence_12" sap:VirtualizedContainerService.HintSize="264,246">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">True</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="Sequence_4" sap:VirtualizedContainerService.HintSize="286,370">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">True</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="If_3" sap:VirtualizedContainerService.HintSize="464,518" />
      <sap2010:ViewStateData Id="If_4" sap:VirtualizedContainerService.HintSize="589,666">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">True</x:Boolean>
            <x:Boolean x:Key="IsPinned">False</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="Sequence_5" sap:VirtualizedContainerService.HintSize="611,952">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">True</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="If_5" sap:VirtualizedContainerService.HintSize="611,51">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">False</x:Boolean>
            <x:Boolean x:Key="IsPinned">False</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="Assign_115" sap:VirtualizedContainerService.HintSize="476,60" />
      <sap2010:ViewStateData Id="Assign_112" sap:VirtualizedContainerService.HintSize="242,60" />
      <sap2010:ViewStateData Id="CommentOut_4" sap:VirtualizedContainerService.HintSize="476,58">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">False</x:Boolean>
            <x:Boolean x:Key="IsPinned">False</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="IONAPIRequestWizard_15" sap:VirtualizedContainerService.HintSize="476,22" />
      <sap2010:ViewStateData Id="Assign_113" sap:VirtualizedContainerService.HintSize="612,60" />
      <sap2010:ViewStateData Id="Delay_3" sap:VirtualizedContainerService.HintSize="612,22" />
      <sap2010:ViewStateData Id="IONAPIRequestWizard_12" sap:VirtualizedContainerService.HintSize="612,22" />
      <sap2010:ViewStateData Id="Delay_4" sap:VirtualizedContainerService.HintSize="200,22" />
      <sap2010:ViewStateData Id="IONAPIRequestWizard_13" sap:VirtualizedContainerService.HintSize="200,22" />
      <sap2010:ViewStateData Id="Sequence_50" sap:VirtualizedContainerService.HintSize="222,208">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">True</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="While_3" sap:VirtualizedContainerService.HintSize="464,372">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">True</x:Boolean>
            <x:Boolean x:Key="IsPinned">False</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="IONAPIRequestWizard_14" sap:VirtualizedContainerService.HintSize="200,22" />
      <sap2010:ViewStateData Id="If_48" sap:VirtualizedContainerService.HintSize="464,52.6666666666667">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">False</x:Boolean>
            <x:Boolean x:Key="IsPinned">False</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="Assign_114" sap:VirtualizedContainerService.HintSize="242,62" />
      <sap2010:ViewStateData Id="Assign_116" sap:VirtualizedContainerService.HintSize="242,62" />
      <sap2010:ViewStateData Id="Sequence_51" sap:VirtualizedContainerService.HintSize="264,288">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">True</x:Boolean>
            <x:Boolean x:Key="IsPinned">False</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="If_49" sap:VirtualizedContainerService.HintSize="464,442">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">True</x:Boolean>
            <x:Boolean x:Key="IsPinned">False</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="Sequence_52" sap:VirtualizedContainerService.HintSize="486,1070.66666666667">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">True</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="If_50" sap:VirtualizedContainerService.HintSize="612,1219">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">True</x:Boolean>
            <x:Boolean x:Key="IsPinned">False</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="Sequence_53" sap:VirtualizedContainerService.HintSize="634,1567">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">True</x:Boolean>
            <x:Boolean x:Key="IsPinned">False</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="Switch`1_2" sap:VirtualizedContainerService.HintSize="476,138" />
      <sap2010:ViewStateData Id="Sequence_54" sap:VirtualizedContainerService.HintSize="200,51">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">False</x:Boolean>
            <x:Boolean x:Key="IsPinned">False</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="If_51" sap:VirtualizedContainerService.HintSize="611,208">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">True</x:Boolean>
            <x:Boolean x:Key="IsPinned">False</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="Sequence_6" sap:VirtualizedContainerService.HintSize="633,1489">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">True</x:Boolean>
            <x:Boolean x:Key="IsPinned">False</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="Assign_11" sap:VirtualizedContainerService.HintSize="242,60" />
      <sap2010:ViewStateData Id="Catch`1_1" sap:VirtualizedContainerService.HintSize="637,21">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">False</x:Boolean>
            <x:Boolean x:Key="IsPinned">False</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="TryCatch_1" sap:VirtualizedContainerService.HintSize="651,1716">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">True</x:Boolean>
            <x:Boolean x:Key="IsPinned">False</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="Sequence_7" sap:VirtualizedContainerService.HintSize="673,1940">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">True</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="RehostedWorkflowDesigner.Workflow_1" sap:VirtualizedContainerService.HintSize="713,2060" />
    </sap2010:ViewStateManager>
  </sap2010:WorkflowViewState.ViewStateManager>
</Activity>