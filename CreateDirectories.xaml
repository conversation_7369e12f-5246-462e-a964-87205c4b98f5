﻿<Activity mc:Ignorable="sap sap2010 sads" x:Class="RehostedWorkflowDesigner.Workflow"
 xmlns="http://schemas.microsoft.com/netfx/2009/xaml/activities"
 xmlns:ias="clr-namespace:Infor.Activities.Sys;assembly=Infor.Activities.Sys"
 xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006"
 xmlns:mva="clr-namespace:Microsoft.VisualBasic.Activities;assembly=System.Activities"
 xmlns:sads="http://schemas.microsoft.com/netfx/2010/xaml/activities/debugger"
 xmlns:sap="http://schemas.microsoft.com/netfx/2009/xaml/activities/presentation"
 xmlns:sap2010="http://schemas.microsoft.com/netfx/2010/xaml/activities/presentation"
 xmlns:scg="clr-namespace:System.Collections.Generic;assembly=mscorlib"
 xmlns:sco="clr-namespace:System.Collections.ObjectModel;assembly=mscorlib"
 xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml">
  <x:Members>
    <x:Property Name="configurationFolder" Type="InArgument(x:String)" />
    <x:Property Name="directoriesNames" Type="InArgument(scg:List(x:String))" />
    <x:Property Name="logFolderName" Type="InArgument(x:String)" />
    <x:Property Name="logFile" Type="OutArgument(x:String)" />
  </x:Members>
  <mva:VisualBasic.Settings>
    <x:Null />
  </mva:VisualBasic.Settings>
  <sap2010:WorkflowViewState.IdRef>RehostedWorkflowDesigner.Workflow_1</sap2010:WorkflowViewState.IdRef>
  <TextExpression.NamespacesForImplementation>
    <sco:Collection x:TypeArguments="x:String">
      <x:String>System.Activities</x:String>
      <x:String>System.Activities.Statements</x:String>
      <x:String>System.Activities.Expressions</x:String>
      <x:String>System.Activities.Validation</x:String>
      <x:String>System.Activities.XamlIntegration</x:String>
      <x:String>Infor.Activities.Email</x:String>
      <x:String>Infor.Activities.Sys</x:String>
      <x:String>Microsoft.VisualBasic.Activities</x:String>
      <x:String>Microsoft.VisualBasic.CompilerServices</x:String>
      <x:String>System</x:String>
      <x:String>System.Activities.Debugger.Symbol</x:String>
      <x:String>System.Collections.Generic</x:String>
      <x:String>System.Windows.Markup</x:String>
      <x:String>Microsoft.VisualBasic</x:String>
      <x:String>System.IO</x:String>
    </sco:Collection>
  </TextExpression.NamespacesForImplementation>
  <TextExpression.ReferencesForImplementation>
    <sco:Collection x:TypeArguments="AssemblyReference">
      <AssemblyReference>System.Activities</AssemblyReference>
      <AssemblyReference>Infor.Activities.Email</AssemblyReference>
      <AssemblyReference>Infor.Activities.Sys</AssemblyReference>
      <AssemblyReference>Microsoft.VisualBasic</AssemblyReference>
      <AssemblyReference>System</AssemblyReference>
      <AssemblyReference>System.Core</AssemblyReference>
      <AssemblyReference>mscorlib</AssemblyReference>
      <AssemblyReference>System.ServiceModel</AssemblyReference>
      <AssemblyReference>System.Runtime.WindowsRuntime</AssemblyReference>
      <AssemblyReference>Microsoft.Bcl.AsyncInterfaces</AssemblyReference>
      <AssemblyReference>PresentationFramework</AssemblyReference>
      <AssemblyReference>WindowsBase</AssemblyReference>
      <AssemblyReference>PresentationCore</AssemblyReference>
      <AssemblyReference>System.Xaml</AssemblyReference>
      <AssemblyReference>System.ComponentModel.Composition</AssemblyReference>
      <AssemblyReference>System.Memory</AssemblyReference>
    </sco:Collection>
  </TextExpression.ReferencesForImplementation>
  <Sequence DisplayName="CheckDirectoriesSequence" sap2010:WorkflowViewState.IdRef="Sequence_1">
    <Sequence.Variables>
      <Variable x:TypeArguments="x:Boolean" Name="logFileExist" />
      <Variable x:TypeArguments="x:Boolean" Name="folderExist" />
      <Variable x:TypeArguments="scg:List(x:String)" Name="ListFilesToMove" />
      <Variable x:TypeArguments="x:String" Name="strFilePath" />
    </Sequence.Variables>
    <ias:Directory_Delete ErrorCode="{x:Null}" ContinueOnError="True" DisplayName="Delete Directory" sap2010:WorkflowViewState.IdRef="Directory_Delete_2" Source="[configurationFolder+ &quot;\OutlookDownloads&quot;]" />
    <ForEach x:TypeArguments="x:String" DisplayName="Create Directory for each Directory Names" sap2010:WorkflowViewState.IdRef="ForEach`1_1" Values="[directoriesNames]">
      <ActivityAction x:TypeArguments="x:String">
        <ActivityAction.Argument>
          <DelegateInArgument x:TypeArguments="x:String" Name="directory" />
        </ActivityAction.Argument>
        <Sequence DisplayName="Inside for each Create Directory Sequence" sap2010:WorkflowViewState.IdRef="Sequence_2">
          <Sequence.Variables>
            <Variable x:TypeArguments="x:String" Name="folder" />
          </Sequence.Variables>
          <Assign sap2010:WorkflowViewState.IdRef="Assign_1">
            <Assign.To>
              <OutArgument x:TypeArguments="x:String">[folder]</OutArgument>
            </Assign.To>
            <Assign.Value>
              <InArgument x:TypeArguments="x:String">[configurationFolder+ "\"+ directory]</InArgument>
            </Assign.Value>
          </Assign>
          <ias:Path_Validate ErrorCode="{x:Null}" ContinueOnError="True" DisplayName="Path Exists" sap2010:WorkflowViewState.IdRef="Path_Validate_1" IsValid="[folderExist]" Path="[folder]" />
          <If Condition="[not folderExist]" DisplayName="Create Directory if not exists" sap2010:WorkflowViewState.IdRef="If_1">
            <If.Then>
              <ias:Directory_Create ErrorCode="{x:Null}" OutputPath="{x:Null}" ContinueOnError="False" DisplayName="Create Directory" sap2010:WorkflowViewState.IdRef="Directory_Create_9" Name="[directory]" Target="[configurationFolder]" />
            </If.Then>
          </If>
        </Sequence>
      </ActivityAction>
    </ForEach>
    <Assign sap2010:WorkflowViewState.IdRef="Assign_2">
      <Assign.To>
        <OutArgument x:TypeArguments="x:String">[logFile]</OutArgument>
      </Assign.To>
      <Assign.Value>
        <InArgument x:TypeArguments="x:String">[configurationFolder+"\"+logFolderName+"\"+"JobsResults"+System.DateTime.Now.ToString("ddMMyyyy")+".txt"]</InArgument>
      </Assign.Value>
    </Assign>
    <ias:Path_Validate ErrorCode="{x:Null}" ContinueOnError="True" DisplayName="Path Exists" sap2010:WorkflowViewState.IdRef="Path_Validate_2" IsValid="[logFileExist]" Path="[logFile]" />
    <If Condition="[not logFileExist]" DisplayName="Create Logfile if not exists" sap2010:WorkflowViewState.IdRef="If_2">
      <If.Then>
        <ias:File_Create ErrorCode="{x:Null}" ContinueOnError="True" DisplayName="Create File" sap2010:WorkflowViewState.IdRef="File_Create_1" Name="[&quot;JobsResults&quot;+System.DateTime.Now.ToString(&quot;ddMMyyyy&quot;)+&quot;.txt&quot;]" OutputFile="[logFile]" Target="[configurationFolder+&quot;\&quot;+logFolderName]" />
      </If.Then>
    </If>
    <If Condition="[directoriesNames.contains(&quot;Success&quot;)]" DisplayName="Check if Current Directory is Success folder" sap2010:WorkflowViewState.IdRef="If_3">
      <If.Then>
        <Sequence DisplayName="True Sequence" sap2010:WorkflowViewState.IdRef="Sequence_3">
          <Sequence.Variables>
            <Variable x:TypeArguments="x:Boolean" Name="currentDateSuccessFolderExist" />
          </Sequence.Variables>
          <ias:Path_Validate ErrorCode="{x:Null}" ContinueOnError="True" DisplayName="Path Exists" sap2010:WorkflowViewState.IdRef="Path_Validate_3" IsValid="[currentDateSuccessFolderExist]" Path="[configurationFolder+&quot;\Success\&quot;+System.DateTime.Now.ToString(&quot;yyyy-MM-dd&quot;)]" />
          <If Condition="[not currentDateSuccessFolderExist]" DisplayName="Check If currentDateSuccessFolderExist" sap2010:WorkflowViewState.IdRef="If_4">
            <If.Then>
              <ias:Directory_Create ErrorCode="{x:Null}" OutputPath="{x:Null}" ContinueOnError="True" DisplayName="Create Date Specific Success Directory" sap2010:WorkflowViewState.IdRef="Directory_Create_2" Name="[System.DateTime.Now.ToString(&quot;yyyy-MM-dd&quot;)]" Target="[configurationFolder+&quot;\Success&quot;]" />
            </If.Then>
          </If>
        </Sequence>
      </If.Then>
    </If>
    <If Condition="[directoriesNames.Contains(&quot;OutlookDownloads&quot;)]" DisplayName="Check if OutlookDownloads folder" sap2010:WorkflowViewState.IdRef="If_9">
      <If.Then>
        <Sequence DisplayName="OutlookDownloads Sequence" sap2010:WorkflowViewState.IdRef="Sequence_10">
          <Sequence.Variables>
            <Variable x:TypeArguments="x:Boolean" Name="DownloadFolders" />
          </Sequence.Variables>
          <Sequence DisplayName="Create Process Downlaod Folder" sap2010:WorkflowViewState.IdRef="Sequence_11">
            <ias:Path_Validate ErrorCode="{x:Null}" ContinueOnError="True" DisplayName="Path Exists" sap2010:WorkflowViewState.IdRef="Path_Validate_11" IsValid="[DownloadFolders]" Path="[configurationFolder+&quot;\OutlookDownloads\DownloadedFiles_1&quot;]" />
            <If Condition="[NOT DownloadFolders]" sap2010:WorkflowViewState.IdRef="If_16">
              <If.Then>
                <ias:Directory_Create ErrorCode="{x:Null}" OutputPath="{x:Null}" ContinueOnError="False" DisplayName="Create Directory" sap2010:WorkflowViewState.IdRef="Directory_Create_15" Name="DownloadedFiles_1" Target="[configurationFolder+&quot;\OutlookDownloads&quot;]" />
              </If.Then>
            </If>
          </Sequence>
          <Sequence DisplayName="Create Master Downlaod Folder" sap2010:WorkflowViewState.IdRef="Sequence_12">
            <ias:Path_Validate ErrorCode="{x:Null}" ContinueOnError="True" DisplayName="Path Exists" sap2010:WorkflowViewState.IdRef="Path_Validate_6" IsValid="[DownloadFolders]" Path="[configurationFolder+&quot;\OutlookDownloads\MasterDownloads&quot;]" />
            <If Condition="[NOT DownloadFolders]" DisplayName="Chekc if MasterDownloads Already exists" sap2010:WorkflowViewState.IdRef="If_11">
              <If.Then>
                <ias:Directory_Create ErrorCode="{x:Null}" OutputPath="{x:Null}" ContinueOnError="False" DisplayName="Create Directory" sap2010:WorkflowViewState.IdRef="Directory_Create_10" Name="MasterDownloads" Target="[configurationFolder+&quot;\OutlookDownloads&quot;]" />
              </If.Then>
            </If>
          </Sequence>
          <Sequence DisplayName="Create OCRData Folder" sap2010:WorkflowViewState.IdRef="Sequence_13">
            <ias:Path_Validate ErrorCode="{x:Null}" ContinueOnError="True" DisplayName="Path Exists" sap2010:WorkflowViewState.IdRef="Path_Validate_7" IsValid="[DownloadFolders]" Path="[configurationFolder+&quot;\OutlookDownloads\OCRData&quot;]" />
            <If Condition="[NOT DownloadFolders]" DisplayName="Chekc if OCRData Already exists" sap2010:WorkflowViewState.IdRef="If_12">
              <If.Then>
                <ias:Directory_Create ErrorCode="{x:Null}" OutputPath="{x:Null}" ContinueOnError="False" DisplayName="Create Directory" sap2010:WorkflowViewState.IdRef="Directory_Create_11" Name="OCRData" Target="[configurationFolder+&quot;\OutlookDownloads&quot;]" />
              </If.Then>
            </If>
          </Sequence>
          <Sequence DisplayName="Create ClassificationData Folder" sap2010:WorkflowViewState.IdRef="Sequence_14">
            <ias:Path_Validate ErrorCode="{x:Null}" ContinueOnError="True" DisplayName="Path Exists" sap2010:WorkflowViewState.IdRef="Path_Validate_8" IsValid="[DownloadFolders]" Path="[configurationFolder+&quot;\OutlookDownloads\ClassificationData&quot;]" />
            <If Condition="[NOT DownloadFolders]" DisplayName="Chekc if ClassificationData Already exists" sap2010:WorkflowViewState.IdRef="If_13">
              <If.Then>
                <ias:Directory_Create ErrorCode="{x:Null}" OutputPath="{x:Null}" ContinueOnError="False" DisplayName="Create Directory" sap2010:WorkflowViewState.IdRef="Directory_Create_12" Name="ClassificationData" Target="[configurationFolder+&quot;\OutlookDownloads&quot;]" />
              </If.Then>
            </If>
          </Sequence>
          <Sequence DisplayName="Create BulkFiles Folder" sap2010:WorkflowViewState.IdRef="Sequence_15">
            <ias:Path_Validate ErrorCode="{x:Null}" ContinueOnError="True" DisplayName="Path Exists" sap2010:WorkflowViewState.IdRef="Path_Validate_9" IsValid="[DownloadFolders]" Path="[configurationFolder+&quot;\OutlookDownloads\BulkFiles&quot;]" />
            <If Condition="[NOT DownloadFolders]" DisplayName="Chekc if BulkFiles folder Already exists" sap2010:WorkflowViewState.IdRef="If_14">
              <If.Then>
                <ias:Directory_Create ErrorCode="{x:Null}" OutputPath="{x:Null}" ContinueOnError="False" DisplayName="Create Directory" sap2010:WorkflowViewState.IdRef="Directory_Create_13" Name="BulkFiles" Target="[configurationFolder+&quot;\OutlookDownloads&quot;]" />
              </If.Then>
            </If>
          </Sequence>
          <Sequence DisplayName="Create Processing Folder" sap2010:WorkflowViewState.IdRef="Sequence_16">
            <ias:Path_Validate ErrorCode="{x:Null}" ContinueOnError="True" DisplayName="Path Exists" sap2010:WorkflowViewState.IdRef="Path_Validate_10" IsValid="[DownloadFolders]" Path="[configurationFolder+&quot;\OutlookDownloads\BulkFiles\Processing&quot;]" />
            <If Condition="[NOT DownloadFolders]" DisplayName="Chekc if Processing folder Already exists" sap2010:WorkflowViewState.IdRef="If_15">
              <If.Then>
                <ias:Directory_Create ErrorCode="{x:Null}" OutputPath="{x:Null}" ContinueOnError="False" DisplayName="Create Directory" sap2010:WorkflowViewState.IdRef="Directory_Create_14" Name="Processing" Target="[configurationFolder+&quot;\OutlookDownloads\BulkFiles&quot;]" />
              </If.Then>
            </If>
          </Sequence>
        </Sequence>
      </If.Then>
    </If>
    <Sequence DisplayName="Move Inprogress Files and Reprocess files to MasterDownloads" sap2010:WorkflowViewState.IdRef="Sequence_22">
      <Sequence DisplayName="Move Inprogress Files to MasterDownloads" sap2010:WorkflowViewState.IdRef="Sequence_23">
        <ias:Directory_GetFiles ErrorCode="{x:Null}" ContinueOnError="False" DirPath="[configurationFolder+ &quot;\InProgress&quot;]" DisplayName="Get Files in Directory" FileType="PDF" Files="[ListFilesToMove]" sap2010:WorkflowViewState.IdRef="Directory_GetFiles_3" IncludeSubDir="True" />
        <ForEach x:TypeArguments="x:String" DisplayName="ForEach&lt;String&gt;" sap2010:WorkflowViewState.IdRef="ForEach`1_4" Values="[ListFilesToMove]">
          <ActivityAction x:TypeArguments="x:String">
            <ActivityAction.Argument>
              <DelegateInArgument x:TypeArguments="x:String" Name="item" />
            </ActivityAction.Argument>
            <ias:File_Move ErrorCode="{x:Null}" ContinueOnError="True" DisplayName="Move File" sap2010:WorkflowViewState.IdRef="File_Move_3" OutputFile="[strFilePath]" OverwriteFile="False" Source="[item]" Target="[configurationFolder+&quot;\OutlookDownloads\MasterDownloads&quot;]" targetName="[Path.GetfileNamewithoutExtension(item).tostring+&quot;_Reprocess.pdf&quot;]" />
          </ActivityAction>
        </ForEach>
      </Sequence>
      <Sequence DisplayName="Move Reprocess Files to MasterDownloads" sap2010:WorkflowViewState.IdRef="Sequence_24">
        <ias:Directory_GetFiles ErrorCode="{x:Null}" ContinueOnError="False" DirPath="[configurationFolder+ &quot;\ReProcess&quot;]" DisplayName="Get Files in Directory" FileType="PDF" Files="[ListFilesToMove]" sap2010:WorkflowViewState.IdRef="Directory_GetFiles_4" IncludeSubDir="True" />
        <ForEach x:TypeArguments="x:String" DisplayName="ForEach&lt;String&gt;" sap2010:WorkflowViewState.IdRef="ForEach`1_5" Values="[ListFilesToMove]">
          <ActivityAction x:TypeArguments="x:String">
            <ActivityAction.Argument>
              <DelegateInArgument x:TypeArguments="x:String" Name="item" />
            </ActivityAction.Argument>
            <ias:File_Move ErrorCode="{x:Null}" ContinueOnError="True" DisplayName="Move File" sap2010:WorkflowViewState.IdRef="File_Move_4" OutputFile="[strFilePath]" OverwriteFile="False" Source="[item]" Target="[configurationFolder+&quot;\OutlookDownloads\MasterDownloads&quot;]" targetName="[Path.GetfileNamewithoutExtension(item).tostring+&quot;_Reprocess.pdf&quot;]" />
          </ActivityAction>
        </ForEach>
      </Sequence>
    </Sequence>
    <sads:DebugSymbol.Symbol>d11DOlxVc2Vyc1xza2F2aXJheWFuaVxBcHBEYXRhXExvY2FsXEluZm9yUlBBXE0zSW52b2ljZVByb2Nlc3NpbmdHZW5BSVYzXENyZWF0ZURpcmVjdG9yaWVzLnhhbWx4PQPOAQ4CAQFEBUTbAQMBswFFBV4PAwGcAV8FZg4DAZYBZwVnvAEDAZEBaAVsCgMBhwFtBXsKAgF3fAW0AQoCASW1AQXMARACAQJEogFE2AEDAbQBRZYBRaoBAwGxAUoJXBQDAZ0BZDBkmQEDAZkBYTFhOgMBlwFnmAFnqAEDAZQBZ64BZ7kBAwGSAWgTaCcDAYgBaglqyQIDAYoBbRNtRQIBeG8JeRQCAXp8E3xOAgEmfgmyARQCASi2AQfAARICARTBAQfLARICAQNOC1UUAwGrAVYLVsABAwGmAVcLWxADAZ4BaoACaosCAwGPAWqTAmrGAgMBjAFqlQFq9AEDAYsBcwtzqgIDAYIBdAt4EAIBe4IBC4kBFgIBaooBC5EBFgIBXZIBC5kBFgIBUJoBC6EBFgIBQ6IBC6kBFgIBNqoBC7EBFgIBKbcBCbcBowICASC4AQm/ARMCARXCAQnCAaICAgEPwwEJygETAgEEUzZTWwMBrgFQN1A/AwGsAVaeAVatAQMBqQFWswFWvQEDAacBVxlXLAMBnwFZD1nwAQMBoQFzngFzvwEDAYUBc8UBc6cCAwGDAXQZdD4CAXx2D3bFAgIBfoMBDYMBhAICAXKEAQ2IARICAWuLAQ2LAYECAgFljAENkAESAgFekwENkwH5AQIBWJQBDZgBEgIBUZsBDZsBhAICAUucAQ2gARICAUSjAQ2jAfsBAgE+pAENqAESAgE3qwENqwGHAgIBMawBDbABEgIBKrcBwQG3AdQBAgEjtwFWtwGGAQIBIbgBhgG4AZkBAgEevQENvQH4AgIBFsIBwAHCAdMBAgESwgFWwgGFAQIBEMMBhgHDAZkBAgENyAENyAH4AgIBBVnWAVntAQMBpAFZwQFZzgEDAaIBdpYCdsICAwGAAXbWAXaOAgIBf4MBoQGDAbQBAgF1gwG6AYMBgQICAXOEARuEATICAWyGARGGAZcCAgFuiwGgAYsBswECAWiLAbkBiwH+AQIBZowBG4wBMgIBX44BEY4BlQICAWGTAaABkwGzAQIBW5MBuQGTAfYBAgFZlAEblAEyAgFSlgERlgGNAgIBVJsBoAGbAbMBAgFOmwG5AZsBgQICAUycARucATICAUWeARGeAZgCAgFHowGgAaMBswECAUGjAbkBowH4AQIBP6QBG6QBMgIBOKYBEaYBjwICATqrAaEBqwG0AQIBNKsBugGrAYQCAgEyrAEbrAEyAgErrgERrgGaAgIBLb0BmQG9AagBAgEcvQHWAb0BmwICARq9AacCvQH1AgIBGb0BxgG9Ac4BAgEXyAGZAcgBqAECAQvIAdYByAGbAgIBCcgBpwLIAfUCAgEIyAHGAcgBzgECAQaGAd8BhgGUAgIBcIYBxAGGAdcBAgFvjgHdAY4BkgICAWOOAcQBjgHVAQIBYpYB1QGWAYoCAgFWlgHEAZYBzQECAVWeAeABngGVAgIBSZ4BxAGeAdgBAgFIpgHXAaYBjAICATymAcQBpgHPAQIBO64B2AGuAZcCAgEvrgHEAa4B0AECAS4=</sads:DebugSymbol.Symbol>
  </Sequence>
  <sap2010:WorkflowViewState.ViewStateManager>
    <sap2010:ViewStateManager>
      <sap2010:ViewStateData Id="Directory_Delete_2" sap:VirtualizedContainerService.HintSize="464,22" />
      <sap2010:ViewStateData Id="Assign_1" sap:VirtualizedContainerService.HintSize="242,62" />
      <sap2010:ViewStateData Id="Path_Validate_1" sap:VirtualizedContainerService.HintSize="242,22" />
      <sap2010:ViewStateData Id="Directory_Create_9" sap:VirtualizedContainerService.HintSize="200,22" />
      <sap2010:ViewStateData Id="If_1" sap:VirtualizedContainerService.HintSize="242,52.6666666666667">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">False</x:Boolean>
            <x:Boolean x:Key="IsPinned">False</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="Sequence_2" sap:VirtualizedContainerService.HintSize="200,52.6666666666667">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">False</x:Boolean>
            <x:Boolean x:Key="IsPinned">False</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="ForEach`1_1" sap:VirtualizedContainerService.HintSize="464,52.6666666666667">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">False</x:Boolean>
            <x:Boolean x:Key="IsPinned">False</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="Assign_2" sap:VirtualizedContainerService.HintSize="464,62" />
      <sap2010:ViewStateData Id="Path_Validate_2" sap:VirtualizedContainerService.HintSize="464,22" />
      <sap2010:ViewStateData Id="File_Create_1" sap:VirtualizedContainerService.HintSize="200,22" />
      <sap2010:ViewStateData Id="If_2" sap:VirtualizedContainerService.HintSize="464,52.6666666666667">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">False</x:Boolean>
            <x:Boolean x:Key="IsPinned">False</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="Path_Validate_3" sap:VirtualizedContainerService.HintSize="464,22" />
      <sap2010:ViewStateData Id="Directory_Create_2" sap:VirtualizedContainerService.HintSize="200,22" />
      <sap2010:ViewStateData Id="If_4" sap:VirtualizedContainerService.HintSize="464,214">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">True</x:Boolean>
            <x:Boolean x:Key="IsPinned">False</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="Sequence_3" sap:VirtualizedContainerService.HintSize="486,400">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">True</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="If_3" sap:VirtualizedContainerService.HintSize="464,52.6666666666667">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">False</x:Boolean>
            <x:Boolean x:Key="IsPinned">False</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="Path_Validate_11" sap:VirtualizedContainerService.HintSize="464,22" />
      <sap2010:ViewStateData Id="Directory_Create_15" sap:VirtualizedContainerService.HintSize="200,22" />
      <sap2010:ViewStateData Id="If_16" sap:VirtualizedContainerService.HintSize="464,214">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">True</x:Boolean>
            <x:Boolean x:Key="IsPinned">False</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="Sequence_11" sap:VirtualizedContainerService.HintSize="200,52.6666666666667">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">False</x:Boolean>
            <x:Boolean x:Key="IsPinned">False</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="Path_Validate_6" sap:VirtualizedContainerService.HintSize="200,22" />
      <sap2010:ViewStateData Id="Directory_Create_10" sap:VirtualizedContainerService.HintSize="200,22" />
      <sap2010:ViewStateData Id="If_11" sap:VirtualizedContainerService.HintSize="200,52.6666666666667">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">False</x:Boolean>
            <x:Boolean x:Key="IsPinned">False</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="Sequence_12" sap:VirtualizedContainerService.HintSize="200,52.6666666666667">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">False</x:Boolean>
            <x:Boolean x:Key="IsPinned">False</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="Path_Validate_7" sap:VirtualizedContainerService.HintSize="200,22" />
      <sap2010:ViewStateData Id="Directory_Create_11" sap:VirtualizedContainerService.HintSize="200,22" />
      <sap2010:ViewStateData Id="If_12" sap:VirtualizedContainerService.HintSize="200,52">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">False</x:Boolean>
            <x:Boolean x:Key="IsPinned">False</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="Sequence_13" sap:VirtualizedContainerService.HintSize="200,52.6666666666667">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">False</x:Boolean>
            <x:Boolean x:Key="IsPinned">False</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="Path_Validate_8" sap:VirtualizedContainerService.HintSize="200,22" />
      <sap2010:ViewStateData Id="Directory_Create_12" sap:VirtualizedContainerService.HintSize="200,22" />
      <sap2010:ViewStateData Id="If_13" sap:VirtualizedContainerService.HintSize="200,52.6666666666667">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">False</x:Boolean>
            <x:Boolean x:Key="IsPinned">False</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="Sequence_14" sap:VirtualizedContainerService.HintSize="200,52.6666666666667">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">False</x:Boolean>
            <x:Boolean x:Key="IsPinned">False</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="Path_Validate_9" sap:VirtualizedContainerService.HintSize="200,22" />
      <sap2010:ViewStateData Id="Directory_Create_13" sap:VirtualizedContainerService.HintSize="200,22" />
      <sap2010:ViewStateData Id="If_14" sap:VirtualizedContainerService.HintSize="200,52">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">False</x:Boolean>
            <x:Boolean x:Key="IsPinned">False</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="Sequence_15" sap:VirtualizedContainerService.HintSize="200,52.6666666666667">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">False</x:Boolean>
            <x:Boolean x:Key="IsPinned">False</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="Path_Validate_10" sap:VirtualizedContainerService.HintSize="200,22" />
      <sap2010:ViewStateData Id="Directory_Create_14" sap:VirtualizedContainerService.HintSize="200,22" />
      <sap2010:ViewStateData Id="If_15" sap:VirtualizedContainerService.HintSize="200,52">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">False</x:Boolean>
            <x:Boolean x:Key="IsPinned">False</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="Sequence_16" sap:VirtualizedContainerService.HintSize="200,52.6666666666667">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">False</x:Boolean>
            <x:Boolean x:Key="IsPinned">False</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="Sequence_10" sap:VirtualizedContainerService.HintSize="200,52.6666666666667">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">False</x:Boolean>
            <x:Boolean x:Key="IsPinned">False</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="If_9" sap:VirtualizedContainerService.HintSize="464,214">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">True</x:Boolean>
            <x:Boolean x:Key="IsPinned">False</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="Directory_GetFiles_3" sap:VirtualizedContainerService.HintSize="200,22" />
      <sap2010:ViewStateData Id="File_Move_3" sap:VirtualizedContainerService.HintSize="200,22" />
      <sap2010:ViewStateData Id="ForEach`1_4" sap:VirtualizedContainerService.HintSize="200,52.6666666666667">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">False</x:Boolean>
            <x:Boolean x:Key="IsPinned">False</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="Sequence_23" sap:VirtualizedContainerService.HintSize="306.666666666667,52.6666666666667">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">False</x:Boolean>
            <x:Boolean x:Key="IsPinned">False</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="Directory_GetFiles_4" sap:VirtualizedContainerService.HintSize="284.666666666667,22" />
      <sap2010:ViewStateData Id="File_Move_4" sap:VirtualizedContainerService.HintSize="200,22" />
      <sap2010:ViewStateData Id="ForEach`1_5" sap:VirtualizedContainerService.HintSize="284.666666666667,212.666666666667">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">True</x:Boolean>
            <x:Boolean x:Key="IsPinned">False</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="Sequence_24" sap:VirtualizedContainerService.HintSize="306.666666666667,398.666666666667">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">True</x:Boolean>
            <x:Boolean x:Key="IsPinned">False</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="Sequence_22" sap:VirtualizedContainerService.HintSize="464,615.333333333333">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">True</x:Boolean>
            <x:Boolean x:Key="IsPinned">False</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="Sequence_1" sap:VirtualizedContainerService.HintSize="486,1497.33333333333">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">True</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="RehostedWorkflowDesigner.Workflow_1" sap:VirtualizedContainerService.HintSize="526,1617.33333333333" />
    </sap2010:ViewStateManager>
  </sap2010:WorkflowViewState.ViewStateManager>
</Activity>