﻿<Activity mc:Ignorable="sap sap2010 sads" x:Class="RehostedWorkflowDesigner.Workflow"
 xmlns="http://schemas.microsoft.com/netfx/2009/xaml/activities"
 xmlns:iad="clr-namespace:Infor.Activities.Debug;assembly=Infor.Activities.Debug"
 xmlns:iai="clr-namespace:Infor.Activities.IONAPI;assembly=Infor.Activities.IONAPI"
 xmlns:ias="clr-namespace:Infor.Activities.Sys;assembly=Infor.Activities.Sys"
 xmlns:iaw="clr-namespace:Infor.Activities.Workflow;assembly=Infor.Activities.Workflow"
 xmlns:iru="clr-namespace:Infor.RPA.Utilities;assembly=Infor.RPA.Utilities"
 xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006"
 xmlns:mva="clr-namespace:Microsoft.VisualBasic.Activities;assembly=System.Activities"
 xmlns:njl="clr-namespace:Newtonsoft.Json.Linq;assembly=Newtonsoft.Json"
 xmlns:s="clr-namespace:System;assembly=mscorlib"
 xmlns:sads="http://schemas.microsoft.com/netfx/2010/xaml/activities/debugger"
 xmlns:sap="http://schemas.microsoft.com/netfx/2009/xaml/activities/presentation"
 xmlns:sap2010="http://schemas.microsoft.com/netfx/2010/xaml/activities/presentation"
 xmlns:scg="clr-namespace:System.Collections.Generic;assembly=mscorlib"
 xmlns:sco="clr-namespace:System.Collections.ObjectModel;assembly=mscorlib"
 xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml">
  <x:Members>
    <x:Property Name="vendorId" Type="InArgument(x:String)" />
    <x:Property Name="ivdate" Type="InArgument(x:String)" />
    <x:Property Name="division" Type="InArgument(x:String)" />
    <x:Property Name="sino" Type="InArgument(x:String)" />
    <x:Property Name="cucd" Type="InArgument(x:String)" />
    <x:Property Name="tepy" Type="InArgument(x:String)" />
    <x:Property Name="pyme" Type="InArgument(x:String)" />
    <x:Property Name="cuam" Type="InArgument(x:String)" />
    <x:Property Name="bkid" Type="InArgument(x:String)" />
    <x:Property Name="correlationID" Type="InArgument(x:String)" />
    <x:Property Name="projectPath" Type="InArgument(x:String)" />
    <x:Property Name="M3TotalTableRows" Type="InArgument(scg:List(s:String[]))" />
    <x:Property Name="vatCodeConfig" Type="InArgument(x:String)" />
    <x:Property Name="inbnValue" Type="InArgument(x:String)" />
    <x:Property Name="ListocrLineValues" Type="InArgument(scg:List(scg:Dictionary(x:String, x:Object)))" />
    <x:Property Name="commentStatus" Type="OutArgument(x:String)" />
    <x:Property Name="Status" Type="OutArgument(x:String)" />
    <x:Property Name="DictOcrValues" Type="InArgument(scg:IDictionary(x:String, x:Object))" />
    <x:Property Name="tenantID" Type="InArgument(x:String)" />
    <x:Property Name="miscValues" Type="InArgument(scg:Dictionary(x:String, x:Object))" />
    <x:Property Name="str_AcceptWithDebitNote" Type="InArgument(x:String)" />
    <x:Property Name="str_PendingProcess" Type="InArgument(x:String)" />
    <x:Property Name="logFile" Type="InArgument(x:String)" />
    <x:Property Name="authUser" Type="InArgument(x:String)" />
  </x:Members>
  <mva:VisualBasic.Settings>
    <x:Null />
  </mva:VisualBasic.Settings>
  <sap2010:WorkflowViewState.IdRef>RehostedWorkflowDesigner.Workflow_1</sap2010:WorkflowViewState.IdRef>
  <TextExpression.NamespacesForImplementation>
    <sco:Collection x:TypeArguments="x:String">
      <x:String>System.Activities</x:String>
      <x:String>System.Activities.Statements</x:String>
      <x:String>System.Activities.Expressions</x:String>
      <x:String>System.Activities.Validation</x:String>
      <x:String>System.Activities.XamlIntegration</x:String>
      <x:String>Infor.Activities.Email</x:String>
      <x:String>Microsoft.VisualBasic.Activities</x:String>
      <x:String>System.Activities.Debugger.Symbol</x:String>
      <x:String>System.Collections.Generic</x:String>
      <x:String>System.Linq</x:String>
      <x:String>System.Windows.Markup</x:String>
      <x:String>Microsoft.VisualBasic.CompilerServices</x:String>
      <x:String>System</x:String>
      <x:String>Infor.RPA.Utilities</x:String>
      <x:String>Newtonsoft.Json.Linq</x:String>
      <x:String>Newtonsoft.Json</x:String>
      <x:String>System.Globalization</x:String>
    </sco:Collection>
  </TextExpression.NamespacesForImplementation>
  <TextExpression.ReferencesForImplementation>
    <sco:Collection x:TypeArguments="AssemblyReference">
      <AssemblyReference>System.Activities</AssemblyReference>
      <AssemblyReference>Infor.Activities.Email</AssemblyReference>
      <AssemblyReference>mscorlib</AssemblyReference>
      <AssemblyReference>System.Core</AssemblyReference>
      <AssemblyReference>PresentationFramework</AssemblyReference>
      <AssemblyReference>WindowsBase</AssemblyReference>
      <AssemblyReference>PresentationCore</AssemblyReference>
      <AssemblyReference>System.Xaml</AssemblyReference>
      <AssemblyReference>Microsoft.VisualBasic</AssemblyReference>
      <AssemblyReference>System</AssemblyReference>
      <AssemblyReference>System.ServiceModel</AssemblyReference>
      <AssemblyReference>System.ComponentModel.Composition</AssemblyReference>
      <AssemblyReference>System.Memory</AssemblyReference>
      <AssemblyReference>System.Runtime.WindowsRuntime</AssemblyReference>
      <AssemblyReference>Microsoft.Bcl.AsyncInterfaces</AssemblyReference>
      <AssemblyReference>Infor.RPA.Utilities</AssemblyReference>
      <AssemblyReference>Newtonsoft.Json</AssemblyReference>
      <AssemblyReference>System.Collections.Immutable</AssemblyReference>
    </sco:Collection>
  </TextExpression.ReferencesForImplementation>
  <Sequence sap2010:WorkflowViewState.IdRef="Sequence_75">
    <Sequence.Variables>
      <Variable x:TypeArguments="iru:ResponseObject" Name="respObj5" />
      <Variable x:TypeArguments="x:Int32" Name="StatusCode5" />
      <Variable x:TypeArguments="iru:ResponseObject" Name="respObj0" />
      <Variable x:TypeArguments="x:Int32" Name="StatusCode0" />
      <Variable x:TypeArguments="x:String" Name="poInvoiceResponseCode" />
      <Variable x:TypeArguments="scg:IDictionary(x:String, x:Object)" Name="poInvoiceResponseDictionary" />
      <Variable x:TypeArguments="scg:IDictionary(x:String, x:Object)" Name="AddHeadOcrWorkflowOutput" />
      <Variable x:TypeArguments="x:Int32" Name="AddHeadOcrWorkflowStatus" />
      <Variable x:TypeArguments="x:String" Name="amt" />
      <Variable x:TypeArguments="x:Int32" Name="TotalAmount" />
      <Variable x:TypeArguments="iru:ResponseObject" Name="respObj2" />
      <Variable x:TypeArguments="x:Int32" Name="StatusCode2" />
      <Variable x:TypeArguments="scg:IDictionary(x:String, x:Object)" Name="DivisiontoGLcodeWorkflowOutput" />
      <Variable x:TypeArguments="x:Int32" Name="DivisiontoGLcodeWorkflowStatus" />
      <Variable x:TypeArguments="njl:JToken" Name="out1" />
      <Variable x:TypeArguments="x:String" Name="originalyear" />
    </Sequence.Variables>
    <Sequence sap2010:WorkflowViewState.IdRef="Sequence_176">
      <Sequence sap2010:WorkflowViewState.IdRef="Sequence_165">
        <Assign sap2010:WorkflowViewState.IdRef="Assign_343">
          <Assign.To>
            <OutArgument x:TypeArguments="x:String">[inbnValue]</OutArgument>
          </Assign.To>
          <Assign.Value>
            <InArgument x:TypeArguments="x:String">
              <Literal x:TypeArguments="x:String" Value="" />
            </InArgument>
          </Assign.Value>
        </Assign>
        <Assign sap2010:WorkflowViewState.IdRef="Assign_334">
          <Assign.To>
            <OutArgument x:TypeArguments="x:String">[amt]</OutArgument>
          </Assign.To>
          <Assign.Value>
            <InArgument x:TypeArguments="x:String">0</InArgument>
          </Assign.Value>
        </Assign>
        <Assign sap2010:WorkflowViewState.IdRef="Assign_400">
          <Assign.To>
            <OutArgument x:TypeArguments="x:Int32">[TotalAmount]</OutArgument>
          </Assign.To>
          <Assign.Value>
            <InArgument x:TypeArguments="x:Int32">0</InArgument>
          </Assign.Value>
        </Assign>
        <ForEach x:TypeArguments="scg:Dictionary(x:String, x:Object)" DisplayName="ForEach&lt;Dictionary&lt;String,Object&gt;&gt;" sap2010:WorkflowViewState.IdRef="ForEach`1_6" Values="[ListocrLineValues]">
          <ActivityAction x:TypeArguments="scg:Dictionary(x:String, x:Object)">
            <ActivityAction.Argument>
              <DelegateInArgument x:TypeArguments="scg:Dictionary(x:String, x:Object)" Name="item" />
            </ActivityAction.Argument>
            <Sequence sap2010:WorkflowViewState.IdRef="Sequence_191">
              <Sequence.Variables>
                <Variable x:TypeArguments="iru:ResponseObject" Name="respObj7" />
                <Variable x:TypeArguments="x:Int32" Name="StatusCode7" />
                <Variable x:TypeArguments="njl:JToken" Name="out7" />
              </Sequence.Variables>
              <If Condition="[item(&quot;CHOICE&quot;).ToString.contains(&quot;debit&quot;)]" sap2010:WorkflowViewState.IdRef="If_138">
                <If.Then>
                  <Assign sap2010:WorkflowViewState.IdRef="Assign_398">
                    <Assign.To>
                      <OutArgument x:TypeArguments="x:Int32">[TotalAmount]</OutArgument>
                    </Assign.To>
                    <Assign.Value>
                      <InArgument x:TypeArguments="x:Int32">[CInt(item("LINE_AMOUNT").ToString)+TotalAmount]</InArgument>
                    </Assign.Value>
                  </Assign>
                </If.Then>
              </If>
            </Sequence>
          </ActivityAction>
        </ForEach>
      </Sequence>
      <Sequence DisplayName="Adding DebitNote Header" sap2010:WorkflowViewState.IdRef="Sequence_59">
        <Sequence.Variables>
          <Variable x:TypeArguments="x:String" Name="InvoiceNumber_DR" />
          <Variable x:TypeArguments="x:String" Name="Amount_DR" />
          <Variable x:TypeArguments="x:String" Name="temp" />
        </Sequence.Variables>
        <Assign sap2010:WorkflowViewState.IdRef="Assign_99">
          <Assign.To>
            <OutArgument x:TypeArguments="x:String">[InvoiceNumber_DR]</OutArgument>
          </Assign.To>
          <Assign.Value>
            <InArgument x:TypeArguments="x:String">[(sino+"_DR").ToString]</InArgument>
          </Assign.Value>
        </Assign>
        <Assign sap2010:WorkflowViewState.IdRef="Assign_402">
          <Assign.To>
            <OutArgument x:TypeArguments="x:String">[temp]</OutArgument>
          </Assign.To>
          <Assign.Value>
            <InArgument x:TypeArguments="x:String">[InvoiceNumber_DR]</InArgument>
          </Assign.Value>
        </Assign>
        <iad:CommentOut DisplayName="Comment Out" sap2010:WorkflowViewState.IdRef="CommentOut_6">
          <iad:CommentOut.Activities>
            <ias:MessageBox ButtonSelection="{x:Null}" ErrorCode="{x:Null}" Title="{x:Null}" ContinueOnError="True" DisplayName="Message Box" sap2010:WorkflowViewState.IdRef="MessageBox_33" Selection="OK" Text="[InvoiceNumber_DR]" />
          </iad:CommentOut.Activities>
        </iad:CommentOut>
        <Assign sap2010:WorkflowViewState.IdRef="Assign_401">
          <Assign.To>
            <OutArgument x:TypeArguments="x:String">[originalyear]</OutArgument>
          </Assign.To>
          <Assign.Value>
            <InArgument x:TypeArguments="x:String">[DateTime.ParseExact(DictOcrValues("INVOICE_RECEIPT_DATE").tostring, "dd/MM/yyyy", System.Globalization.CultureInfo.InvariantCulture).Year.ToString]</InArgument>
          </Assign.Value>
        </Assign>
        <Assign sap2010:WorkflowViewState.IdRef="Assign_100">
          <Assign.To>
            <OutArgument x:TypeArguments="x:String">[Amount_DR]</OutArgument>
          </Assign.To>
          <Assign.Value>
            <InArgument x:TypeArguments="x:String">["-"+TotalAmount.ToString]</InArgument>
          </Assign.Value>
        </Assign>
        <iai:IONAPIRequestWizard FileAttachments="{x:Null}" PostData="{x:Null}" ResponseCode="{x:Null}" ContentType="application_json" ContinueOnError="True" DisplayName="AddHead_IONAPIRequestWizard_8" HttpMethod="GET" sap2010:WorkflowViewState.IdRef="IONAPIRequestWizard_38" Response="[respObj5]" StatusCode="[StatusCode5]" Url="[TenantID + &quot;M3/m3api-rest/v2/execute/APS450MI/AddHead&quot;]">
          <iai:IONAPIRequestWizard.Headers>
            <scg:List x:TypeArguments="scg:List(x:String)" Capacity="4">
              <scg:List x:TypeArguments="x:String" Capacity="4">
                <x:String>Accept</x:String>
              </scg:List>
              <scg:List x:TypeArguments="x:String" Capacity="4">
                <x:String>application/json</x:String>
              </scg:List>
            </scg:List>
          </iai:IONAPIRequestWizard.Headers>
          <iai:IONAPIRequestWizard.QueryParameters>
            <scg:List x:TypeArguments="scg:List(x:String)" Capacity="4">
              <scg:List x:TypeArguments="x:String" Capacity="32">
                <x:String>SUNO</x:String>
                <x:String>IVDT</x:String>
                <x:String>DIVI</x:String>
                <x:String>SINO</x:String>
                <x:String>CUCD</x:String>
                <x:String>TEPY</x:String>
                <x:String>PYME</x:String>
                <x:String>CUAM</x:String>
                <x:String>IMCD</x:String>
                <x:String>CRTP</x:String>
                <x:String>dateformat</x:String>
                <x:String>excludeempty</x:String>
                <x:String>righttrim</x:String>
                <x:String>format</x:String>
                <x:String>extendedresult</x:String>
                <x:String>CORI</x:String>
                <x:String>OYEA</x:String>
                <x:String>DNOI</x:String>
              </scg:List>
              <scg:List x:TypeArguments="x:String" Capacity="32">
                <x:String>vendorId</x:String>
                <x:String>ivdate</x:String>
                <x:String>division</x:String>
                <x:String>temp</x:String>
                <x:String>cucd</x:String>
                <x:String>tepy</x:String>
                <x:String>pyme</x:String>
                <x:String>Amount_DR</x:String>
                <x:String>1</x:String>
                <x:String>1</x:String>
                <x:String>YMD8</x:String>
                <x:String>false</x:String>
                <x:String>true</x:String>
                <x:String>PRETTY</x:String>
                <x:String>false</x:String>
                <x:String>correlationID</x:String>
                <x:String>originalyear</x:String>
                <x:String>sino</x:String>
              </scg:List>
            </scg:List>
          </iai:IONAPIRequestWizard.QueryParameters>
        </iai:IONAPIRequestWizard>
        <If Condition="[StatusCode5 = 200  and respObj5.ReadAsText.Contains(&quot;Bank account identity must be entered&quot;)]" sap2010:WorkflowViewState.IdRef="If_44">
          <If.Then>
            <iai:IONAPIRequestWizard FileAttachments="{x:Null}" PostData="{x:Null}" ResponseCode="{x:Null}" ContentType="application_json" ContinueOnError="True" DisplayName="AddHead_IONAPIRequestWizard_8" HttpMethod="GET" sap2010:WorkflowViewState.IdRef="IONAPIRequestWizard_39" Response="[respObj5]" StatusCode="[StatusCode5]" Url="[TenantID + &quot;M3/m3api-rest/v2/execute/APS450MI/AddHead&quot;]">
              <iai:IONAPIRequestWizard.Headers>
                <scg:List x:TypeArguments="scg:List(x:String)" Capacity="4">
                  <scg:List x:TypeArguments="x:String" Capacity="4">
                    <x:String>Accept</x:String>
                  </scg:List>
                  <scg:List x:TypeArguments="x:String" Capacity="4">
                    <x:String>application/json</x:String>
                  </scg:List>
                </scg:List>
              </iai:IONAPIRequestWizard.Headers>
              <iai:IONAPIRequestWizard.QueryParameters>
                <scg:List x:TypeArguments="scg:List(x:String)" Capacity="4">
                  <scg:List x:TypeArguments="x:String" Capacity="32">
                    <x:String>SUNO</x:String>
                    <x:String>IVDT</x:String>
                    <x:String>DIVI</x:String>
                    <x:String>SINO</x:String>
                    <x:String>CUCD</x:String>
                    <x:String>TEPY</x:String>
                    <x:String>PYME</x:String>
                    <x:String>CUAM</x:String>
                    <x:String>IMCD</x:String>
                    <x:String>CRTP</x:String>
                    <x:String>dateformat</x:String>
                    <x:String>excludeempty</x:String>
                    <x:String>righttrim</x:String>
                    <x:String>format</x:String>
                    <x:String>extendedresult</x:String>
                    <x:String>CORI</x:String>
                    <x:String>OYEA</x:String>
                    <x:String>DNOI</x:String>
                    <x:String>BKID</x:String>
                  </scg:List>
                  <scg:List x:TypeArguments="x:String" Capacity="32">
                    <x:String>vendorId</x:String>
                    <x:String>ivdate</x:String>
                    <x:String>division</x:String>
                    <x:String>temp</x:String>
                    <x:String>cucd</x:String>
                    <x:String>tepy</x:String>
                    <x:String>pyme</x:String>
                    <x:String>Amount_DR</x:String>
                    <x:String>1</x:String>
                    <x:String>1</x:String>
                    <x:String>YMD8</x:String>
                    <x:String>false</x:String>
                    <x:String>true</x:String>
                    <x:String>PRETTY</x:String>
                    <x:String>false</x:String>
                    <x:String>correlationID</x:String>
                    <x:String>originalyear</x:String>
                    <x:String>sino</x:String>
                    <x:String>bkid</x:String>
                  </scg:List>
                </scg:List>
              </iai:IONAPIRequestWizard.QueryParameters>
            </iai:IONAPIRequestWizard>
          </If.Then>
          <If.Else>
            <iad:CommentOut DisplayName="Comment Out" sap2010:WorkflowViewState.IdRef="CommentOut_4">
              <iad:CommentOut.Activities>
                <iai:IONAPIRequestWizard FileAttachments="{x:Null}" PostData="{x:Null}" ResponseCode="{x:Null}" ContentType="application_json" ContinueOnError="True" DisplayName="IONAPI Request for Invoice header" HttpMethod="GET" sap2010:WorkflowViewState.IdRef="IONAPIRequestWizard_10" Response="[respObj5]" StatusCode="[StatusCode5]" Url="[TenantID + &quot;M3/m3api-rest/v2/execute/APS450MI/AddHead&quot;]">
                  <iai:IONAPIRequestWizard.Headers>
                    <scg:List x:TypeArguments="scg:List(x:String)" Capacity="4">
                      <scg:List x:TypeArguments="x:String" Capacity="4">
                        <x:String>Accept</x:String>
                      </scg:List>
                      <scg:List x:TypeArguments="x:String" Capacity="4">
                        <x:String>application/json</x:String>
                      </scg:List>
                    </scg:List>
                  </iai:IONAPIRequestWizard.Headers>
                  <iai:IONAPIRequestWizard.QueryParameters>
                    <scg:List x:TypeArguments="scg:List(x:String)" Capacity="4">
                      <scg:List x:TypeArguments="x:String" Capacity="32">
                        <x:String>SUNO</x:String>
                        <x:String>IVDT</x:String>
                        <x:String>DIVI</x:String>
                        <x:String>SINO</x:String>
                        <x:String>CUCD</x:String>
                        <x:String>TEPY</x:String>
                        <x:String>PYME</x:String>
                        <x:String>CUAM</x:String>
                        <x:String>IMCD</x:String>
                        <x:String>CRTP</x:String>
                        <x:String>dateformat</x:String>
                        <x:String>excludeempty</x:String>
                        <x:String>righttrim</x:String>
                        <x:String>format</x:String>
                        <x:String>extendedresult</x:String>
                        <x:String>APCD</x:String>
                        <x:String>BKID</x:String>
                        <x:String>CORI</x:String>
                        <x:String>DINO</x:String>
                      </scg:List>
                      <scg:List x:TypeArguments="x:String" Capacity="32">
                        <x:String>vendorId</x:String>
                        <x:String>ivdate</x:String>
                        <x:String>division</x:String>
                        <x:String>sino</x:String>
                        <x:String>cucd</x:String>
                        <x:String>tepy</x:String>
                        <x:String>pyme</x:String>
                        <x:String>Amount_DR</x:String>
                        <x:String>1</x:String>
                        <x:String>1</x:String>
                        <x:String>YMD8</x:String>
                        <x:String>false</x:String>
                        <x:String>true</x:String>
                        <x:String>PRETTY</x:String>
                        <x:String>false</x:String>
                        <x:String>authUser</x:String>
                        <x:String>bkid</x:String>
                        <x:String>correlationID</x:String>
                        <x:String>InvoiceNumber_DR</x:String>
                      </scg:List>
                    </scg:List>
                  </iai:IONAPIRequestWizard.QueryParameters>
                </iai:IONAPIRequestWizard>
                <iai:IONAPIRequestWizard FileAttachments="{x:Null}" PostData="{x:Null}" ResponseCode="{x:Null}" ContentType="application_json" ContinueOnError="True" DisplayName="IONAPI Request for Invoice header" HttpMethod="GET" sap2010:WorkflowViewState.IdRef="IONAPIRequestWizard_35" Response="[respObj5]" StatusCode="[StatusCode5]" Url="[TenantID + &quot;M3/m3api-rest/v2/execute/APS450MI/AddHead&quot;]">
                  <iai:IONAPIRequestWizard.Headers>
                    <scg:List x:TypeArguments="scg:List(x:String)" Capacity="4">
                      <scg:List x:TypeArguments="x:String" Capacity="4">
                        <x:String>Accept</x:String>
                      </scg:List>
                      <scg:List x:TypeArguments="x:String" Capacity="4">
                        <x:String>application/json</x:String>
                      </scg:List>
                    </scg:List>
                  </iai:IONAPIRequestWizard.Headers>
                  <iai:IONAPIRequestWizard.QueryParameters>
                    <scg:List x:TypeArguments="scg:List(x:String)" Capacity="4">
                      <scg:List x:TypeArguments="x:String" Capacity="32">
                        <x:String>SUNO</x:String>
                        <x:String>IVDT</x:String>
                        <x:String>DIVI</x:String>
                        <x:String>DINO</x:String>
                        <x:String>CUCD</x:String>
                        <x:String>TEPY</x:String>
                        <x:String>PYME</x:String>
                        <x:String>CUAM</x:String>
                        <x:String>IMCD</x:String>
                        <x:String>CRTP</x:String>
                        <x:String>dateformat</x:String>
                        <x:String>excludeempty</x:String>
                        <x:String>righttrim</x:String>
                        <x:String>format</x:String>
                        <x:String>extendedresult</x:String>
                        <x:String>APCD</x:String>
                        <x:String>CORI</x:String>
                        <x:String>BKID</x:String>
                        <x:String>SINO</x:String>
                      </scg:List>
                      <scg:List x:TypeArguments="x:String" Capacity="32">
                        <x:String>vendorId</x:String>
                        <x:String>ivdate</x:String>
                        <x:String>division</x:String>
                        <x:String>InvoiceNumber_DR</x:String>
                        <x:String>cucd</x:String>
                        <x:String>tepy</x:String>
                        <x:String>pyme</x:String>
                        <x:String>Amount_DR</x:String>
                        <x:String>1</x:String>
                        <x:String>1</x:String>
                        <x:String>YMD8</x:String>
                        <x:String>false</x:String>
                        <x:String>true</x:String>
                        <x:String>PRETTY</x:String>
                        <x:String>false</x:String>
                        <x:String>authUser</x:String>
                        <x:String>correlationID</x:String>
                        <x:String>bkid</x:String>
                        <x:String>sino</x:String>
                      </scg:List>
                    </scg:List>
                  </iai:IONAPIRequestWizard.QueryParameters>
                </iai:IONAPIRequestWizard>
              </iad:CommentOut.Activities>
            </iad:CommentOut>
          </If.Else>
        </If>
        <iad:CommentOut DisplayName="Comment Out" sap2010:WorkflowViewState.IdRef="CommentOut_8">
          <iad:CommentOut.Activities>
            <iai:IONAPIRequestWizard FileAttachments="{x:Null}" PostData="{x:Null}" ResponseCode="{x:Null}" ContentType="application_json" ContinueOnError="True" DisplayName="IONAPI Request for Invoice header" HttpMethod="GET" sap2010:WorkflowViewState.IdRef="IONAPIRequestWizard_37" Response="[respObj5]" StatusCode="[StatusCode5]" Url="[TenantID + &quot;M3/m3api-rest/v2/execute/APS450MI/AddHead&quot;]">
              <iai:IONAPIRequestWizard.Headers>
                <scg:List x:TypeArguments="scg:List(x:String)" Capacity="4">
                  <scg:List x:TypeArguments="x:String" Capacity="4">
                    <x:String>Accept</x:String>
                  </scg:List>
                  <scg:List x:TypeArguments="x:String" Capacity="4">
                    <x:String>application/json</x:String>
                  </scg:List>
                </scg:List>
              </iai:IONAPIRequestWizard.Headers>
              <iai:IONAPIRequestWizard.QueryParameters>
                <scg:List x:TypeArguments="scg:List(x:String)" Capacity="4">
                  <scg:List x:TypeArguments="x:String" Capacity="32">
                    <x:String>SUNO</x:String>
                    <x:String>IVDT</x:String>
                    <x:String>DIVI</x:String>
                    <x:String>DNOI</x:String>
                    <x:String>CUCD</x:String>
                    <x:String>TEPY</x:String>
                    <x:String>PYME</x:String>
                    <x:String>CUAM</x:String>
                    <x:String>IMCD</x:String>
                    <x:String>CRTP</x:String>
                    <x:String>dateformat</x:String>
                    <x:String>excludeempty</x:String>
                    <x:String>righttrim</x:String>
                    <x:String>format</x:String>
                    <x:String>extendedresult</x:String>
                    <x:String>APCD</x:String>
                    <x:String>CORI</x:String>
                    <x:String>SINO</x:String>
                    <x:String>BKID</x:String>
                  </scg:List>
                  <scg:List x:TypeArguments="x:String" Capacity="32">
                    <x:String>vendorId</x:String>
                    <x:String>ivdate</x:String>
                    <x:String>division</x:String>
                    <x:String>sino</x:String>
                    <x:String>cucd</x:String>
                    <x:String>tepy</x:String>
                    <x:String>pyme</x:String>
                    <x:String>Amount_DR</x:String>
                    <x:String>0</x:String>
                    <x:String>1</x:String>
                    <x:String>YMD8</x:String>
                    <x:String>false</x:String>
                    <x:String>true</x:String>
                    <x:String>PRETTY</x:String>
                    <x:String>false</x:String>
                    <x:String>authUser</x:String>
                    <x:String>correlationID</x:String>
                    <x:String>InvoiceNumber_DR</x:String>
                    <x:String>bkid</x:String>
                  </scg:List>
                </scg:List>
              </iai:IONAPIRequestWizard.QueryParameters>
            </iai:IONAPIRequestWizard>
          </iad:CommentOut.Activities>
        </iad:CommentOut>
        <ias:MessageBox ButtonSelection="{x:Null}" ErrorCode="{x:Null}" ContinueOnError="True" DisplayName="Message Box" sap2010:WorkflowViewState.IdRef="MessageBox_39" Selection="OK" Text="[respObj5.ReadAsText.ToString]" Title="header output" />
        <ias:MessageBox ButtonSelection="{x:Null}" ErrorCode="{x:Null}" ContinueOnError="True" DisplayName="Message Box" sap2010:WorkflowViewState.IdRef="MessageBox_40" Selection="OK" Text="[StatusCode5.ToString]" Title="StatusCode5" />
        <If Condition="[StatusCode5 = 200]" sap2010:WorkflowViewState.IdRef="If_126">
          <If.Then>
            <Sequence sap2010:WorkflowViewState.IdRef="Sequence_167">
              <Sequence.Variables>
                <Variable x:TypeArguments="njl:JToken" Name="out5" />
              </Sequence.Variables>
              <Assign sap2010:WorkflowViewState.IdRef="Assign_405">
                <Assign.To>
                  <OutArgument x:TypeArguments="njl:JToken">[out5]</OutArgument>
                </Assign.To>
                <Assign.Value>
                  <InArgument x:TypeArguments="njl:JToken">[JToken.Parse(respObj5.ReadAsText)]</InArgument>
                </Assign.Value>
              </Assign>
              <ias:MessageBox ButtonSelection="{x:Null}" ErrorCode="{x:Null}" ContinueOnError="True" DisplayName="Message Box" sap2010:WorkflowViewState.IdRef="MessageBox_36" Selection="OK" Text="[out5.ToString]" Title="out5" />
              <If Condition="[out5(&quot;results&quot;)(0)(&quot;records&quot;).ToString = &quot;[]&quot;]" DisplayName="AddHead_If_14" sap2010:WorkflowViewState.IdRef="If_140">
                <If.Then>
                  <Sequence DisplayName="AddHead_Sequence_15" sap2010:WorkflowViewState.IdRef="Sequence_192">
                    <Assign DisplayName="AddHead_Assign_17_inbnValue" sap2010:WorkflowViewState.IdRef="Assign_407">
                      <Assign.To>
                        <OutArgument x:TypeArguments="x:String">[inbnValue]</OutArgument>
                      </Assign.To>
                      <Assign.Value>
                        <InArgument x:TypeArguments="x:String">
                          <Literal x:TypeArguments="x:String" Value="" />
                        </InArgument>
                      </Assign.Value>
                    </Assign>
                    <Assign DisplayName="AddHead_Assign_18_commentStatus" sap2010:WorkflowViewState.IdRef="Assign_408">
                      <Assign.To>
                        <OutArgument x:TypeArguments="x:String">[commentStatus]</OutArgument>
                      </Assign.To>
                      <Assign.Value>
                        <InArgument x:TypeArguments="x:String">[(out5("results")(0)("errorMessage")).ToString]</InArgument>
                      </Assign.Value>
                    </Assign>
                    <If Condition="[respObj5.ReadAsText.Contains(&quot;Bank account identity must be entered&quot;)]" DisplayName="AddHead_If_19" sap2010:WorkflowViewState.IdRef="If_139">
                      <If.Then>
                        <Assign DisplayName="AddHead_Assign_20_Status" sap2010:WorkflowViewState.IdRef="Assign_409">
                          <Assign.To>
                            <OutArgument x:TypeArguments="x:String">[Status]</OutArgument>
                          </Assign.To>
                          <Assign.Value>
                            <InArgument x:TypeArguments="x:String">NEEDSVERIFICATION</InArgument>
                          </Assign.Value>
                        </Assign>
                      </If.Then>
                      <If.Else>
                        <Assign DisplayName="AddHead_Assign_21_Status" sap2010:WorkflowViewState.IdRef="Assign_410">
                          <Assign.To>
                            <OutArgument x:TypeArguments="x:String">[Status]</OutArgument>
                          </Assign.To>
                          <Assign.Value>
                            <InArgument x:TypeArguments="x:String">FAILURE</InArgument>
                          </Assign.Value>
                        </Assign>
                      </If.Else>
                    </If>
                  </Sequence>
                </If.Then>
                <If.Else>
                  <Sequence DisplayName="AddHead_Sequence_22" sap2010:WorkflowViewState.IdRef="Sequence_193">
                    <Assign DisplayName="AddHead_Assign_24_inbnValue" sap2010:WorkflowViewState.IdRef="Assign_412">
                      <Assign.To>
                        <OutArgument x:TypeArguments="x:String">[inbnValue]</OutArgument>
                      </Assign.To>
                      <Assign.Value>
                        <InArgument x:TypeArguments="x:String">[(out5("results")(0)("records")(0)("INBN")).ToString]</InArgument>
                      </Assign.Value>
                    </Assign>
                    <Assign DisplayName="AddHead_Assign_25_commentStatus" sap2010:WorkflowViewState.IdRef="Assign_413">
                      <Assign.To>
                        <OutArgument x:TypeArguments="x:String">[commentStatus]</OutArgument>
                      </Assign.To>
                      <Assign.Value>
                        <InArgument x:TypeArguments="x:String">["Debit Note Header Created"]</InArgument>
                      </Assign.Value>
                    </Assign>
                  </Sequence>
                </If.Else>
              </If>
            </Sequence>
          </If.Then>
          <If.Else>
            <Assign sap2010:WorkflowViewState.IdRef="Assign_345">
              <Assign.To>
                <OutArgument x:TypeArguments="x:String">[inbnValue]</OutArgument>
              </Assign.To>
              <Assign.Value>
                <InArgument x:TypeArguments="x:String">
                  <Literal x:TypeArguments="x:String" Value="" />
                </InArgument>
              </Assign.Value>
            </Assign>
          </If.Else>
        </If>
      </Sequence>
      <ias:MessageBox ButtonSelection="{x:Null}" ErrorCode="{x:Null}" ContinueOnError="True" DisplayName="Message Box" sap2010:WorkflowViewState.IdRef="MessageBox_37" Selection="OK" Text="[inbnValue]" Title="inbnValue" />
      <If Condition="[inbnValue=&quot;&quot;]" sap2010:WorkflowViewState.IdRef="If_134">
        <If.Then>
          <Sequence DisplayName="INBN Empty" sap2010:WorkflowViewState.IdRef="Sequence_181">
            <Assign sap2010:WorkflowViewState.IdRef="Assign_366">
              <Assign.To>
                <OutArgument x:TypeArguments="x:String">[commentStatus]</OutArgument>
              </Assign.To>
              <Assign.Value>
                <InArgument x:TypeArguments="x:String" xml:space="preserve">["Error occured while fetching the INBN value from Adding the DebitNote Header  "]</InArgument>
              </Assign.Value>
            </Assign>
            <Assign sap2010:WorkflowViewState.IdRef="Assign_367">
              <Assign.To>
                <OutArgument x:TypeArguments="x:String">[Status]</OutArgument>
              </Assign.To>
              <Assign.Value>
                <InArgument x:TypeArguments="x:String">FAILURE</InArgument>
              </Assign.Value>
            </Assign>
            <ias:Append_Line ErrorCode="{x:Null}" ContinueOnError="True" DisplayName="Append Line" sap2010:WorkflowViewState.IdRef="Append_Line_16" Line="[commentStatus]" Source="[logfile]" />
          </Sequence>
        </If.Then>
        <If.Else>
          <Sequence DisplayName="INBN Not Empty" sap2010:WorkflowViewState.IdRef="Sequence_182">
            <If Condition="[str_AcceptWithDebitNote=&quot;True&quot;]" DisplayName="If 'AcceptWithDebitNote'" sap2010:WorkflowViewState.IdRef="If_124">
              <If.Then>
                <Sequence DisplayName="Adding Lines wth -ve amount" sap2010:WorkflowViewState.IdRef="Sequence_80">
                  <ForEach x:TypeArguments="scg:Dictionary(x:String, x:Object)" DisplayName="ForEach&lt;Dictionary&lt;String,Object&gt;&gt;" sap2010:WorkflowViewState.IdRef="ForEach`1_5" Values="[ListocrLineValues]">
                    <ActivityAction x:TypeArguments="scg:Dictionary(x:String, x:Object)">
                      <ActivityAction.Argument>
                        <DelegateInArgument x:TypeArguments="scg:Dictionary(x:String, x:Object)" Name="item" />
                      </ActivityAction.Argument>
                      <Sequence sap2010:WorkflowViewState.IdRef="Sequence_79">
                        <Sequence.Variables>
                          <Variable x:TypeArguments="iru:ResponseObject" Name="respObj7" />
                          <Variable x:TypeArguments="x:Int32" Name="StatusCode7" />
                          <Variable x:TypeArguments="njl:JToken" Name="out7" />
                        </Sequence.Variables>
                        <If Condition="[item(&quot;DESCRIPTION&quot;).ToString.Split(&quot;~&quot;c).Length - 1=6]" sap2010:WorkflowViewState.IdRef="If_137">
                          <If.Then>
                            <Sequence sap2010:WorkflowViewState.IdRef="Sequence_78">
                              <Sequence.Variables>
                                <Variable x:TypeArguments="scg:Dictionary(x:String, x:Object)" Name="LinesDict" />
                                <Variable x:TypeArguments="x:String" Name="AIT1" />
                                <Variable x:TypeArguments="x:String" Name="AIT2" />
                                <Variable x:TypeArguments="x:String" Name="AIT3" />
                                <Variable x:TypeArguments="x:String" Name="AIT4" />
                                <Variable x:TypeArguments="x:String" Name="AIT5" />
                                <Variable x:TypeArguments="x:String" Name="AIT6" />
                                <Variable x:TypeArguments="x:String" Name="AIT7" />
                              </Sequence.Variables>
                              <Assign sap2010:WorkflowViewState.IdRef="Assign_101">
                                <Assign.To>
                                  <OutArgument x:TypeArguments="scg:Dictionary(x:String, x:Object)">[LinesDict]</OutArgument>
                                </Assign.To>
                                <Assign.Value>
                                  <InArgument x:TypeArguments="scg:Dictionary(x:String, x:Object)">[New Dictionary (Of String, Object)]</InArgument>
                                </Assign.Value>
                              </Assign>
                              <Assign sap2010:WorkflowViewState.IdRef="Assign_102">
                                <Assign.To>
                                  <OutArgument x:TypeArguments="scg:Dictionary(x:String, x:Object)">[LinesDict]</OutArgument>
                                </Assign.To>
                                <Assign.Value>
                                  <InArgument x:TypeArguments="scg:Dictionary(x:String, x:Object)">[item]</InArgument>
                                </Assign.Value>
                              </Assign>
                              <Assign sap2010:WorkflowViewState.IdRef="Assign_103">
                                <Assign.To>
                                  <OutArgument x:TypeArguments="x:String">[AIT1]</OutArgument>
                                </Assign.To>
                                <Assign.Value>
                                  <InArgument x:TypeArguments="x:String">[LinesDict("DESCRIPTION").ToString.Split("~"c)(0)]</InArgument>
                                </Assign.Value>
                              </Assign>
                              <Assign sap2010:WorkflowViewState.IdRef="Assign_104">
                                <Assign.To>
                                  <OutArgument x:TypeArguments="x:String">[AIT2]</OutArgument>
                                </Assign.To>
                                <Assign.Value>
                                  <InArgument x:TypeArguments="x:String">[LinesDict("DESCRIPTION").ToString.Split("~"c)(1)]</InArgument>
                                </Assign.Value>
                              </Assign>
                              <Assign sap2010:WorkflowViewState.IdRef="Assign_105">
                                <Assign.To>
                                  <OutArgument x:TypeArguments="x:String">[AIT3]</OutArgument>
                                </Assign.To>
                                <Assign.Value>
                                  <InArgument x:TypeArguments="x:String">[LinesDict("DESCRIPTION").ToString.Split("~"c)(2)]</InArgument>
                                </Assign.Value>
                              </Assign>
                              <Assign sap2010:WorkflowViewState.IdRef="Assign_106">
                                <Assign.To>
                                  <OutArgument x:TypeArguments="x:String">[AIT4]</OutArgument>
                                </Assign.To>
                                <Assign.Value>
                                  <InArgument x:TypeArguments="x:String">[LinesDict("DESCRIPTION").ToString.Split("~"c)(3)]</InArgument>
                                </Assign.Value>
                              </Assign>
                              <Assign sap2010:WorkflowViewState.IdRef="Assign_107">
                                <Assign.To>
                                  <OutArgument x:TypeArguments="x:String">[AIT5]</OutArgument>
                                </Assign.To>
                                <Assign.Value>
                                  <InArgument x:TypeArguments="x:String">[LinesDict("DESCRIPTION").ToString.Split("~"c)(4)]</InArgument>
                                </Assign.Value>
                              </Assign>
                              <Assign sap2010:WorkflowViewState.IdRef="Assign_108">
                                <Assign.To>
                                  <OutArgument x:TypeArguments="x:String">[AIT6]</OutArgument>
                                </Assign.To>
                                <Assign.Value>
                                  <InArgument x:TypeArguments="x:String">[LinesDict("DESCRIPTION").ToString.Split("~"c)(5)]</InArgument>
                                </Assign.Value>
                              </Assign>
                              <Assign sap2010:WorkflowViewState.IdRef="Assign_109">
                                <Assign.To>
                                  <OutArgument x:TypeArguments="x:String">[AIT7]</OutArgument>
                                </Assign.To>
                                <Assign.Value>
                                  <InArgument x:TypeArguments="x:String">[LinesDict("DESCRIPTION").ToString.Split("~"c)(6)]</InArgument>
                                </Assign.Value>
                              </Assign>
                              <If Condition="[LinesDict(&quot;LINE_AMOUNT&quot;).ToString.contains(&quot;-&quot;)]" sap2010:WorkflowViewState.IdRef="If_141">
                                <If.Then>
                                  <Assign sap2010:WorkflowViewState.IdRef="Assign_414">
                                    <Assign.To>
                                      <OutArgument x:TypeArguments="x:String">[amt]</OutArgument>
                                    </Assign.To>
                                    <Assign.Value>
                                      <InArgument x:TypeArguments="x:String">[LinesDict("LINE_AMOUNT").ToString]</InArgument>
                                    </Assign.Value>
                                  </Assign>
                                </If.Then>
                                <If.Else>
                                  <Assign sap2010:WorkflowViewState.IdRef="Assign_110">
                                    <Assign.To>
                                      <OutArgument x:TypeArguments="x:String">[amt]</OutArgument>
                                    </Assign.To>
                                    <Assign.Value>
                                      <InArgument x:TypeArguments="x:String">["-"+LinesDict("LINE_AMOUNT").ToString]</InArgument>
                                    </Assign.Value>
                                  </Assign>
                                </If.Else>
                              </If>
                              <iai:IONAPIRequestWizard FileAttachments="{x:Null}" PostData="{x:Null}" ResponseCode="{x:Null}" ContentType="application_json" ContinueOnError="True" DisplayName="IONAPI Request for Adding lines" HttpMethod="GET" sap2010:WorkflowViewState.IdRef="IONAPIRequestWizard_15" Response="[respObj2]" StatusCode="[StatusCode2]" Url="[TenantID+ &quot;M3/m3api-rest/v2/execute/APS450MI/AddLine&quot;]">
                                <iai:IONAPIRequestWizard.Headers>
                                  <scg:List x:TypeArguments="scg:List(x:String)" Capacity="4">
                                    <scg:List x:TypeArguments="x:String" Capacity="4">
                                      <x:String>Accept</x:String>
                                    </scg:List>
                                    <scg:List x:TypeArguments="x:String" Capacity="4">
                                      <x:String>application/json</x:String>
                                    </scg:List>
                                  </scg:List>
                                </iai:IONAPIRequestWizard.Headers>
                                <iai:IONAPIRequestWizard.QueryParameters>
                                  <scg:List x:TypeArguments="scg:List(x:String)" Capacity="4">
                                    <scg:List x:TypeArguments="x:String" Capacity="16">
                                      <x:String>INBN</x:String>
                                      <x:String>RDTP</x:String>
                                      <x:String>DIVI</x:String>
                                      <x:String>NLAM</x:String>
                                      <x:String>AIT1</x:String>
                                      <x:String>AIT2</x:String>
                                      <x:String>AIT3</x:String>
                                      <x:String>AIT4</x:String>
                                      <x:String>AIT5</x:String>
                                      <x:String>AIT6</x:String>
                                      <x:String>AIT7</x:String>
                                    </scg:List>
                                    <scg:List x:TypeArguments="x:String" Capacity="16">
                                      <x:String>inbnValue</x:String>
                                      <x:String>8</x:String>
                                      <x:String>division</x:String>
                                      <x:String>amt</x:String>
                                      <x:String>AIT1</x:String>
                                      <x:String>AIT2</x:String>
                                      <x:String>AIT3</x:String>
                                      <x:String>AIT4</x:String>
                                      <x:String>AIT5</x:String>
                                      <x:String>AIT6</x:String>
                                      <x:String>AIT7</x:String>
                                    </scg:List>
                                  </scg:List>
                                </iai:IONAPIRequestWizard.QueryParameters>
                              </iai:IONAPIRequestWizard>
                              <Assign sap2010:WorkflowViewState.IdRef="Assign_112">
                                <Assign.To>
                                  <OutArgument x:TypeArguments="njl:JToken">[out1]</OutArgument>
                                </Assign.To>
                                <Assign.Value>
                                  <InArgument x:TypeArguments="njl:JToken">[JToken.Parse(respObj2.ReadAsText)]</InArgument>
                                </Assign.Value>
                              </Assign>
                              <If Condition="[out1(&quot;results&quot;)(0)(&quot;records&quot;).ToString = &quot;[]&quot;]" sap2010:WorkflowViewState.IdRef="If_53">
                                <If.Then>
                                  <Sequence sap2010:WorkflowViewState.IdRef="Sequence_76">
                                    <Assign sap2010:WorkflowViewState.IdRef="Assign_113">
                                      <Assign.To>
                                        <OutArgument x:TypeArguments="x:String">[commentStatus]</OutArgument>
                                      </Assign.To>
                                      <Assign.Value>
                                        <InArgument x:TypeArguments="x:String" xml:space="preserve">["Error occured while adding the debit line.  "  +  (out1("results")(0)("errorMessage")).ToString]</InArgument>
                                      </Assign.Value>
                                    </Assign>
                                    <Assign sap2010:WorkflowViewState.IdRef="Assign_114">
                                      <Assign.To>
                                        <OutArgument x:TypeArguments="x:String">[Status]</OutArgument>
                                      </Assign.To>
                                      <Assign.Value>
                                        <InArgument x:TypeArguments="x:String">FAILURE</InArgument>
                                      </Assign.Value>
                                    </Assign>
                                    <ias:Append_Line ErrorCode="{x:Null}" ContinueOnError="True" DisplayName="Append Line" sap2010:WorkflowViewState.IdRef="Append_Line_11" Line="[commentStatus]" Source="[logfile]" />
                                  </Sequence>
                                </If.Then>
                                <If.Else>
                                  <Sequence sap2010:WorkflowViewState.IdRef="Sequence_77">
                                    <Assign sap2010:WorkflowViewState.IdRef="Assign_115">
                                      <Assign.To>
                                        <OutArgument x:TypeArguments="x:String">[commentStatus]</OutArgument>
                                      </Assign.To>
                                      <Assign.Value>
                                        <InArgument x:TypeArguments="x:String">["Debit Line created"]</InArgument>
                                      </Assign.Value>
                                    </Assign>
                                    <Assign sap2010:WorkflowViewState.IdRef="Assign_116">
                                      <Assign.To>
                                        <OutArgument x:TypeArguments="x:String">[Status]</OutArgument>
                                      </Assign.To>
                                      <Assign.Value>
                                        <InArgument x:TypeArguments="x:String">["SUCCESS"]</InArgument>
                                      </Assign.Value>
                                    </Assign>
                                    <ias:Append_Line ErrorCode="{x:Null}" ContinueOnError="True" DisplayName="Append Line" sap2010:WorkflowViewState.IdRef="Append_Line_12" Line="[commentStatus]" Source="[logfile]" />
                                  </Sequence>
                                </If.Else>
                              </If>
                            </Sequence>
                          </If.Then>
                        </If>
                      </Sequence>
                    </ActivityAction>
                  </ForEach>
                </Sequence>
              </If.Then>
            </If>
            <If Condition="[str_PendingProcess=&quot;True&quot;]" DisplayName="If 'PendingProcess'" sap2010:WorkflowViewState.IdRef="If_125">
              <If.Then>
                <Sequence sap2010:WorkflowViewState.IdRef="Sequence_162">
                  <iaw:InvokeWorkflow Arguments="[New Dictionary(Of String, Object) From {{&quot;businessRule&quot;,&quot;DivisiontoGLCode&quot;},{&quot;tenantID&quot;,tenantID},{&quot;division&quot;, &quot;division&quot;},{&quot;vendorId&quot;, &quot;vendorId&quot;}}]" ContinueOnError="True" DisplayName="Call DivisiontoGLcode xaml" sap2010:WorkflowViewState.IdRef="InvokeWorkflow_9" OutputArguments="[DivisiontoGLcodeWorkflowOutput]" ResponseCode="[DivisiontoGLcodeWorkflowStatus]" WorkflowFile="[projectPath+&quot;\DivisiontoGLCode.xaml&quot;]" />
                  <Sequence DisplayName="Add Lines" sap2010:WorkflowViewState.IdRef="Sequence_164">
                    <Sequence.Variables>
                      <Variable x:TypeArguments="x:String" Name="AIT1" />
                      <Variable x:TypeArguments="x:String" Name="AIT2" />
                      <Variable x:TypeArguments="x:String" Name="AIT3" />
                      <Variable x:TypeArguments="x:String" Name="AIT4" />
                      <Variable x:TypeArguments="x:String" Name="AIT5" />
                      <Variable x:TypeArguments="x:String" Name="AIT6" />
                      <Variable x:TypeArguments="x:String" Name="AIT7" />
                    </Sequence.Variables>
                    <Assign sap2010:WorkflowViewState.IdRef="Assign_341">
                      <Assign.To>
                        <OutArgument x:TypeArguments="x:String">[AIT1]</OutArgument>
                      </Assign.To>
                      <Assign.Value>
                        <InArgument x:TypeArguments="x:String">[DivisiontoGLcodeWorkflowOutput("AIT1").tostring]</InArgument>
                      </Assign.Value>
                    </Assign>
                    <Assign sap2010:WorkflowViewState.IdRef="Assign_340">
                      <Assign.To>
                        <OutArgument x:TypeArguments="x:String">[AIT2]</OutArgument>
                      </Assign.To>
                      <Assign.Value>
                        <InArgument x:TypeArguments="x:String">[DivisiontoGLcodeWorkflowOutput("AIT2").tostring]</InArgument>
                      </Assign.Value>
                    </Assign>
                    <Assign sap2010:WorkflowViewState.IdRef="Assign_339">
                      <Assign.To>
                        <OutArgument x:TypeArguments="x:String">[AIT3]</OutArgument>
                      </Assign.To>
                      <Assign.Value>
                        <InArgument x:TypeArguments="x:String">[DivisiontoGLcodeWorkflowOutput("AIT3").tostring]</InArgument>
                      </Assign.Value>
                    </Assign>
                    <Assign sap2010:WorkflowViewState.IdRef="Assign_338">
                      <Assign.To>
                        <OutArgument x:TypeArguments="x:String">[AIT4]</OutArgument>
                      </Assign.To>
                      <Assign.Value>
                        <InArgument x:TypeArguments="x:String">[DivisiontoGLcodeWorkflowOutput("AIT4").tostring]</InArgument>
                      </Assign.Value>
                    </Assign>
                    <Assign sap2010:WorkflowViewState.IdRef="Assign_337">
                      <Assign.To>
                        <OutArgument x:TypeArguments="x:String">[AIT5]</OutArgument>
                      </Assign.To>
                      <Assign.Value>
                        <InArgument x:TypeArguments="x:String">[DivisiontoGLcodeWorkflowOutput("AIT5").tostring]</InArgument>
                      </Assign.Value>
                    </Assign>
                    <Assign sap2010:WorkflowViewState.IdRef="Assign_336">
                      <Assign.To>
                        <OutArgument x:TypeArguments="x:String">[AIT6]</OutArgument>
                      </Assign.To>
                      <Assign.Value>
                        <InArgument x:TypeArguments="x:String">[DivisiontoGLcodeWorkflowOutput("AIT6").tostring]</InArgument>
                      </Assign.Value>
                    </Assign>
                    <Assign sap2010:WorkflowViewState.IdRef="Assign_342">
                      <Assign.To>
                        <OutArgument x:TypeArguments="x:String">[AIT7]</OutArgument>
                      </Assign.To>
                      <Assign.Value>
                        <InArgument x:TypeArguments="x:String">[DivisiontoGLcodeWorkflowOutput("AIT7").tostring]</InArgument>
                      </Assign.Value>
                    </Assign>
                    <iai:IONAPIRequestWizard FileAttachments="{x:Null}" PostData="{x:Null}" ResponseCode="{x:Null}" ContentType="application_json" ContinueOnError="True" DisplayName="IONAPI Request for Adding lines" HttpMethod="GET" sap2010:WorkflowViewState.IdRef="IONAPIRequestWizard_32" Response="[respObj2]" StatusCode="[StatusCode2]" Url="[TenantID+ &quot;M3/m3api-rest/v2/execute/APS450MI/AddLine&quot;]">
                      <iai:IONAPIRequestWizard.Headers>
                        <scg:List x:TypeArguments="scg:List(x:String)" Capacity="4">
                          <scg:List x:TypeArguments="x:String" Capacity="4">
                            <x:String>Accept</x:String>
                          </scg:List>
                          <scg:List x:TypeArguments="x:String" Capacity="4">
                            <x:String>application/json</x:String>
                          </scg:List>
                        </scg:List>
                      </iai:IONAPIRequestWizard.Headers>
                      <iai:IONAPIRequestWizard.QueryParameters>
                        <scg:List x:TypeArguments="scg:List(x:String)" Capacity="4">
                          <scg:List x:TypeArguments="x:String" Capacity="16">
                            <x:String>INBN</x:String>
                            <x:String>RDTP</x:String>
                            <x:String>DIVI</x:String>
                            <x:String>NLAM</x:String>
                            <x:String>AIT1</x:String>
                            <x:String>AIT2</x:String>
                            <x:String>AIT3</x:String>
                            <x:String>AIT4</x:String>
                            <x:String>AIT5</x:String>
                            <x:String>AIT6</x:String>
                            <x:String>AIT7</x:String>
                          </scg:List>
                          <scg:List x:TypeArguments="x:String" Capacity="16">
                            <x:String>inbnValue</x:String>
                            <x:String>8</x:String>
                            <x:String>division</x:String>
                            <x:String>amt</x:String>
                            <x:String>AIT1</x:String>
                            <x:String>AIT2</x:String>
                            <x:String>AIT3</x:String>
                            <x:String>AIT4</x:String>
                            <x:String>AIT5</x:String>
                            <x:String>AIT6</x:String>
                            <x:String>AIT7</x:String>
                          </scg:List>
                        </scg:List>
                      </iai:IONAPIRequestWizard.QueryParameters>
                    </iai:IONAPIRequestWizard>
                    <Assign sap2010:WorkflowViewState.IdRef="Assign_359">
                      <Assign.To>
                        <OutArgument x:TypeArguments="njl:JToken">[out1]</OutArgument>
                      </Assign.To>
                      <Assign.Value>
                        <InArgument x:TypeArguments="njl:JToken">[JToken.Parse(respObj2.ReadAsText)]</InArgument>
                      </Assign.Value>
                    </Assign>
                    <If Condition="[out1(&quot;results&quot;)(0)(&quot;records&quot;).ToString = &quot;[]&quot;]" sap2010:WorkflowViewState.IdRef="If_133">
                      <If.Then>
                        <Sequence sap2010:WorkflowViewState.IdRef="Sequence_178">
                          <Assign sap2010:WorkflowViewState.IdRef="Assign_360">
                            <Assign.To>
                              <OutArgument x:TypeArguments="x:String">[commentStatus]</OutArgument>
                            </Assign.To>
                            <Assign.Value>
                              <InArgument x:TypeArguments="x:String" xml:space="preserve">["Error occured while adding the debit line.  "  +  (out1("results")(0)("errorMessage")).ToString]</InArgument>
                            </Assign.Value>
                          </Assign>
                          <Assign sap2010:WorkflowViewState.IdRef="Assign_361">
                            <Assign.To>
                              <OutArgument x:TypeArguments="x:String">[Status]</OutArgument>
                            </Assign.To>
                            <Assign.Value>
                              <InArgument x:TypeArguments="x:String">FAILURE</InArgument>
                            </Assign.Value>
                          </Assign>
                          <ias:Append_Line ErrorCode="{x:Null}" ContinueOnError="True" DisplayName="Append Line" sap2010:WorkflowViewState.IdRef="Append_Line_13" Line="[commentStatus]" Source="[logfile]" />
                        </Sequence>
                      </If.Then>
                      <If.Else>
                        <Sequence sap2010:WorkflowViewState.IdRef="Sequence_179">
                          <Assign sap2010:WorkflowViewState.IdRef="Assign_362">
                            <Assign.To>
                              <OutArgument x:TypeArguments="x:String">[commentStatus]</OutArgument>
                            </Assign.To>
                            <Assign.Value>
                              <InArgument x:TypeArguments="x:String">["Debit Line created"]</InArgument>
                            </Assign.Value>
                          </Assign>
                          <Assign sap2010:WorkflowViewState.IdRef="Assign_363">
                            <Assign.To>
                              <OutArgument x:TypeArguments="x:String">[Status]</OutArgument>
                            </Assign.To>
                            <Assign.Value>
                              <InArgument x:TypeArguments="x:String">["SUCCESS"]</InArgument>
                            </Assign.Value>
                          </Assign>
                          <ias:Append_Line ErrorCode="{x:Null}" ContinueOnError="True" DisplayName="Append Line" sap2010:WorkflowViewState.IdRef="Append_Line_14" Line="[commentStatus]" Source="[logfile]" />
                        </Sequence>
                      </If.Else>
                    </If>
                  </Sequence>
                </Sequence>
              </If.Then>
            </If>
          </Sequence>
        </If.Else>
      </If>
    </Sequence>
    <sads:DebugSymbol.Symbol>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</sads:DebugSymbol.Symbol>
  </Sequence>
  <sap2010:WorkflowViewState.ViewStateManager>
    <sap2010:ViewStateManager>
      <sap2010:ViewStateData Id="Assign_343" sap:VirtualizedContainerService.HintSize="516.666666666667,62" />
      <sap2010:ViewStateData Id="Assign_334" sap:VirtualizedContainerService.HintSize="516.666666666667,62" />
      <sap2010:ViewStateData Id="Assign_400" sap:VirtualizedContainerService.HintSize="516.666666666667,61.3333333333333" />
      <sap2010:ViewStateData Id="Assign_398" sap:VirtualizedContainerService.HintSize="242,61.3333333333333" />
      <sap2010:ViewStateData Id="If_138" sap:VirtualizedContainerService.HintSize="464,213.333333333333">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsPinned">True</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="Sequence_191" sap:VirtualizedContainerService.HintSize="486,337.333333333333">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">True</x:Boolean>
            <x:Boolean x:Key="IsPinned">True</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="ForEach`1_6" sap:VirtualizedContainerService.HintSize="516.666666666667,488.666666666667">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">True</x:Boolean>
            <x:Boolean x:Key="IsPinned">True</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="Sequence_165" sap:VirtualizedContainerService.HintSize="1134,52">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">False</x:Boolean>
            <x:Boolean x:Key="IsPinned">False</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="Assign_99" sap:VirtualizedContainerService.HintSize="1112,61.3333333333333" />
      <sap2010:ViewStateData Id="Assign_402" sap:VirtualizedContainerService.HintSize="1112,61.3333333333333" />
      <sap2010:ViewStateData Id="MessageBox_33" sap:VirtualizedContainerService.HintSize="532,22" />
      <sap2010:ViewStateData Id="CommentOut_6" sap:VirtualizedContainerService.HintSize="1112,58" />
      <sap2010:ViewStateData Id="Assign_401" sap:VirtualizedContainerService.HintSize="1112,61.3333333333333" />
      <sap2010:ViewStateData Id="Assign_100" sap:VirtualizedContainerService.HintSize="1112,61.3333333333333" />
      <sap2010:ViewStateData Id="IONAPIRequestWizard_38" sap:VirtualizedContainerService.HintSize="1112,22" />
      <sap2010:ViewStateData Id="IONAPIRequestWizard_39" sap:VirtualizedContainerService.HintSize="200,22" />
      <sap2010:ViewStateData Id="IONAPIRequestWizard_10" sap:VirtualizedContainerService.HintSize="200,22" />
      <sap2010:ViewStateData Id="IONAPIRequestWizard_35" sap:VirtualizedContainerService.HintSize="200,22" />
      <sap2010:ViewStateData Id="CommentOut_4" sap:VirtualizedContainerService.HintSize="192.666666666667,58">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsPinned">False</x:Boolean>
            <x:Boolean x:Key="IsExpanded">False</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="If_44" sap:VirtualizedContainerService.HintSize="1112,212">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsPinned">True</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="IONAPIRequestWizard_37" sap:VirtualizedContainerService.HintSize="1112,22" />
      <sap2010:ViewStateData Id="CommentOut_8" sap:VirtualizedContainerService.HintSize="1112,58" />
      <sap2010:ViewStateData Id="MessageBox_39" sap:VirtualizedContainerService.HintSize="1112,22" />
      <sap2010:ViewStateData Id="MessageBox_40" sap:VirtualizedContainerService.HintSize="1112,22" />
      <sap2010:ViewStateData Id="Assign_405" sap:VirtualizedContainerService.HintSize="822,61.3333333333333" />
      <sap2010:ViewStateData Id="MessageBox_36" sap:VirtualizedContainerService.HintSize="822,22" />
      <sap2010:ViewStateData Id="Assign_407" sap:VirtualizedContainerService.HintSize="510,61.3333333333333" />
      <sap2010:ViewStateData Id="Assign_408" sap:VirtualizedContainerService.HintSize="510,61.3333333333333" />
      <sap2010:ViewStateData Id="Assign_409" sap:VirtualizedContainerService.HintSize="242,61.3333333333333" />
      <sap2010:ViewStateData Id="Assign_410" sap:VirtualizedContainerService.HintSize="242,61.3333333333333" />
      <sap2010:ViewStateData Id="If_139" sap:VirtualizedContainerService.HintSize="510,213.333333333333">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsPinned">True</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="Sequence_192" sap:VirtualizedContainerService.HintSize="532,540">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">True</x:Boolean>
            <x:Boolean x:Key="IsPinned">True</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="Assign_412" sap:VirtualizedContainerService.HintSize="242,61.3333333333333" />
      <sap2010:ViewStateData Id="Assign_413" sap:VirtualizedContainerService.HintSize="242,61.3333333333333" />
      <sap2010:ViewStateData Id="Sequence_193" sap:VirtualizedContainerService.HintSize="264,286.666666666667">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">True</x:Boolean>
            <x:Boolean x:Key="IsPinned">True</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="If_140" sap:VirtualizedContainerService.HintSize="822,692">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsPinned">True</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="Sequence_167" sap:VirtualizedContainerService.HintSize="844,979.333333333333">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">True</x:Boolean>
            <x:Boolean x:Key="IsPinned">True</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="Assign_345" sap:VirtualizedContainerService.HintSize="242,61.3333333333333" />
      <sap2010:ViewStateData Id="If_126" sap:VirtualizedContainerService.HintSize="1112,1131.33333333333">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsPinned">True</x:Boolean>
            <x:Boolean x:Key="IsExpanded">True</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="Sequence_59" sap:VirtualizedContainerService.HintSize="1134,2294.66666666667">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">True</x:Boolean>
            <x:Boolean x:Key="IsPinned">True</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="MessageBox_37" sap:VirtualizedContainerService.HintSize="1134,22" />
      <sap2010:ViewStateData Id="Assign_366" sap:VirtualizedContainerService.HintSize="242,61.3333333333333" />
      <sap2010:ViewStateData Id="Assign_367" sap:VirtualizedContainerService.HintSize="242,61.3333333333333" />
      <sap2010:ViewStateData Id="Append_Line_16" sap:VirtualizedContainerService.HintSize="242,22" />
      <sap2010:ViewStateData Id="Sequence_181" sap:VirtualizedContainerService.HintSize="264,348.666666666667">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">True</x:Boolean>
            <x:Boolean x:Key="IsPinned">True</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="Assign_101" sap:VirtualizedContainerService.HintSize="554,62" />
      <sap2010:ViewStateData Id="Assign_102" sap:VirtualizedContainerService.HintSize="554,62" />
      <sap2010:ViewStateData Id="Assign_103" sap:VirtualizedContainerService.HintSize="554,62" />
      <sap2010:ViewStateData Id="Assign_104" sap:VirtualizedContainerService.HintSize="554,62" />
      <sap2010:ViewStateData Id="Assign_105" sap:VirtualizedContainerService.HintSize="554,62" />
      <sap2010:ViewStateData Id="Assign_106" sap:VirtualizedContainerService.HintSize="554,62" />
      <sap2010:ViewStateData Id="Assign_107" sap:VirtualizedContainerService.HintSize="554,62" />
      <sap2010:ViewStateData Id="Assign_108" sap:VirtualizedContainerService.HintSize="554,62" />
      <sap2010:ViewStateData Id="Assign_109" sap:VirtualizedContainerService.HintSize="554,62" />
      <sap2010:ViewStateData Id="Assign_414" sap:VirtualizedContainerService.HintSize="242,62" />
      <sap2010:ViewStateData Id="Assign_110" sap:VirtualizedContainerService.HintSize="242,62" />
      <sap2010:ViewStateData Id="If_141" sap:VirtualizedContainerService.HintSize="554,216">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsPinned">True</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="IONAPIRequestWizard_15" sap:VirtualizedContainerService.HintSize="554,22" />
      <sap2010:ViewStateData Id="Assign_112" sap:VirtualizedContainerService.HintSize="554,62" />
      <sap2010:ViewStateData Id="Assign_113" sap:VirtualizedContainerService.HintSize="242,62" />
      <sap2010:ViewStateData Id="Assign_114" sap:VirtualizedContainerService.HintSize="242,62" />
      <sap2010:ViewStateData Id="Append_Line_11" sap:VirtualizedContainerService.HintSize="242,22" />
      <sap2010:ViewStateData Id="Sequence_76" sap:VirtualizedContainerService.HintSize="264,350">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">True</x:Boolean>
            <x:Boolean x:Key="IsPinned">True</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="Assign_115" sap:VirtualizedContainerService.HintSize="242,62" />
      <sap2010:ViewStateData Id="Assign_116" sap:VirtualizedContainerService.HintSize="242,62" />
      <sap2010:ViewStateData Id="Append_Line_12" sap:VirtualizedContainerService.HintSize="242,22" />
      <sap2010:ViewStateData Id="Sequence_77" sap:VirtualizedContainerService.HintSize="264,350">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">True</x:Boolean>
            <x:Boolean x:Key="IsPinned">True</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="If_53" sap:VirtualizedContainerService.HintSize="554,504">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">True</x:Boolean>
            <x:Boolean x:Key="IsPinned">True</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="Sequence_78" sap:VirtualizedContainerService.HintSize="200,52.6666666666667">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">False</x:Boolean>
            <x:Boolean x:Key="IsPinned">False</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="If_137" sap:VirtualizedContainerService.HintSize="464,214">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">True</x:Boolean>
            <x:Boolean x:Key="IsPinned">True</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="Sequence_79" sap:VirtualizedContainerService.HintSize="200,52">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">False</x:Boolean>
            <x:Boolean x:Key="IsPinned">False</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="ForEach`1_5" sap:VirtualizedContainerService.HintSize="286,211.333333333333">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">True</x:Boolean>
            <x:Boolean x:Key="IsPinned">True</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="Sequence_80" sap:VirtualizedContainerService.HintSize="308,335.333333333333">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">True</x:Boolean>
            <x:Boolean x:Key="IsPinned">True</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="If_124" sap:VirtualizedContainerService.HintSize="464,487.333333333333">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsPinned">True</x:Boolean>
            <x:Boolean x:Key="IsExpanded">True</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="InvokeWorkflow_9" sap:VirtualizedContainerService.HintSize="200,22" />
      <sap2010:ViewStateData Id="Assign_341" sap:VirtualizedContainerService.HintSize="554,62" />
      <sap2010:ViewStateData Id="Assign_340" sap:VirtualizedContainerService.HintSize="554,62" />
      <sap2010:ViewStateData Id="Assign_339" sap:VirtualizedContainerService.HintSize="554,62" />
      <sap2010:ViewStateData Id="Assign_338" sap:VirtualizedContainerService.HintSize="554,61.3333333333333" />
      <sap2010:ViewStateData Id="Assign_337" sap:VirtualizedContainerService.HintSize="554,61.3333333333333" />
      <sap2010:ViewStateData Id="Assign_336" sap:VirtualizedContainerService.HintSize="554,61.3333333333333" />
      <sap2010:ViewStateData Id="Assign_342" sap:VirtualizedContainerService.HintSize="554,61.3333333333333" />
      <sap2010:ViewStateData Id="IONAPIRequestWizard_32" sap:VirtualizedContainerService.HintSize="554,22" />
      <sap2010:ViewStateData Id="Assign_359" sap:VirtualizedContainerService.HintSize="554,61.3333333333333" />
      <sap2010:ViewStateData Id="Assign_360" sap:VirtualizedContainerService.HintSize="242,61.3333333333333" />
      <sap2010:ViewStateData Id="Assign_361" sap:VirtualizedContainerService.HintSize="242,61.3333333333333" />
      <sap2010:ViewStateData Id="Append_Line_13" sap:VirtualizedContainerService.HintSize="242,22" />
      <sap2010:ViewStateData Id="Sequence_178" sap:VirtualizedContainerService.HintSize="264,348.666666666667">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">True</x:Boolean>
            <x:Boolean x:Key="IsPinned">True</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="Assign_362" sap:VirtualizedContainerService.HintSize="242,61.3333333333333" />
      <sap2010:ViewStateData Id="Assign_363" sap:VirtualizedContainerService.HintSize="242,61.3333333333333" />
      <sap2010:ViewStateData Id="Append_Line_14" sap:VirtualizedContainerService.HintSize="242,22" />
      <sap2010:ViewStateData Id="Sequence_179" sap:VirtualizedContainerService.HintSize="264,348.666666666667">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">True</x:Boolean>
            <x:Boolean x:Key="IsPinned">True</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="If_133" sap:VirtualizedContainerService.HintSize="554,500.666666666667">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">True</x:Boolean>
            <x:Boolean x:Key="IsPinned">True</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="Sequence_164" sap:VirtualizedContainerService.HintSize="200,52.6666666666667">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">False</x:Boolean>
            <x:Boolean x:Key="IsPinned">False</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="Sequence_162" sap:VirtualizedContainerService.HintSize="222,238.666666666667">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">True</x:Boolean>
            <x:Boolean x:Key="IsPinned">True</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="If_125" sap:VirtualizedContainerService.HintSize="464,390.666666666667">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsPinned">True</x:Boolean>
            <x:Boolean x:Key="IsExpanded">True</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="Sequence_182" sap:VirtualizedContainerService.HintSize="486,1042">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">True</x:Boolean>
            <x:Boolean x:Key="IsPinned">True</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="If_134" sap:VirtualizedContainerService.HintSize="1134,1194">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsPinned">True</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="Sequence_176" sap:VirtualizedContainerService.HintSize="1156,3806.66666666667">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">True</x:Boolean>
            <x:Boolean x:Key="IsPinned">True</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="Sequence_75" sap:VirtualizedContainerService.HintSize="1178,3930.66666666667">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">False</x:Boolean>
            <x:Boolean x:Key="IsPinned">False</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="RehostedWorkflowDesigner.Workflow_1" sap:VirtualizedContainerService.HintSize="1218,4091.33333333333">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="ShouldCollapseAll">True</x:Boolean>
            <x:Boolean x:Key="ShouldExpandAll">False</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
    </sap2010:ViewStateManager>
  </sap2010:WorkflowViewState.ViewStateManager>
</Activity>