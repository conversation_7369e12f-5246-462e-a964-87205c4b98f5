﻿<Activity mc:Ignorable="sap sap2010 sads" x:Class="RehostedWorkflowDesigner.Workflow" this:Workflow.logicalId="infor.ims.imsfromrpastudio" this:Workflow.imsAPIUrl="https://mingle-ionapi.inforcloudsuite.com/OLDCASTLE_DEV/IONSERVICES/api/ion/messaging/service/v2/message" this:Workflow.Process_ID="x1" this:Workflow.Operation="insert" this:Workflow.documentName="RPA_Process_Attributes"
 xmlns="http://schemas.microsoft.com/netfx/2009/xaml/activities"
 xmlns:iai="clr-namespace:Infor.Activities.IONAPI;assembly=Infor.Activities.IONAPI"
 xmlns:ias="clr-namespace:Infor.Activities.Sys;assembly=Infor.Activities.Sys"
 xmlns:iru="clr-namespace:Infor.RPA.Utilities;assembly=Infor.RPA.Utilities"
 xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006"
 xmlns:mva="clr-namespace:Microsoft.VisualBasic.Activities;assembly=System.Activities"
 xmlns:njl="clr-namespace:Newtonsoft.Json.Linq;assembly=Newtonsoft.Json"
 xmlns:s="clr-namespace:System;assembly=mscorlib"
 xmlns:sads="http://schemas.microsoft.com/netfx/2010/xaml/activities/debugger"
 xmlns:sap="http://schemas.microsoft.com/netfx/2009/xaml/activities/presentation"
 xmlns:sap2010="http://schemas.microsoft.com/netfx/2010/xaml/activities/presentation"
 xmlns:scg="clr-namespace:System.Collections.Generic;assembly=mscorlib"
 xmlns:sco="clr-namespace:System.Collections.ObjectModel;assembly=mscorlib"
 xmlns:this="clr-namespace:RehostedWorkflowDesigner"
 xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml">
  <x:Members>
    <x:Property Name="logicalId" Type="InArgument(x:String)" />
    <x:Property Name="imsAPIUrl" Type="InArgument(x:String)" />
    <x:Property Name="Process_ID" Type="InArgument(x:String)" />
    <x:Property Name="Operation" Type="InArgument(x:String)" />
    <x:Property Name="AttributeList" Type="InArgument(scg:List(s:String[]))" />
    <x:Property Name="documentName" Type="InArgument(x:String)" />
    <x:Property Name="logFile" Type="InArgument(x:String)" />
  </x:Members>
  <mva:VisualBasic.Settings>
    <x:Null />
  </mva:VisualBasic.Settings>
  <sap2010:WorkflowViewState.IdRef>RehostedWorkflowDesigner.Workflow_1</sap2010:WorkflowViewState.IdRef>
  <TextExpression.NamespacesForImplementation>
    <sco:Collection x:TypeArguments="x:String">
      <x:String>System.Activities</x:String>
      <x:String>System.Activities.Statements</x:String>
      <x:String>System.Activities.Expressions</x:String>
      <x:String>System.Activities.Validation</x:String>
      <x:String>System.Activities.XamlIntegration</x:String>
      <x:String>Infor.Activities.Email</x:String>
      <x:String>Infor.Activities.IONAPI</x:String>
      <x:String>Infor.Activities.Sys</x:String>
      <x:String>Infor.RPA.Utilities</x:String>
      <x:String>Microsoft.VisualBasic.Activities</x:String>
      <x:String>Newtonsoft.Json</x:String>
      <x:String>Newtonsoft.Json.Linq</x:String>
      <x:String>System</x:String>
      <x:String>System.Activities.Debugger.Symbol</x:String>
      <x:String>System.Collections.Generic</x:String>
      <x:String>System.Windows.Markup</x:String>
      <x:String>Microsoft.VisualBasic.CompilerServices</x:String>
    </sco:Collection>
  </TextExpression.NamespacesForImplementation>
  <TextExpression.ReferencesForImplementation>
    <sco:Collection x:TypeArguments="AssemblyReference">
      <AssemblyReference>System.Activities</AssemblyReference>
      <AssemblyReference>Infor.Activities.Email</AssemblyReference>
      <AssemblyReference>Infor.Activities.IONAPI</AssemblyReference>
      <AssemblyReference>Infor.Activities.Sys</AssemblyReference>
      <AssemblyReference>Infor.RPA.Utilities</AssemblyReference>
      <AssemblyReference>Newtonsoft.Json</AssemblyReference>
      <AssemblyReference>System.ServiceModel</AssemblyReference>
      <AssemblyReference>System</AssemblyReference>
      <AssemblyReference>System.Core</AssemblyReference>
      <AssemblyReference>mscorlib</AssemblyReference>
      <AssemblyReference>System.Runtime.WindowsRuntime</AssemblyReference>
      <AssemblyReference>Microsoft.Bcl.AsyncInterfaces</AssemblyReference>
      <AssemblyReference>PresentationFramework</AssemblyReference>
      <AssemblyReference>WindowsBase</AssemblyReference>
      <AssemblyReference>PresentationCore</AssemblyReference>
      <AssemblyReference>System.Xaml</AssemblyReference>
      <AssemblyReference>Microsoft.VisualBasic</AssemblyReference>
      <AssemblyReference>System.ComponentModel.Composition</AssemblyReference>
      <AssemblyReference>System.Memory</AssemblyReference>
    </sco:Collection>
  </TextExpression.ReferencesForImplementation>
  <Sequence DisplayName="SentToInvoiceProcessingResults_Attributes_Sequence_MainSequence_1" sap2010:WorkflowViewState.IdRef="Sequence_2">
    <Sequence.Variables>
      <Variable x:TypeArguments="x:String" Name="value" />
      <Variable x:TypeArguments="x:Int32" Name="responseStatus" />
      <Variable x:TypeArguments="njl:JToken" Name="jout" />
      <Variable x:TypeArguments="njl:JToken" Name="jout1" />
      <Variable x:TypeArguments="iru:ResponseObject" Name="responseObject" />
      <Variable x:TypeArguments="x:String" Name="requestStr" />
      <Variable x:TypeArguments="njl:JToken" Name="requestToken" />
      <Variable x:TypeArguments="x:String" Name="varJtoken" />
      <Variable x:TypeArguments="iru:ResponseObject" Name="resp" />
      <Variable x:TypeArguments="njl:JToken" Name="uout" />
      <Variable x:TypeArguments="x:String" Name="ERP" />
      <Variable x:TypeArguments="x:String" Name="createdBy" />
      <Variable x:TypeArguments="x:String" Name="userID" />
      <Variable x:TypeArguments="x:String" Name="ProcessAttributeID" />
      <Variable x:TypeArguments="x:Int32" Name="Variation_ID" />
      <Variable x:TypeArguments="x:String" Name="AttributeName" />
      <Variable x:TypeArguments="x:String" Name="AttributeValue" />
      <Variable x:TypeArguments="x:String" Name="Var" />
      <Variable x:TypeArguments="x:String" Name="value1" />
      <Variable x:TypeArguments="x:Int32" Name="Index" />
      <Variable x:TypeArguments="s:String[]" Name="Totallist" />
    </Sequence.Variables>
    <Assign DisplayName="SentToInvoiceProcessingResults_Attributes_Assign_Index_2" sap2010:WorkflowViewState.IdRef="Assign_37">
      <Assign.To>
        <OutArgument x:TypeArguments="x:Int32">[Index]</OutArgument>
      </Assign.To>
      <Assign.Value>
        <InArgument x:TypeArguments="x:Int32">1</InArgument>
      </Assign.Value>
    </Assign>
    <Assign DisplayName="SentToInvoiceProcessingResults_Attributes_Assign_value_3" sap2010:WorkflowViewState.IdRef="Assign_52">
      <Assign.To>
        <OutArgument x:TypeArguments="x:String">[value]</OutArgument>
      </Assign.To>
      <Assign.Value>
        <InArgument x:TypeArguments="x:String">
          <Literal x:TypeArguments="x:String" Value="" />
        </InArgument>
      </Assign.Value>
    </Assign>
    <Assign DisplayName="SentToInvoiceProcessingResults_Attributes_Assign_Totallist_4" sap2010:WorkflowViewState.IdRef="Assign_53">
      <Assign.To>
        <OutArgument x:TypeArguments="s:String[]">[Totallist]</OutArgument>
      </Assign.To>
      <Assign.Value>
        <InArgument x:TypeArguments="s:String[]">[New String(AttributeList.Count-1){}]</InArgument>
      </Assign.Value>
    </Assign>
    <ForEach x:TypeArguments="s:String[]" DisplayName="SentToInvoiceProcessingResults_Attributes_ForEach_AttributeList_5" sap2010:WorkflowViewState.IdRef="ForEach`1_1" Values="[AttributeList]">
      <ActivityAction x:TypeArguments="s:String[]">
        <ActivityAction.Argument>
          <DelegateInArgument x:TypeArguments="s:String[]" Name="item" />
        </ActivityAction.Argument>
        <Sequence DisplayName="SentToInvoiceProcessingResults_Attributes_Sequence_ItemSequence_6" sap2010:WorkflowViewState.IdRef="Sequence_24">
          <If Condition="[Operation = &quot;insert&quot;]" DisplayName="SentToInvoiceProcessingResults_Attributes_If_Operation_7" sap2010:WorkflowViewState.IdRef="If_11">
            <If.Then>
              <Sequence DisplayName="SentToInvoiceProcessingResults_Attributes_Sequence_InsertOperation_8" sap2010:WorkflowViewState.IdRef="Sequence_25">
                <Assign DisplayName="SentToInvoiceProcessingResults_Attributes_Assign_Variation_ID_9" sap2010:WorkflowViewState.IdRef="Assign_36">
                  <Assign.To>
                    <OutArgument x:TypeArguments="x:Int32">[Variation_ID]</OutArgument>
                  </Assign.To>
                  <Assign.Value>
                    <InArgument x:TypeArguments="x:Int32">1</InArgument>
                  </Assign.Value>
                </Assign>
                <Assign DisplayName="SentToInvoiceProcessingResults_Attributes_Assign_ProcessAttributeID_10" sap2010:WorkflowViewState.IdRef="Assign_38">
                  <Assign.To>
                    <OutArgument x:TypeArguments="x:String">[ProcessAttributeID]</OutArgument>
                  </Assign.To>
                  <Assign.Value>
                    <InArgument x:TypeArguments="x:String">[Process_ID+Convert.toString(Index)]</InArgument>
                  </Assign.Value>
                </Assign>
                <Assign DisplayName="SentToInvoiceProcessingResults_Attributes_Assign_AttributeName_11" sap2010:WorkflowViewState.IdRef="Assign_39">
                  <Assign.To>
                    <OutArgument x:TypeArguments="x:String">[AttributeName]</OutArgument>
                  </Assign.To>
                  <Assign.Value>
                    <InArgument x:TypeArguments="x:String">[item(1)]</InArgument>
                  </Assign.Value>
                </Assign>
                <TryCatch DisplayName="SentToInvoiceProcessingResults_Attributes_TryCatch_AttributeValue_12" sap2010:WorkflowViewState.IdRef="TryCatch_1">
                  <TryCatch.Try>
                    <Assign DisplayName="Try SentToInvoiceProcessingResults_Attributes_Assign_AttributeValue_13" sap2010:WorkflowViewState.IdRef="Assign_41">
                      <Assign.To>
                        <OutArgument x:TypeArguments="x:String">[AttributeValue]</OutArgument>
                      </Assign.To>
                      <Assign.Value>
                        <InArgument x:TypeArguments="x:String">[item(2).Replace("""","").Replace("\","").Replace("\n","")]</InArgument>
                      </Assign.Value>
                    </Assign>
                  </TryCatch.Try>
                  <TryCatch.Catches>
                    <Catch x:TypeArguments="s:Exception" sap2010:WorkflowViewState.IdRef="Catch`1_1">
                      <ActivityAction x:TypeArguments="s:Exception">
                        <ActivityAction.Argument>
                          <DelegateInArgument x:TypeArguments="s:Exception" Name="exception" />
                        </ActivityAction.Argument>
                        <Assign DisplayName="SentToInvoiceProcessingResults_Attributes_Assign_AttributeValue_14" sap2010:WorkflowViewState.IdRef="Assign_61">
                          <Assign.To>
                            <OutArgument x:TypeArguments="x:String">[AttributeValue]</OutArgument>
                          </Assign.To>
                          <Assign.Value>
                            <InArgument x:TypeArguments="x:String">
                              <Literal x:TypeArguments="x:String" Value="" />
                            </InArgument>
                          </Assign.Value>
                        </Assign>
                      </ActivityAction>
                    </Catch>
                  </TryCatch.Catches>
                </TryCatch>
                <Assign DisplayName="SentToInvoiceProcessingResults_Attributes_Assign_Var_15" sap2010:WorkflowViewState.IdRef="Assign_40">
                  <Assign.To>
                    <OutArgument x:TypeArguments="x:String">[Var]</OutArgument>
                  </Assign.To>
                  <Assign.Value>
                    <InArgument x:TypeArguments="x:String">[Convert.toString(Variation_ID)]</InArgument>
                  </Assign.Value>
                </Assign>
                <Assign DisplayName="SentToInvoiceProcessingResults_Attributes_Assign_Totallist_16" sap2010:WorkflowViewState.IdRef="Assign_54">
                  <Assign.To>
                    <OutArgument x:TypeArguments="x:String">[Totallist(index-1)]</OutArgument>
                  </Assign.To>
                  <Assign.Value>
                    <InArgument x:TypeArguments="x:String">["{\""RPA_ProcessAttributeID\"":\"""+ProcessAttributeID+"\"",\""Process_ID\"":\"""+Process_ID+"\"",\""AttributeName\"":\"""+AttributeName+"\"",\""AttributeValue\"":\"""+AttributeValue+"\"",\""Variation_ID\"":\"""+Variation_ID.tostring+"\""}"]</InArgument>
                  </Assign.Value>
                </Assign>
                <Assign DisplayName="SentToInvoiceProcessingResults_Attributes_Assign_index_17" sap2010:WorkflowViewState.IdRef="Assign_47">
                  <Assign.To>
                    <OutArgument x:TypeArguments="x:Int32">[index]</OutArgument>
                  </Assign.To>
                  <Assign.Value>
                    <InArgument x:TypeArguments="x:Int32">[index+1]</InArgument>
                  </Assign.Value>
                </Assign>
              </Sequence>
            </If.Then>
            <If.Else>
              <Sequence DisplayName="SentToInvoiceProcessingResults_Attributes_Sequence_ElseSequence_18" sap2010:WorkflowViewState.IdRef="Sequence_26">
                <If Condition="[Operation = &quot;update&quot;]" DisplayName="SentToInvoiceProcessingResults_Attributes_If_update_19" sap2010:WorkflowViewState.IdRef="If_12">
                  <If.Then>
                    <Sequence DisplayName="SentToInvoiceProcessingResults_Attributes_Sequence_UpdateData_20" sap2010:WorkflowViewState.IdRef="Sequence_27">
                      <Assign DisplayName="SentToInvoiceProcessingResults_Attributes_Assign_ProcessAttributeID_21" sap2010:WorkflowViewState.IdRef="Assign_42">
                        <Assign.To>
                          <OutArgument x:TypeArguments="x:String">[ProcessAttributeID]</OutArgument>
                        </Assign.To>
                        <Assign.Value>
                          <InArgument x:TypeArguments="x:String">[item(0)]</InArgument>
                        </Assign.Value>
                      </Assign>
                      <Assign DisplayName="SentToInvoiceProcessingResults_Attributes_Assign_AttributeName_22" sap2010:WorkflowViewState.IdRef="Assign_43">
                        <Assign.To>
                          <OutArgument x:TypeArguments="x:String">[AttributeName]</OutArgument>
                        </Assign.To>
                        <Assign.Value>
                          <InArgument x:TypeArguments="x:String">[item(1)]</InArgument>
                        </Assign.Value>
                      </Assign>
                      <TryCatch DisplayName="SentToInvoiceProcessingResults_Attributes_TryCatch_AttributeValue_23" sap2010:WorkflowViewState.IdRef="TryCatch_2">
                        <TryCatch.Try>
                          <Assign DisplayName="SentToInvoiceProcessingResults_Attributes_Assign_AttributeValue_24" sap2010:WorkflowViewState.IdRef="Assign_44">
                            <Assign.To>
                              <OutArgument x:TypeArguments="x:String">[AttributeValue]</OutArgument>
                            </Assign.To>
                            <Assign.Value>
                              <InArgument x:TypeArguments="x:String">[item(2).Replace("""","").Replace("\","").Replace("\n","")]</InArgument>
                            </Assign.Value>
                          </Assign>
                        </TryCatch.Try>
                        <TryCatch.Catches>
                          <Catch x:TypeArguments="s:Exception" sap2010:WorkflowViewState.IdRef="Catch`1_2">
                            <ActivityAction x:TypeArguments="s:Exception">
                              <ActivityAction.Argument>
                                <DelegateInArgument x:TypeArguments="s:Exception" Name="exception" />
                              </ActivityAction.Argument>
                              <Assign DisplayName="SentToInvoiceProcessingResults_Attributes_Assign_AttributeValue_25" sap2010:WorkflowViewState.IdRef="Assign_62">
                                <Assign.To>
                                  <OutArgument x:TypeArguments="x:String">[AttributeValue]</OutArgument>
                                </Assign.To>
                                <Assign.Value>
                                  <InArgument x:TypeArguments="x:String">
                                    <Literal x:TypeArguments="x:String" Value="" />
                                  </InArgument>
                                </Assign.Value>
                              </Assign>
                            </ActivityAction>
                          </Catch>
                        </TryCatch.Catches>
                      </TryCatch>
                      <Assign DisplayName="SentToInvoiceProcessingResults_Attributes_Assign_Var_26" sap2010:WorkflowViewState.IdRef="Assign_46">
                        <Assign.To>
                          <OutArgument x:TypeArguments="x:String">[Var]</OutArgument>
                        </Assign.To>
                        <Assign.Value>
                          <InArgument x:TypeArguments="x:String">[(Convert.ToDouble(item(3))+1).tostring]</InArgument>
                        </Assign.Value>
                      </Assign>
                      <Assign DisplayName="SentToInvoiceProcessingResults_Attributes_Assign_Totallist_27" sap2010:WorkflowViewState.IdRef="Assign_60">
                        <Assign.To>
                          <OutArgument x:TypeArguments="x:String">[Totallist(index-1)]</OutArgument>
                        </Assign.To>
                        <Assign.Value>
                          <InArgument x:TypeArguments="x:String">["{\""RPA_ProcessAttributeID\"":\"""+ProcessAttributeID+"\"",\""Process_ID\"":\"""+Process_ID+"\"",\""AttributeName\"":\"""+AttributeName+"\"",\""AttributeValue\"":\"""+AttributeValue+"\"",\""Variation_ID\"":\"""+Var.tostring+"\""}"]</InArgument>
                        </Assign.Value>
                      </Assign>
                      <Assign sap2010:WorkflowViewState.IdRef="Assign_63">
                        <Assign.To>
                          <OutArgument x:TypeArguments="x:Int32">[index]</OutArgument>
                        </Assign.To>
                        <Assign.Value>
                          <InArgument x:TypeArguments="x:Int32">[index+1]</InArgument>
                        </Assign.Value>
                      </Assign>
                    </Sequence>
                  </If.Then>
                </If>
              </Sequence>
            </If.Else>
          </If>
        </Sequence>
      </ActivityAction>
    </ForEach>
    <Sequence DisplayName="SentToInvoiceProcessingResults_Attributes_Sequence_PushAttributesSequence_29" sap2010:WorkflowViewState.IdRef="Sequence_38">
      <Assign DisplayName="SentToInvoiceProcessingResults_Attributes_Assign_value_30" sap2010:WorkflowViewState.IdRef="Assign_59">
        <Assign.To>
          <OutArgument x:TypeArguments="x:String">[value]</OutArgument>
        </Assign.To>
        <Assign.Value>
          <InArgument x:TypeArguments="x:String">[String.Join("\n", Totallist)]</InArgument>
        </Assign.Value>
      </Assign>
      <ias:Template_Apply ErrorCode="{x:Null}" ContinueOnError="True" DisplayName="SentToInvoiceProcessingResults_Attributes_Template_Apply_requestStr_31" sap2010:WorkflowViewState.IdRef="Template_Apply_12" Template="[&quot;{  &quot;&quot;documentName&quot;&quot;: &quot;&quot;{{%documentName%}}&quot;&quot;,  &quot;&quot;messageId&quot;&quot;: &quot;&quot;msg#1234598&quot;&quot;, &quot;&quot;fromLogicalId&quot;&quot;: &quot;&quot;lid://{{%logicalId%}}&quot;&quot;, &quot;&quot;toLogicalId&quot;&quot;: &quot;&quot;lid://default&quot;&quot;,  &quot;&quot;document&quot;&quot;: {     &quot;&quot;value&quot;&quot;: &quot;&quot;{{%datalakeAttributes%}}&quot;&quot;,  &quot;&quot;encoding&quot;&quot;: &quot;&quot;NONE&quot;&quot;,  &quot;&quot;characterSet&quot;&quot;: &quot;&quot;UTF-8&quot;&quot; } }&quot;]" Text="[requestStr]">
        <ias:Template_Apply.Values>
          <scg:List x:TypeArguments="scg:List(x:String)" Capacity="4">
            <scg:List x:TypeArguments="x:String" Capacity="4">
              <x:String>documentName</x:String>
              <x:String>logicalId</x:String>
              <x:String>datalakeAttributes</x:String>
            </scg:List>
            <scg:List x:TypeArguments="x:String" Capacity="4">
              <x:String>documentName</x:String>
              <x:String>logicalId</x:String>
              <x:String>value</x:String>
            </scg:List>
          </scg:List>
        </ias:Template_Apply.Values>
      </ias:Template_Apply>
      <ias:DeserializeJSON ErrorCode="{x:Null}" ContinueOnError="True" DisplayName="SentToInvoiceProcessingResults_Attributes_DeserializeJSON_jout_32" sap2010:WorkflowViewState.IdRef="DeserializeJSON_6" JTokenObject="[jout]" JTokenString="[requestStr]" />
      <iai:IONAPIRequestWizard FileAttachments="{x:Null}" QueryParameters="{x:Null}" ResponseCode="{x:Null}" ContentType="application_json" ContinueOnError="True" DisplayName="SentToInvoiceProcessingResults_Attributes_IONAPIRequestWizard_responseObject_33" HttpMethod="POST" sap2010:WorkflowViewState.IdRef="IONAPIRequestWizard_8" PostData="[requestStr]" Response="[responseObject]" StatusCode="[responseStatus]" Url="[imsAPIUrl]">
        <iai:IONAPIRequestWizard.Headers>
          <scg:List x:TypeArguments="scg:List(x:String)" Capacity="4">
            <scg:List x:TypeArguments="x:String" Capacity="4">
              <x:String>Accept</x:String>
            </scg:List>
            <scg:List x:TypeArguments="x:String" Capacity="4">
              <x:String>application/json</x:String>
            </scg:List>
          </scg:List>
        </iai:IONAPIRequestWizard.Headers>
      </iai:IONAPIRequestWizard>
      <If Condition="[responseStatus= 200]" DisplayName="SentToInvoiceProcessingResults_Attributes_If_responseStatus_34" sap2010:WorkflowViewState.IdRef="If_18">
        <If.Then>
          <Sequence DisplayName="SentToInvoiceProcessingResults_Attributes_Sequence_SuccessSequence_35" sap2010:WorkflowViewState.IdRef="Sequence_37">
            <Sequence.Variables>
              <Variable x:TypeArguments="njl:JToken" Name="reponseToken" />
            </Sequence.Variables>
            <Assign DisplayName="SentToInvoiceProcessingResults_Attributes_Assign_reponseToken_36" sap2010:WorkflowViewState.IdRef="Assign_51">
              <Assign.To>
                <OutArgument x:TypeArguments="njl:JToken">[reponseToken]</OutArgument>
              </Assign.To>
              <Assign.Value>
                <InArgument x:TypeArguments="njl:JToken">[responseObject.readasjson]</InArgument>
              </Assign.Value>
            </Assign>
            <If Condition="[reponseToken.tostring.Contains(&quot;status&quot;) and reponseToken(&quot;status&quot;).tostring=&quot;OK&quot;]" DisplayName="SentToInvoiceProcessingResults_Attributes_If_reponseToken_37" sap2010:WorkflowViewState.IdRef="If_17">
              <If.Then>
                <Sequence DisplayName="SentToInvoiceProcessingResults_Attributes_Sequence_OKSequence_38" sap2010:WorkflowViewState.IdRef="Sequence_35">
                  <ias:StudioWriteLine ErrorCode="{x:Null}" ContinueOnError="True" DisplayName="SentToInvoiceProcessingResults_Attributes_StudioWriteLine_OK_39" sap2010:WorkflowViewState.IdRef="StudioWriteLine_3" Line="Datalake Status : OK" />
                </Sequence>
              </If.Then>
              <If.Else>
                <Sequence DisplayName="SentToInvoiceProcessingResults_Attributes_Sequence_ErrorSequence_40" sap2010:WorkflowViewState.IdRef="Sequence_36">
                  <ias:StudioWriteLine ErrorCode="{x:Null}" ContinueOnError="True" DisplayName="SentToInvoiceProcessingResults_Attributes_StudioWriteLine_ERROR_41" sap2010:WorkflowViewState.IdRef="StudioWriteLine_4" Line="Datalake Status : ERROR" />
                </Sequence>
              </If.Else>
            </If>
          </Sequence>
        </If.Then>
      </If>
    </Sequence>
    <sads:DebugSymbol.Symbol>d3VDOlxVc2Vyc1xza2F2aXJheWFuaVxBcHBEYXRhXExvY2FsXEluZm9yUlBBXE0zSW52b2ljZVByb2Nlc3NpbmdHZW5BSVYzXFNlbnRUb0ludm9pY2VQcm9jZXNzaW5nUmVzdWx0c19BdHRyaWJ1dGVzLnhhbWxlAW8BigEBBgGkAQGNAgEFAcUCAcwCAQQBqAIBqwIBAwHpAgGAAwECSgPwAg4CAQFiBWkOAwGRAWoFcw4DAY0BdAV7DgMBiAF8BaoCDwIBKqsCBe4CEAIBAmcvZzADAZQBZDBkNwMBkgFwC3A6AwGQAWwxbDgDAY4BeTJ5VwMBiwF2M3Y+AwGJAXywAXzBAQMBhgGBAQmoAhQCASusAgezAhACASW0AgfDAhwCASHEAgfEAoACAgEcxQIH0AIhAgET0QIH7QIMAgEDggELpwIQAgEssQIysQJQAgEorgIzrgI6AgEmtAKyBrQCwAYCASO0AtkBtAKsBgIBIsQC7wHEAv0BAgEfxALZAcQC4QECAR3FAtUCxQLjAgIBGsUC7QLFAv8CAgEYxQKiA8UCrwMCARbFAosDxQKdAwIBFNECFdECLAIBBNMCC+sCFgIBBoIBGYIBOwIBLYQBD9QBGgIBW9cBD6UCGgIBL9cCDd4CFgIBD98CDeoCEgIBB4UBEYwBGgMBggGNARGUARoCAX6VARGcARoCAXmdARG7ARwCAW+8ARHDARoCAWvEARHLARoCAWHMARHTARoCAVzYARGkAhYCATDcAjrcAlUCARLZAjvZAkkCARDfAhvfAo4BAgEI4QIR4wIcAgEM5gIR6AIcAgEJigE7igE8AwGFAYcBPIcBSgMBgwGSATySAWADAYEBjwE9jwFRAgF/mgE8mgFFAgF8lwE9lwFMAgF6nwEVpgEeAgF0rgEZtwEiAgFwwQE8wQFcAgFuvgE9vgFCAgFsyQE8yQGuAgIBZcYBPcYBUQIBYtEBO9EBRAIBX84BPM4BQwIBXdgBH9gBQQIBMdoBFaICIAIBM+ICE+IC9AECAQ3nAhPnAvoBAgEKpAFApAF7AgF3oQFBoQFRAgF1tAEftAFOAgFzsAFFsAFVAgFx2wEX4gEgAgFW4wEX6gEgAgFR6wEXiQIiAgFHigIXkQIgAgFDkgIXmQIgAgE5mgIXoQIgAgE04gLbAeIC8QECAQ7nAt4B5wL3AQIBC+ABQuABSwIBWd0BQ90BVwIBV+gBQugBSwIBVOUBQ+UBUgIBUu0BG/QBJAIBTPwBH4UCKAIBSI8CQo8CagIBRowCQ4wCSAIBRJcCQpcCqwICAT2UAkOUAlcCATqfAkGfAkoCATecAkKcAkkCATXyAUbyAYEBAgFP7wFH7wFXAgFNggIlggJUAgFL/gFL/gFbAgFJ</sads:DebugSymbol.Symbol>
  </Sequence>
  <sap2010:WorkflowViewState.ViewStateManager>
    <sap2010:ViewStateManager>
      <sap2010:ViewStateData Id="Assign_37" sap:VirtualizedContainerService.HintSize="1107.33333333333,62" />
      <sap2010:ViewStateData Id="Assign_52" sap:VirtualizedContainerService.HintSize="1107.33333333333,62" />
      <sap2010:ViewStateData Id="Assign_53" sap:VirtualizedContainerService.HintSize="1107.33333333333,62" />
      <sap2010:ViewStateData Id="Assign_36" sap:VirtualizedContainerService.HintSize="418,62" />
      <sap2010:ViewStateData Id="Assign_38" sap:VirtualizedContainerService.HintSize="418,62" />
      <sap2010:ViewStateData Id="Assign_39" sap:VirtualizedContainerService.HintSize="418.666666666667,62" />
      <sap2010:ViewStateData Id="Assign_41" sap:VirtualizedContainerService.HintSize="242,62" />
      <sap2010:ViewStateData Id="Assign_61" sap:VirtualizedContainerService.HintSize="242,62" />
      <sap2010:ViewStateData Id="Catch`1_1" sap:VirtualizedContainerService.HintSize="404.666666666667,22">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">False</x:Boolean>
            <x:Boolean x:Key="IsPinned">False</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="TryCatch_1" sap:VirtualizedContainerService.HintSize="418.666666666667,300">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">True</x:Boolean>
            <x:Boolean x:Key="IsPinned">False</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="Assign_40" sap:VirtualizedContainerService.HintSize="418.666666666667,62" />
      <sap2010:ViewStateData Id="Assign_54" sap:VirtualizedContainerService.HintSize="418,62" />
      <sap2010:ViewStateData Id="Assign_47" sap:VirtualizedContainerService.HintSize="418,60" />
      <sap2010:ViewStateData Id="Sequence_25" sap:VirtualizedContainerService.HintSize="440.666666666667,1034">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">True</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="Assign_42" sap:VirtualizedContainerService.HintSize="418.666666666667,62" />
      <sap2010:ViewStateData Id="Assign_43" sap:VirtualizedContainerService.HintSize="418.666666666667,62" />
      <sap2010:ViewStateData Id="Assign_44" sap:VirtualizedContainerService.HintSize="242,62" />
      <sap2010:ViewStateData Id="Assign_62" sap:VirtualizedContainerService.HintSize="242,62" />
      <sap2010:ViewStateData Id="Catch`1_2" sap:VirtualizedContainerService.HintSize="404.666666666667,22">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">False</x:Boolean>
            <x:Boolean x:Key="IsPinned">False</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="TryCatch_2" sap:VirtualizedContainerService.HintSize="418.666666666667,300">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">True</x:Boolean>
            <x:Boolean x:Key="IsPinned">False</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="Assign_46" sap:VirtualizedContainerService.HintSize="418.666666666667,62" />
      <sap2010:ViewStateData Id="Assign_60" sap:VirtualizedContainerService.HintSize="418.666666666667,62" />
      <sap2010:ViewStateData Id="Assign_63" sap:VirtualizedContainerService.HintSize="418,60" />
      <sap2010:ViewStateData Id="Sequence_27" sap:VirtualizedContainerService.HintSize="440.666666666667,932">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">True</x:Boolean>
            <x:Boolean x:Key="IsPinned">False</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="If_12" sap:VirtualizedContainerService.HintSize="566.666666666667,1086" />
      <sap2010:ViewStateData Id="Sequence_26" sap:VirtualizedContainerService.HintSize="588.666666666667,1210">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">True</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="If_11" sap:VirtualizedContainerService.HintSize="1055.33333333333,1364" />
      <sap2010:ViewStateData Id="Sequence_24" sap:VirtualizedContainerService.HintSize="1077.33333333333,1488">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">True</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="ForEach`1_1" sap:VirtualizedContainerService.HintSize="1108,1640.66666666667">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">True</x:Boolean>
            <x:Boolean x:Key="IsPinned">False</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="Assign_59" sap:VirtualizedContainerService.HintSize="616,60" />
      <sap2010:ViewStateData Id="Template_Apply_12" sap:VirtualizedContainerService.HintSize="616,22" />
      <sap2010:ViewStateData Id="DeserializeJSON_6" sap:VirtualizedContainerService.HintSize="616,22" />
      <sap2010:ViewStateData Id="IONAPIRequestWizard_8" sap:VirtualizedContainerService.HintSize="616,22" />
      <sap2010:ViewStateData Id="Assign_51" sap:VirtualizedContainerService.HintSize="469,60" />
      <sap2010:ViewStateData Id="StudioWriteLine_3" sap:VirtualizedContainerService.HintSize="200,22" />
      <sap2010:ViewStateData Id="Sequence_35" sap:VirtualizedContainerService.HintSize="222,146">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">True</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="StudioWriteLine_4" sap:VirtualizedContainerService.HintSize="200,22" />
      <sap2010:ViewStateData Id="Sequence_36" sap:VirtualizedContainerService.HintSize="222,146">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">True</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="If_17" sap:VirtualizedContainerService.HintSize="469,294" />
      <sap2010:ViewStateData Id="Sequence_37" sap:VirtualizedContainerService.HintSize="491,518">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">True</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="If_18" sap:VirtualizedContainerService.HintSize="616,666" />
      <sap2010:ViewStateData Id="Sequence_38" sap:VirtualizedContainerService.HintSize="1104,1076">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">True</x:Boolean>
            <x:Boolean x:Key="IsPinned">False</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="Sequence_2" sap:VirtualizedContainerService.HintSize="1130,3186.66666666667">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">True</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="RehostedWorkflowDesigner.Workflow_1" sap:VirtualizedContainerService.HintSize="1170,3306.66666666667" />
    </sap2010:ViewStateManager>
  </sap2010:WorkflowViewState.ViewStateManager>
</Activity>