﻿<Activity mc:Ignorable="sap sap2010 sads" x:Class="RehostedWorkflowDesigner.Workflow"
 xmlns="http://schemas.microsoft.com/netfx/2009/xaml/activities"
 xmlns:iad="clr-namespace:Infor.Activities.Debug;assembly=Infor.Activities.Debug"
 xmlns:iai="clr-namespace:Infor.Activities.IONAPI;assembly=Infor.Activities.IONAPI"
 xmlns:ias="clr-namespace:Infor.Activities.Sys;assembly=Infor.Activities.Sys"
 xmlns:iru="clr-namespace:Infor.RPA.Utilities;assembly=Infor.RPA.Utilities"
 xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006"
 xmlns:mva="clr-namespace:Microsoft.VisualBasic.Activities;assembly=System.Activities"
 xmlns:njl="clr-namespace:Newtonsoft.Json.Linq;assembly=Newtonsoft.Json"
 xmlns:s="clr-namespace:System;assembly=mscorlib"
 xmlns:sads="http://schemas.microsoft.com/netfx/2010/xaml/activities/debugger"
 xmlns:sap="http://schemas.microsoft.com/netfx/2009/xaml/activities/presentation"
 xmlns:sap2010="http://schemas.microsoft.com/netfx/2010/xaml/activities/presentation"
 xmlns:scg="clr-namespace:System.Collections.Generic;assembly=mscorlib"
 xmlns:sco="clr-namespace:System.Collections.ObjectModel;assembly=mscorlib"
 xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml">
  <x:Members>
    <x:Property Name="tenantID" Type="InArgument(x:String)" />
    <x:Property Name="VendorID" Type="InArgument(x:String)" />
    <x:Property Name="division" Type="InArgument(x:String)" />
    <x:Property Name="company" Type="InArgument(x:String)" />
    <x:Property Name="APResp" Type="OutArgument(x:String)" />
    <x:Property Name="User_GUID" Type="OutArgument(x:String)" />
    <x:Property Name="poNumber" Type="InArgument(x:String)" />
    <x:Property Name="commentStatus" Type="InArgument(x:String)" />
    <x:Property Name="Status" Type="InArgument(x:String)" />
  </x:Members>
  <mva:VisualBasic.Settings>
    <x:Null />
  </mva:VisualBasic.Settings>
  <sap2010:WorkflowViewState.IdRef>RehostedWorkflowDesigner.Workflow_1</sap2010:WorkflowViewState.IdRef>
  <TextExpression.NamespacesForImplementation>
    <sco:Collection x:TypeArguments="x:String">
      <x:String>System.Activities</x:String>
      <x:String>System.Activities.Statements</x:String>
      <x:String>System.Activities.Expressions</x:String>
      <x:String>System.Activities.Validation</x:String>
      <x:String>System.Activities.XamlIntegration</x:String>
      <x:String>Infor.Activities.Email</x:String>
      <x:String>Infor.Activities.IONAPI</x:String>
      <x:String>Infor.Activities.Sys</x:String>
      <x:String>Infor.RPA.Utilities</x:String>
      <x:String>Microsoft.VisualBasic.Activities</x:String>
      <x:String>Newtonsoft.Json</x:String>
      <x:String>Newtonsoft.Json.Linq</x:String>
      <x:String>System</x:String>
      <x:String>System.Activities.Debugger.Symbol</x:String>
      <x:String>System.Collections.Generic</x:String>
      <x:String>System.Windows.Markup</x:String>
      <x:String>Microsoft.VisualBasic.CompilerServices</x:String>
      <x:String>System.Linq</x:String>
    </sco:Collection>
  </TextExpression.NamespacesForImplementation>
  <TextExpression.ReferencesForImplementation>
    <sco:Collection x:TypeArguments="AssemblyReference">
      <AssemblyReference>System.Activities</AssemblyReference>
      <AssemblyReference>Infor.Activities.Email</AssemblyReference>
      <AssemblyReference>Infor.Activities.IONAPI</AssemblyReference>
      <AssemblyReference>Infor.Activities.Sys</AssemblyReference>
      <AssemblyReference>Infor.RPA.Utilities</AssemblyReference>
      <AssemblyReference>Newtonsoft.Json</AssemblyReference>
      <AssemblyReference>System.Core</AssemblyReference>
      <AssemblyReference>System</AssemblyReference>
      <AssemblyReference>mscorlib</AssemblyReference>
      <AssemblyReference>System.ServiceModel</AssemblyReference>
      <AssemblyReference>System.Runtime.WindowsRuntime</AssemblyReference>
      <AssemblyReference>Microsoft.Bcl.AsyncInterfaces</AssemblyReference>
      <AssemblyReference>System.ComponentModel.Composition</AssemblyReference>
      <AssemblyReference>PresentationFramework</AssemblyReference>
      <AssemblyReference>WindowsBase</AssemblyReference>
      <AssemblyReference>PresentationCore</AssemblyReference>
      <AssemblyReference>System.Xaml</AssemblyReference>
      <AssemblyReference>System.Memory</AssemblyReference>
      <AssemblyReference>Microsoft.VisualBasic</AssemblyReference>
      <AssemblyReference>System.Collections.Immutable</AssemblyReference>
    </sco:Collection>
  </TextExpression.ReferencesForImplementation>
  <Sequence DisplayName="AP Responsible Sequence" sap2010:WorkflowViewState.IdRef="Sequence_3">
    <Sequence.Variables>
      <Variable x:TypeArguments="x:String" Name="brFomat" />
      <Variable x:TypeArguments="x:String" Name="businessRulesAPIURL" />
      <Variable x:TypeArguments="njl:JToken" Name="brDesOpt" />
      <Variable x:TypeArguments="iru:ResponseObject" Name="respOutput" />
      <Variable x:TypeArguments="x:Int32" Name="brRespStatus" />
      <Variable x:TypeArguments="iru:ResponseObject" Name="respObj1" />
      <Variable x:TypeArguments="x:Int32" Name="StatusCode1" />
      <Variable x:TypeArguments="njl:JToken" Name="out1" />
      <Variable x:TypeArguments="iru:ResponseObject" Name="respObj2" />
      <Variable x:TypeArguments="x:Int32" Name="StatusCode2" />
      <Variable x:TypeArguments="scg:List(x:String)" Default="[New List(Of String) From{&quot;SHIVARAJU&quot;}]" Name="var_usernames" />
      <Variable x:TypeArguments="njl:JToken" Name="ProxyAPIoutput" />
      <Variable x:TypeArguments="x:String" Name="CATEGORY">
        <Variable.Default>
          <Literal x:TypeArguments="x:String" Value="" />
        </Variable.Default>
      </Variable>
    </Sequence.Variables>
    <Assign DisplayName="Assign_usernameslist" sap2010:WorkflowViewState.IdRef="Assign_29">
      <Assign.To>
        <OutArgument x:TypeArguments="scg:List(x:String)">[var_usernames]</OutArgument>
      </Assign.To>
      <Assign.Value>
        <InArgument x:TypeArguments="scg:List(x:String)">[New List(Of String)]</InArgument>
      </Assign.Value>
    </Assign>
    <Assign sap2010:WorkflowViewState.IdRef="Assign_9">
      <Assign.To>
        <OutArgument x:TypeArguments="x:String">[APResp]</OutArgument>
      </Assign.To>
      <Assign.Value>
        <InArgument x:TypeArguments="x:String">
          <Literal x:TypeArguments="x:String" Value="" />
        </InArgument>
      </Assign.Value>
    </Assign>
    <Assign DisplayName="Assign User_GUID" sap2010:WorkflowViewState.IdRef="Assign_14">
      <Assign.To>
        <OutArgument x:TypeArguments="x:String">[User_GUID]</OutArgument>
      </Assign.To>
      <Assign.Value>
        <InArgument x:TypeArguments="x:String">[""]</InArgument>
      </Assign.Value>
    </Assign>
    <Sequence sap2010:WorkflowViewState.IdRef="Sequence_6">
      <Sequence DisplayName="Happy_Sequence" sap2010:WorkflowViewState.IdRef="Sequence_26">
        <Assign DisplayName="Assign_PO" sap2010:WorkflowViewState.IdRef="Assign_48">
          <Assign.To>
            <OutArgument x:TypeArguments="x:String">[poNumber]</OutArgument>
          </Assign.To>
          <Assign.Value>
            <InArgument x:TypeArguments="x:String">[If(String.IsNullOrEmpty(poNumber), "0", poNumber)]</InArgument>
          </Assign.Value>
        </Assign>
        <Assign DisplayName="Assign_division" sap2010:WorkflowViewState.IdRef="Assign_49">
          <Assign.To>
            <OutArgument x:TypeArguments="x:String">[division]</OutArgument>
          </Assign.To>
          <Assign.Value>
            <InArgument x:TypeArguments="x:String">[If(String.IsNullOrEmpty(division), "", division)]</InArgument>
          </Assign.Value>
        </Assign>
        <Assign DisplayName="Assign_company" sap2010:WorkflowViewState.IdRef="Assign_50">
          <Assign.To>
            <OutArgument x:TypeArguments="x:String">[company]</OutArgument>
          </Assign.To>
          <Assign.Value>
            <InArgument x:TypeArguments="x:String">[If(String.IsNullOrEmpty(company), "", company)]</InArgument>
          </Assign.Value>
        </Assign>
        <Assign DisplayName="Assign_VendorID" sap2010:WorkflowViewState.IdRef="Assign_51">
          <Assign.To>
            <OutArgument x:TypeArguments="x:String">[VendorID]</OutArgument>
          </Assign.To>
          <Assign.Value>
            <InArgument x:TypeArguments="x:String">[If(String.IsNullOrEmpty(VendorID), "0", VendorID)]</InArgument>
          </Assign.Value>
        </Assign>
        <If Condition="[Not(commentStatus.Equals(&quot;&quot;)) and (commentStatus.Equals(&quot;Quantity mismatch&quot;) or commentStatus.Equals(&quot;Price mismatch&quot;))]" DisplayName="If-Pricemismatch" sap2010:WorkflowViewState.IdRef="If_39">
          <If.Then>
            <Sequence DisplayName="tolerance-Sequence" sap2010:WorkflowViewState.IdRef="Sequence_39">
              <Assign DisplayName="Assign_VcommentStatus" sap2010:WorkflowViewState.IdRef="Assign_70">
                <Assign.To>
                  <OutArgument x:TypeArguments="x:String">[CATEGORY]</OutArgument>
                </Assign.To>
                <Assign.Value>
                  <InArgument x:TypeArguments="x:String">[commentStatus]</InArgument>
                </Assign.Value>
              </Assign>
            </Sequence>
          </If.Then>
          <If.Else>
            <Sequence DisplayName="Status_Sequence" sap2010:WorkflowViewState.IdRef="Sequence_40">
              <Assign DisplayName="Assign_VcommentStatus" sap2010:WorkflowViewState.IdRef="Assign_71">
                <Assign.To>
                  <OutArgument x:TypeArguments="x:String">[CATEGORY]</OutArgument>
                </Assign.To>
                <Assign.Value>
                  <InArgument x:TypeArguments="x:String">[Status]</InArgument>
                </Assign.Value>
              </Assign>
            </Sequence>
          </If.Else>
        </If>
        <Assign DisplayName="Assign_VcommentStatus" sap2010:WorkflowViewState.IdRef="Assign_67">
          <Assign.To>
            <OutArgument x:TypeArguments="x:String">[CATEGORY]</OutArgument>
          </Assign.To>
          <Assign.Value>
            <InArgument x:TypeArguments="x:String">[If(String.IsNullOrEmpty(commentStatus), "0", commentStatus)]</InArgument>
          </Assign.Value>
        </Assign>
        <iai:IONAPIRequestWizard FileAttachments="{x:Null}" PostData="{x:Null}" ResponseCode="{x:Null}" ContentType="application_json" ContinueOnError="True" DisplayName="IONAPI Request for vendor name" HttpMethod="GET" sap2010:WorkflowViewState.IdRef="IONAPIRequestWizard_20" Response="[respObj2]" StatusCode="[StatusCode2]" Url="[TenantID + &quot;CustomerApi/APRAPI/Authorization&quot;]">
          <iai:IONAPIRequestWizard.Headers>
            <scg:List x:TypeArguments="scg:List(x:String)" Capacity="4">
              <scg:List x:TypeArguments="x:String" Capacity="4">
                <x:String>Accept</x:String>
              </scg:List>
              <scg:List x:TypeArguments="x:String" Capacity="4">
                <x:String>application/json</x:String>
              </scg:List>
            </scg:List>
          </iai:IONAPIRequestWizard.Headers>
          <iai:IONAPIRequestWizard.QueryParameters>
            <scg:List x:TypeArguments="scg:List(x:String)" Capacity="4">
              <scg:List x:TypeArguments="x:String" Capacity="16">
                <x:String>extendedresult</x:String>
                <x:String>format</x:String>
                <x:String>righttrim</x:String>
                <x:String>excludeempty</x:String>
                <x:String>dateformat</x:String>
                <x:String>SUNO</x:String>
                <x:String>CONO</x:String>
                <x:String>PUNO</x:String>
                <x:String>DIVI</x:String>
                <x:String>Category</x:String>
              </scg:List>
              <scg:List x:TypeArguments="x:String" Capacity="16">
                <x:String>false</x:String>
                <x:String>PRETTY</x:String>
                <x:String>true</x:String>
                <x:String>false</x:String>
                <x:String>YMD8</x:String>
                <x:String>VendorID</x:String>
                <x:String>company</x:String>
                <x:String>poNumber</x:String>
                <x:String>division</x:String>
                <x:String>CATEGORY</x:String>
              </scg:List>
            </scg:List>
          </iai:IONAPIRequestWizard.QueryParameters>
        </iai:IONAPIRequestWizard>
      </Sequence>
      <If Condition="[StatusCode2 = 200]" sap2010:WorkflowViewState.IdRef="If_3">
        <If.Then>
          <Sequence DisplayName="AP_Sequence" sap2010:WorkflowViewState.IdRef="Sequence_5">
            <Assign DisplayName="Assign_PROXYAPI" sap2010:WorkflowViewState.IdRef="Assign_52">
              <Assign.To>
                <OutArgument x:TypeArguments="njl:JToken">[ProxyAPIoutput]</OutArgument>
              </Assign.To>
              <Assign.Value>
                <InArgument x:TypeArguments="njl:JToken">[JToken.Parse(respObj2.ReadAsText)]</InArgument>
              </Assign.Value>
            </Assign>
            <If sap2010:Annotation.AnnotationText="" Condition="[ProxyAPIoutput(&quot;result&quot;).ToString = &quot;[]&quot;]" DisplayName="If ProxyAPIoutput" sap2010:WorkflowViewState.IdRef="If_30">
              <If.Then>
                <Assign DisplayName="Assign_APResp" sap2010:WorkflowViewState.IdRef="Assign_54">
                  <Assign.To>
                    <OutArgument x:TypeArguments="x:String">[APResp]</OutArgument>
                  </Assign.To>
                  <Assign.Value>
                    <InArgument x:TypeArguments="x:String">
                      <Literal x:TypeArguments="x:String" Value="" />
                    </InArgument>
                  </Assign.Value>
                </Assign>
              </If.Then>
              <If.Else>
                <Sequence sap2010:WorkflowViewState.IdRef="Sequence_27">
                  <Assign DisplayName="Assign_APResp" sap2010:WorkflowViewState.IdRef="Assign_53">
                    <Assign.To>
                      <OutArgument x:TypeArguments="x:String">[APResp]</OutArgument>
                    </Assign.To>
                    <Assign.Value>
                      <InArgument x:TypeArguments="x:String">[ProxyAPIoutput("result").ToString()]</InArgument>
                    </Assign.Value>
                  </Assign>
                </Sequence>
              </If.Else>
            </If>
            <iad:CommentOut sap2010:Annotation.AnnotationText="based on ranga instructions" DisplayName="Comment Out" sap2010:WorkflowViewState.IdRef="CommentOut_4">
              <iad:CommentOut.Activities>
                <Assign sap2010:WorkflowViewState.IdRef="Assign_10">
                  <Assign.To>
                    <OutArgument x:TypeArguments="njl:JToken">[out1]</OutArgument>
                  </Assign.To>
                  <Assign.Value>
                    <InArgument x:TypeArguments="njl:JToken">[respObj1.ReadAsJson]</InArgument>
                  </Assign.Value>
                </Assign>
                <Assign sap2010:WorkflowViewState.IdRef="Assign_11">
                  <Assign.To>
                    <OutArgument x:TypeArguments="x:String">[APResp]</OutArgument>
                  </Assign.To>
                  <Assign.Value>
                    <InArgument x:TypeArguments="x:String">[out1("AP Responsible").ToString]</InArgument>
                  </Assign.Value>
                </Assign>
              </iad:CommentOut.Activities>
            </iad:CommentOut>
          </Sequence>
        </If.Then>
      </If>
    </Sequence>
    <iad:CommentOut sap2010:Annotation.AnnotationText="based on ranga words commented" DisplayName="Comment Out" sap2010:WorkflowViewState.IdRef="CommentOut_3">
      <iad:CommentOut.Activities>
        <If Condition="[APResp = &quot;&quot;]" sap2010:WorkflowViewState.IdRef="If_5">
          <If.Then>
            <Sequence sap2010:WorkflowViewState.IdRef="Sequence_8">
              <iai:IONAPIRequestWizard FileAttachments="{x:Null}" PostData="{x:Null}" ResponseCode="{x:Null}" ContentType="application_json" ContinueOnError="True" DisplayName="APResp IONAPI Request" HttpMethod="GET" sap2010:WorkflowViewState.IdRef="IONAPIRequestWizard_3" Response="[respObj1]" StatusCode="[StatusCode1]" Url="[TenantID + &quot;CustomerApi/APRAPI/APAuthorization/CUSEXTMI/GetFieldValue&quot;]">
                <iai:IONAPIRequestWizard.Headers>
                  <scg:List x:TypeArguments="scg:List(x:String)" Capacity="4">
                    <scg:List x:TypeArguments="x:String" Capacity="4">
                      <x:String>Accept</x:String>
                    </scg:List>
                    <scg:List x:TypeArguments="x:String" Capacity="4">
                      <x:String>application/json</x:String>
                    </scg:List>
                  </scg:List>
                </iai:IONAPIRequestWizard.Headers>
                <iai:IONAPIRequestWizard.QueryParameters>
                  <scg:List x:TypeArguments="scg:List(x:String)" Capacity="4">
                    <scg:List x:TypeArguments="x:String" Capacity="4">
                      <x:String>cono</x:String>
                      <x:String>PK01</x:String>
                      <x:String>PK02</x:String>
                    </scg:List>
                    <scg:List x:TypeArguments="x:String" Capacity="4">
                      <x:String>company</x:String>
                      <x:String>VendorID</x:String>
                      <x:String>division</x:String>
                    </scg:List>
                  </scg:List>
                </iai:IONAPIRequestWizard.QueryParameters>
              </iai:IONAPIRequestWizard>
              <If Condition="[StatusCode1 = 200]" sap2010:WorkflowViewState.IdRef="If_4">
                <If.Then>
                  <Sequence sap2010:WorkflowViewState.IdRef="Sequence_7">
                    <Assign sap2010:WorkflowViewState.IdRef="Assign_12">
                      <Assign.To>
                        <OutArgument x:TypeArguments="njl:JToken">[out1]</OutArgument>
                      </Assign.To>
                      <Assign.Value>
                        <InArgument x:TypeArguments="njl:JToken">[respObj1.ReadAsJson]</InArgument>
                      </Assign.Value>
                    </Assign>
                    <Assign sap2010:WorkflowViewState.IdRef="Assign_13">
                      <Assign.To>
                        <OutArgument x:TypeArguments="x:String">[APResp]</OutArgument>
                      </Assign.To>
                      <Assign.Value>
                        <InArgument x:TypeArguments="x:String">[out1("AP Responsible").ToString]</InArgument>
                      </Assign.Value>
                    </Assign>
                  </Sequence>
                </If.Then>
              </If>
            </Sequence>
          </If.Then>
        </If>
        <If Condition="[APResp = &quot;&quot;]" DisplayName="If_Businessrule" sap2010:WorkflowViewState.IdRef="If_21">
          <If.Then>
            <Sequence DisplayName="Business rule Sequence" sap2010:WorkflowViewState.IdRef="Sequence_4">
              <ias:Template_Apply ErrorCode="{x:Null}" ContinueOnError="True" DisplayName="Templating Activity" sap2010:WorkflowViewState.IdRef="Template_Apply_1" Template="{}{ 'parameters': { 'Supplier': '{{%VendorID%}}','Division':'{{%division%}}','Company':'{{%company%}}' } }" Text="[brFomat]">
                <ias:Template_Apply.Values>
                  <scg:List x:TypeArguments="scg:List(x:String)" Capacity="4">
                    <scg:List x:TypeArguments="x:String" Capacity="4">
                      <x:String>VendorID</x:String>
                      <x:String>division</x:String>
                      <x:String>company</x:String>
                    </scg:List>
                    <scg:List x:TypeArguments="x:String" Capacity="4">
                      <x:String>VendorID</x:String>
                      <x:String>division</x:String>
                      <x:String>company</x:String>
                    </scg:List>
                  </scg:List>
                </ias:Template_Apply.Values>
              </ias:Template_Apply>
              <Assign sap2010:WorkflowViewState.IdRef="Assign_2">
                <Assign.To>
                  <OutArgument x:TypeArguments="x:String">[businessRulesAPIURL]</OutArgument>
                </Assign.To>
                <Assign.Value>
                  <InArgument x:TypeArguments="x:String">[TenantID +"IONSERVICES/businessrules/decision/execute/"+businessRule]</InArgument>
                </Assign.Value>
              </Assign>
              <ias:DeserializeJSON ErrorCode="{x:Null}" ContinueOnError="True" DisplayName="Deserialize JSON" sap2010:WorkflowViewState.IdRef="DeserializeJSON_1" JTokenObject="[brDesOpt]" JTokenString="[brFomat]" />
              <iai:IONAPIRequestWizard FileAttachments="{x:Null}" ResponseCode="{x:Null}" ContentType="application_json" ContinueOnError="True" DisplayName="IONAPI Request" HttpMethod="POST" sap2010:WorkflowViewState.IdRef="IONAPIRequestWizard_1" PostData="[brDesOpt.toString()]" Response="[respOutput]" StatusCode="[brRespStatus]" Url="[TenantID +&quot;IONSERVICES/businessrules/decision/execute/&quot;+businessRule]">
                <iai:IONAPIRequestWizard.Headers>
                  <scg:List x:TypeArguments="scg:List(x:String)" Capacity="4">
                    <scg:List x:TypeArguments="x:String" Capacity="4">
                      <x:String>Accept</x:String>
                    </scg:List>
                    <scg:List x:TypeArguments="x:String" Capacity="4">
                      <x:String>application/json</x:String>
                    </scg:List>
                  </scg:List>
                </iai:IONAPIRequestWizard.Headers>
                <iai:IONAPIRequestWizard.QueryParameters>
                  <scg:List x:TypeArguments="scg:List(x:String)" Capacity="4">
                    <scg:List x:TypeArguments="x:String" Capacity="4">
                      <x:String>VendorID</x:String>
                      <x:String>Division</x:String>
                    </scg:List>
                    <scg:List x:TypeArguments="x:String" Capacity="4">
                      <x:String>vendorID</x:String>
                      <x:String>division</x:String>
                    </scg:List>
                  </scg:List>
                </iai:IONAPIRequestWizard.QueryParameters>
              </iai:IONAPIRequestWizard>
              <TryCatch DisplayName="APResp Try - If" sap2010:WorkflowViewState.IdRef="TryCatch_1">
                <TryCatch.Try>
                  <If Condition="[respOutput.ReadAsJson(&quot;parameters&quot;).HasValues]" DisplayName="APResp If" sap2010:WorkflowViewState.IdRef="If_1">
                    <If.Then>
                      <Assign DisplayName="APResp If - Else - Assign" sap2010:WorkflowViewState.IdRef="Assign_6">
                        <Assign.To>
                          <OutArgument x:TypeArguments="x:String">[APResp]</OutArgument>
                        </Assign.To>
                        <Assign.Value>
                          <InArgument x:TypeArguments="x:String">[respOutput.ReadAsJson("parameters")("APperson").toString]</InArgument>
                        </Assign.Value>
                      </Assign>
                    </If.Then>
                    <If.Else>
                      <Assign DisplayName="APResp If - Else - Assign" sap2010:WorkflowViewState.IdRef="Assign_7">
                        <Assign.To>
                          <OutArgument x:TypeArguments="x:String">[APResp]</OutArgument>
                        </Assign.To>
                        <Assign.Value>
                          <InArgument x:TypeArguments="x:String">
                            <Literal x:TypeArguments="x:String" Value="" />
                          </InArgument>
                        </Assign.Value>
                      </Assign>
                    </If.Else>
                  </If>
                </TryCatch.Try>
                <TryCatch.Catches>
                  <Catch x:TypeArguments="s:Exception" sap2010:WorkflowViewState.IdRef="Catch`1_1">
                    <ActivityAction x:TypeArguments="s:Exception">
                      <ActivityAction.Argument>
                        <DelegateInArgument x:TypeArguments="s:Exception" Name="exception" />
                      </ActivityAction.Argument>
                      <Assign DisplayName="APResp Catch - Assign" sap2010:WorkflowViewState.IdRef="Assign_5">
                        <Assign.To>
                          <OutArgument x:TypeArguments="x:String">[APResp]</OutArgument>
                        </Assign.To>
                        <Assign.Value>
                          <InArgument x:TypeArguments="x:String">
                            <Literal x:TypeArguments="x:String" Value="" />
                          </InArgument>
                        </Assign.Value>
                      </Assign>
                    </ActivityAction>
                  </Catch>
                </TryCatch.Catches>
              </TryCatch>
            </Sequence>
          </If.Then>
        </If>
      </iad:CommentOut.Activities>
    </iad:CommentOut>
    <If Condition="[APResp = &quot;&quot;]" sap2010:WorkflowViewState.IdRef="If_2">
      <If.Then>
        <Assign sap2010:WorkflowViewState.IdRef="Assign_8">
          <Assign.To>
            <OutArgument x:TypeArguments="x:String">[APResp]</OutArgument>
          </Assign.To>
          <Assign.Value>
            <InArgument x:TypeArguments="x:String">UNKNOWN</InArgument>
          </Assign.Value>
        </Assign>
      </If.Then>
      <If.Else>
        <Sequence DisplayName="GUID_Sequence" sap2010:WorkflowViewState.IdRef="Sequence_19">
          <If sap2010:Annotation.AnnotationText="" Condition="[ProxyAPIoutput(&quot;result&quot;).ToString = &quot;[]&quot;]" DisplayName="If ProxyAPIoutput" sap2010:WorkflowViewState.IdRef="If_34">
            <If.Then>
              <Assign DisplayName="Assign User_GUID_Empty" sap2010:WorkflowViewState.IdRef="Assign_59">
                <Assign.To>
                  <OutArgument x:TypeArguments="x:String">[User_GUID]</OutArgument>
                </Assign.To>
                <Assign.Value>
                  <InArgument x:TypeArguments="x:String">
                    <Literal x:TypeArguments="x:String" Value="" />
                  </InArgument>
                </Assign.Value>
              </Assign>
            </If.Then>
            <If.Else>
              <Sequence DisplayName="username_Assign_Sequence" sap2010:WorkflowViewState.IdRef="Sequence_30">
                <Assign DisplayName="Assign_usernameslist" sap2010:WorkflowViewState.IdRef="Assign_60">
                  <Assign.To>
                    <OutArgument x:TypeArguments="scg:List(x:String)">[var_usernames]</OutArgument>
                  </Assign.To>
                  <Assign.Value>
                    <InArgument x:TypeArguments="scg:List(x:String)">[ProxyAPIoutput("result").ToString().Split(","C).ToList()]</InArgument>
                  </Assign.Value>
                </Assign>
              </Sequence>
            </If.Else>
          </If>
          <If Condition="[var_usernames.Count &lt;&gt; 0]" sap2010:WorkflowViewState.IdRef="If_36">
            <If.Then>
              <ForEach x:TypeArguments="x:String" DisplayName="ForEach&lt;Username&gt;" sap2010:WorkflowViewState.IdRef="ForEach`1_3" Values="[var_usernames]">
                <ActivityAction x:TypeArguments="x:String">
                  <ActivityAction.Argument>
                    <DelegateInArgument x:TypeArguments="x:String" Name="item" />
                  </ActivityAction.Argument>
                  <Sequence sap2010:WorkflowViewState.IdRef="Sequence_29">
                    <Sequence.Variables>
                      <Variable x:TypeArguments="x:Int32" Name="ResponseCode" />
                    </Sequence.Variables>
                    <ias:Template_Apply ContinueOnError="True" DisplayName="Templating Activity" ErrorCode="[brRespStatus]" sap2010:WorkflowViewState.IdRef="Template_Apply_7" Template="{}{ 'erpPersonIds': ['{{%erpPersonIds%}}'], 'includeUserProperties': true}" Text="[brFomat]">
                      <ias:Template_Apply.Values>
                        <scg:List x:TypeArguments="scg:List(x:String)" Capacity="4">
                          <scg:List x:TypeArguments="x:String" Capacity="4">
                            <x:String>erpPersonIds</x:String>
                            <x:String>erpAccountingEntity</x:String>
                          </scg:List>
                          <scg:List x:TypeArguments="x:String" Capacity="4">
                            <x:String>APResp</x:String>
                            <x:String>erpAccountingEntity</x:String>
                          </scg:List>
                        </scg:List>
                      </ias:Template_Apply.Values>
                    </ias:Template_Apply>
                    <ias:DeserializeJSON ErrorCode="{x:Null}" ContinueOnError="True" DisplayName="Deserialize JSON" sap2010:WorkflowViewState.IdRef="DeserializeJSON_7" JTokenObject="[brDesOpt]" JTokenString="[brFomat]" />
                    <iai:IONAPIRequestWizard FileAttachments="{x:Null}" ResponseCode="{x:Null}" sap2010:Annotation.AnnotationText="API call of erppersonids to get User_GUID value" ContentType="application_json" ContinueOnError="True" DisplayName="IONAPI Request" HttpMethod="POST" sap2010:WorkflowViewState.IdRef="IONAPIRequestWizard_21" PostData="[brDesOpt]" Response="[respOutput]" StatusCode="[brRespStatus]" Url="[TenantID+&quot;ifsservice/usermgt/v2/users/search/erppersonids&quot;]">
                      <iai:IONAPIRequestWizard.Headers>
                        <scg:List x:TypeArguments="scg:List(x:String)" Capacity="4">
                          <scg:List x:TypeArguments="x:String" Capacity="4">
                            <x:String>Accept</x:String>
                          </scg:List>
                          <scg:List x:TypeArguments="x:String" Capacity="4">
                            <x:String>application/json</x:String>
                          </scg:List>
                        </scg:List>
                      </iai:IONAPIRequestWizard.Headers>
                      <iai:IONAPIRequestWizard.QueryParameters>
                        <scg:List x:TypeArguments="scg:List(x:String)" Capacity="4">
                          <scg:List x:TypeArguments="x:String" Capacity="0" />
                          <scg:List x:TypeArguments="x:String" Capacity="0" />
                        </scg:List>
                      </iai:IONAPIRequestWizard.QueryParameters>
                    </iai:IONAPIRequestWizard>
                    <If Condition="[brRespStatus = 200]" DisplayName="If_GUID" sap2010:WorkflowViewState.IdRef="If_33">
                      <If.Then>
                        <Sequence sap2010:WorkflowViewState.IdRef="Sequence_31">
                          <If Condition="[JToken.Parse(respOutput.ReadAsText)(&quot;response&quot;)(&quot;userlist&quot;).tostring &lt;&gt; &quot;[]&quot;]" sap2010:WorkflowViewState.IdRef="If_35">
                            <If.Then>
                              <Sequence sap2010:WorkflowViewState.IdRef="Sequence_32">
                                <Assign DisplayName="Assign User_GUID_Empty" sap2010:WorkflowViewState.IdRef="Assign_66">
                                  <Assign.To>
                                    <OutArgument x:TypeArguments="x:String">[User_GUID]</OutArgument>
                                  </Assign.To>
                                  <Assign.Value>
                                    <InArgument x:TypeArguments="x:String">
                                      <Literal x:TypeArguments="x:String" Value="" />
                                    </InArgument>
                                  </Assign.Value>
                                </Assign>
                                <ForEach x:TypeArguments="njl:JToken" DisplayName="ForEach&lt;JToken&gt;" sap2010:WorkflowViewState.IdRef="ForEach`1_6" Values="[JToken.Parse(respOutput.ReadAsText)(&quot;response&quot;)(&quot;userlist&quot;)]">
                                  <ActivityAction x:TypeArguments="njl:JToken">
                                    <ActivityAction.Argument>
                                      <DelegateInArgument x:TypeArguments="njl:JToken" Name="item" />
                                    </ActivityAction.Argument>
                                    <Sequence sap2010:WorkflowViewState.IdRef="Sequence_33">
                                      <If Condition="[User_GUID &lt;&gt; &quot;&quot;]" sap2010:WorkflowViewState.IdRef="If_37">
                                        <If.Then>
                                          <Sequence DisplayName="GUIDadd-Sequence" sap2010:WorkflowViewState.IdRef="Sequence_35">
                                            <Assign DisplayName="Assign User_GUID" sap2010:WorkflowViewState.IdRef="Assign_58">
                                              <Assign.To>
                                                <OutArgument x:TypeArguments="x:String">[User_GUID]</OutArgument>
                                              </Assign.To>
                                              <Assign.Value>
                                                <InArgument x:TypeArguments="x:String">[(User_GUID+","+item("id").tostring).Trim]</InArgument>
                                              </Assign.Value>
                                            </Assign>
                                          </Sequence>
                                        </If.Then>
                                        <If.Else>
                                          <Sequence DisplayName="initial-Sequence" sap2010:WorkflowViewState.IdRef="Sequence_34">
                                            <Assign DisplayName="Assign User_GUID" sap2010:WorkflowViewState.IdRef="Assign_65">
                                              <Assign.To>
                                                <OutArgument x:TypeArguments="x:String">[User_GUID]</OutArgument>
                                              </Assign.To>
                                              <Assign.Value>
                                                <InArgument x:TypeArguments="x:String">[JToken.Parse(respOutput.ReadAsText)("response")("userlist")(0)("id").tostring]</InArgument>
                                              </Assign.Value>
                                            </Assign>
                                          </Sequence>
                                        </If.Else>
                                      </If>
                                    </Sequence>
                                  </ActivityAction>
                                </ForEach>
                              </Sequence>
                            </If.Then>
                            <If.Else>
                              <Assign DisplayName="Assign User_GUID" sap2010:WorkflowViewState.IdRef="Assign_61">
                                <Assign.To>
                                  <OutArgument x:TypeArguments="x:String">[User_GUID]</OutArgument>
                                </Assign.To>
                                <Assign.Value>
                                  <InArgument x:TypeArguments="x:String">
                                    <Literal x:TypeArguments="x:String" Value="" />
                                  </InArgument>
                                </Assign.Value>
                              </Assign>
                            </If.Else>
                          </If>
                          <iad:CommentOut DisplayName="Comment Out" sap2010:WorkflowViewState.IdRef="CommentOut_5">
                            <iad:CommentOut.Activities>
                              <If Condition="[JToken.Parse(respOutput.ReadAsText)(&quot;response&quot;)(&quot;userlist&quot;)(1)(&quot;id&quot;).tostring &lt;&gt; &quot;&quot;]" sap2010:WorkflowViewState.IdRef="If_32">
                                <If.Then>
                                  <Sequence sap2010:WorkflowViewState.IdRef="Sequence_28">
                                    <If Condition="[User_GUID = &quot;&quot;]" sap2010:WorkflowViewState.IdRef="If_31">
                                      <If.Then>
                                        <Assign DisplayName="Assign User_GUID" sap2010:WorkflowViewState.IdRef="Assign_57">
                                          <Assign.To>
                                            <OutArgument x:TypeArguments="x:String">[User_GUID]</OutArgument>
                                          </Assign.To>
                                          <Assign.Value>
                                            <InArgument x:TypeArguments="x:String">[JToken.Parse(respOutput.ReadAsText)("response")("userlist")(0)("id").tostring]</InArgument>
                                          </Assign.Value>
                                        </Assign>
                                      </If.Then>
                                    </If>
                                  </Sequence>
                                </If.Then>
                              </If>
                            </iad:CommentOut.Activities>
                          </iad:CommentOut>
                        </Sequence>
                      </If.Then>
                      <If.Else>
                        <Assign DisplayName="Assign User_GUID" sap2010:WorkflowViewState.IdRef="Assign_63">
                          <Assign.To>
                            <OutArgument x:TypeArguments="x:String">[User_GUID]</OutArgument>
                          </Assign.To>
                          <Assign.Value>
                            <InArgument x:TypeArguments="x:String">
                              <Literal x:TypeArguments="x:String" Value="" />
                            </InArgument>
                          </Assign.Value>
                        </Assign>
                      </If.Else>
                    </If>
                  </Sequence>
                </ActivityAction>
              </ForEach>
            </If.Then>
          </If>
          <iad:CommentOut DisplayName="DONOT Delete" sap2010:WorkflowViewState.IdRef="CommentOut_2">
            <iad:CommentOut.Activities>
              <Assign DisplayName="TEST RUN" sap2010:WorkflowViewState.IdRef="Assign_36">
                <Assign.To>
                  <OutArgument x:TypeArguments="scg:List(x:String)">[var_usernames]</OutArgument>
                </Assign.To>
                <Assign.Value>
                  <InArgument x:TypeArguments="scg:List(x:String)">[New List(Of String) From{"SHIVARAJU"}]</InArgument>
                </Assign.Value>
              </Assign>
            </iad:CommentOut.Activities>
          </iad:CommentOut>
          <iad:CommentOut DisplayName="Comment Out" sap2010:WorkflowViewState.IdRef="CommentOut_1">
            <iad:CommentOut.Activities>
              <If Condition="[Buyer.Contains(&quot;True&quot;)]" DisplayName="If - Buyer" sap2010:WorkflowViewState.IdRef="If_16">
                <If.Then>
                  <Sequence sap2010:WorkflowViewState.IdRef="Sequence_16">
                    <Sequence.Variables>
                      <Variable x:TypeArguments="x:String" Name="Req" />
                      <Variable x:TypeArguments="x:String" Name="BuyerName" />
                      <Variable x:TypeArguments="x:Int32" Name="ResponseCode" />
                      <Variable x:TypeArguments="iru:ResponseObject" Name="Response" />
                    </Sequence.Variables>
                    <Assign DisplayName="Assign- Req" sap2010:WorkflowViewState.IdRef="Assign_21">
                      <Assign.To>
                        <OutArgument x:TypeArguments="x:String">[Req]</OutArgument>
                      </Assign.To>
                      <Assign.Value>
                        <InArgument x:TypeArguments="x:String">["PUNO : "+poNumber+" AND DIVI : " +division]</InArgument>
                      </Assign.Value>
                    </Assign>
                    <iai:IONAPIRequestWizard FileAttachments="{x:Null}" PostData="{x:Null}" ResponseCode="{x:Null}" ContentType="application_json" ContinueOnError="True" DisplayName="IONAPI - Get User Name" HttpMethod="GET" sap2010:WorkflowViewState.IdRef="IONAPIRequestWizard_7" Response="[Response]" StatusCode="[ResponseCode]" Url="[TenantID + &quot;M3/m3api-rest/v2/execute/PPS200MI/SearchHead?&quot;]">
                      <iai:IONAPIRequestWizard.Headers>
                        <scg:List x:TypeArguments="scg:List(x:String)" Capacity="4">
                          <scg:List x:TypeArguments="x:String" Capacity="4">
                            <x:String>Accept</x:String>
                          </scg:List>
                          <scg:List x:TypeArguments="x:String" Capacity="4">
                            <x:String>application/json</x:String>
                          </scg:List>
                        </scg:List>
                      </iai:IONAPIRequestWizard.Headers>
                      <iai:IONAPIRequestWizard.QueryParameters>
                        <scg:List x:TypeArguments="scg:List(x:String)" Capacity="4">
                          <scg:List x:TypeArguments="x:String" Capacity="4">
                            <x:String>SQRY</x:String>
                          </scg:List>
                          <scg:List x:TypeArguments="x:String" Capacity="4">
                            <x:String>Req</x:String>
                          </scg:List>
                        </scg:List>
                      </iai:IONAPIRequestWizard.QueryParameters>
                    </iai:IONAPIRequestWizard>
                    <Assign DisplayName="Assign - Buyer" sap2010:WorkflowViewState.IdRef="Assign_22">
                      <Assign.To>
                        <OutArgument x:TypeArguments="x:String">[BuyerName]</OutArgument>
                      </Assign.To>
                      <Assign.Value>
                        <InArgument x:TypeArguments="x:String">[Response.readasjson("results")(0)("records")(0)("BUYE").ToString]</InArgument>
                      </Assign.Value>
                    </Assign>
                    <ias:Template_Apply ContinueOnError="True" DisplayName="Templating Activity" ErrorCode="[brRespStatus]" sap2010:WorkflowViewState.IdRef="Template_Apply_4" Template="{}{ 'erpPersonIds': ['{{%erpPersonIds%}}'], 'includeUserProperties': true}" Text="[brFomat]">
                      <ias:Template_Apply.Values>
                        <scg:List x:TypeArguments="scg:List(x:String)" Capacity="4">
                          <scg:List x:TypeArguments="x:String" Capacity="4">
                            <x:String>erpPersonIds</x:String>
                            <x:String>erpAccountingEntity</x:String>
                          </scg:List>
                          <scg:List x:TypeArguments="x:String" Capacity="4">
                            <x:String>BuyerName</x:String>
                            <x:String>erpAccountingEntity</x:String>
                          </scg:List>
                        </scg:List>
                      </ias:Template_Apply.Values>
                    </ias:Template_Apply>
                    <ias:DeserializeJSON ErrorCode="{x:Null}" ContinueOnError="True" DisplayName="Deserialize JSON" sap2010:WorkflowViewState.IdRef="DeserializeJSON_4" JTokenObject="[brDesOpt]" JTokenString="[brFomat]" />
                    <iai:IONAPIRequestWizard FileAttachments="{x:Null}" ResponseCode="{x:Null}" sap2010:Annotation.AnnotationText="API call of erppersonids to get User_GUID value" ContentType="application_json" ContinueOnError="True" DisplayName="IONAPI Request" HttpMethod="POST" sap2010:WorkflowViewState.IdRef="IONAPIRequestWizard_8" PostData="[brDesOpt]" Response="[respOutput]" StatusCode="[brRespStatus]" Url="[TenantID+&quot;ifsservice/usermgt/v2/users/search/erppersonids&quot;]">
                      <iai:IONAPIRequestWizard.Headers>
                        <scg:List x:TypeArguments="scg:List(x:String)" Capacity="4">
                          <scg:List x:TypeArguments="x:String" Capacity="4">
                            <x:String>Accept</x:String>
                          </scg:List>
                          <scg:List x:TypeArguments="x:String" Capacity="4">
                            <x:String>application/json</x:String>
                          </scg:List>
                        </scg:List>
                      </iai:IONAPIRequestWizard.Headers>
                      <iai:IONAPIRequestWizard.QueryParameters>
                        <scg:List x:TypeArguments="scg:List(x:String)" Capacity="4">
                          <scg:List x:TypeArguments="x:String" Capacity="0" />
                          <scg:List x:TypeArguments="x:String" Capacity="0" />
                        </scg:List>
                      </iai:IONAPIRequestWizard.QueryParameters>
                    </iai:IONAPIRequestWizard>
                    <If Condition="[brRespStatus = 200]" sap2010:WorkflowViewState.IdRef="If_15">
                      <If.Then>
                        <If Condition="[JToken.Parse(respOutput.ReadAsText)(&quot;response&quot;)(&quot;userlist&quot;)(0)(&quot;id&quot;).tostring &lt;&gt; &quot;&quot;]" sap2010:WorkflowViewState.IdRef="If_14">
                          <If.Then>
                            <Sequence sap2010:WorkflowViewState.IdRef="Sequence_15">
                              <Assign DisplayName="Assign User_GUID" sap2010:WorkflowViewState.IdRef="Assign_23">
                                <Assign.To>
                                  <OutArgument x:TypeArguments="x:String">[User_GUID]</OutArgument>
                                </Assign.To>
                                <Assign.Value>
                                  <InArgument x:TypeArguments="x:String">[JToken.Parse(respOutput.ReadAsText)("response")("userlist")(0)("id").tostring]</InArgument>
                                </Assign.Value>
                              </Assign>
                            </Sequence>
                          </If.Then>
                        </If>
                      </If.Then>
                    </If>
                  </Sequence>
                </If.Then>
              </If>
              <If Condition="[APTeam.Contains(&quot;True&quot;)]" DisplayName="If - Apteam" sap2010:WorkflowViewState.IdRef="If_20" />
            </iad:CommentOut.Activities>
          </iad:CommentOut>
        </Sequence>
      </If.Else>
    </If>
    <sads:DebugSymbol.Symbol>d1JDOlxVc2Vyc1xkbWl0dGFwYWxsaVxBcHBEYXRhXExvY2FsXEluZm9yUlBBXE0zSW52b2ljZVByb2Nlc3NpbmdHZW5BSVYzXEFQUmVzcC54YW1se04D/wUOAgEBWj5acQIBA14LXjoCAQJiBWkOAwGsAWoFcw4DAagBdAV7DgMBpAF8BaUCEAIBWKYCBcUDFgIBV8YDBf0FCgIBBGc6Z08DAa8BZDtkSgMBrQFwC3A6AwGrAWwxbDkDAakBeTB5NAMBpwF2MXY8AwGlAX0H6AESAgFt6QEHpAIMAgFZxgMTxgMsAgEFyAMJzwMSAgFT0gMJ+wUUAgEHfgmFARIDAZ4BhgEJjQESAwGYAY4BCZUBEgMBkgGWAQmdARIDAYwBngEJtwEOAgF7uAEJvwESAgF1wAEJ5wEjAgFu6QEV6QEqAgFa6wELogIWAgFczQM0zQM7AgFWygM1ygM9AgFU0wML7AMQAgFI7QMLhAUQAgEKhQULkAUcAgEJkQUL+gUcAgEIgwE0gwFnAwGhAYABNYABPwMBnwGLATSLAWYDAZsBiAE1iAE/AwGZAZMBNJMBZAMBlQGQATWQAT4DAZMBmwE0mwFnAwGPAZgBNZgBPwMBjQGeAReeAbABAgF8oAENqQEYAwGGAawBDbUBGAMBgAG9ATS9AXECAXi6ATW6AT8CAXbAAZ8CwAGrAgIBc8ABywLAAYYDAgFxwAG3AsABxgICAW/sAQ3zARYCAWn0AQ2NAhICAV6OAg2hAh4CAV3TAz7TA34CAUnVAw/eAxgCAU/hAw/qAxoCAUrtAxntAzsCAQvvAw+CBRkCAQ2hAQ+oARgDAYcBrQEPtAEYAwGBAfEBOvEBXQIBbO4BO+4BSwIBavQBQPQBgAECAV/2ARH/ARoCAWWCAhGLAhwCAWDbAxXbA0QCAVLXAzvXA0YCAVDiAxHpAxoCAUvvA44B7wOfAQIBRvQDE4AFHgIBDqYBOqYBSQMBigGjATujAUUDAYgBsgE6sgFCAwGEAa8BO68BRQMBggH8ARf8AUYCAWj4AT34AUUCAWaDAhOKAhwCAWHnA0bnA4ABAgFO5ANH5ANWAgFM+AMVhQQqAgFAhgQVhgTeAQIBO4cEFZgELwIBM5kEFf8EGgIBD4gCPogCYwIBZIUCP4UCRwIBYvgDiwL4A5YCAgFE+AO5AfgDhQICAUP4A2z4A3wCAUGGBNABhgTbAQIBPoYEtgGGBMIBAgE8hwTcAocE6AICATqHBPIChwSAAwIBOIcEoQOHBOkDAgE2hwSMA4cEnAMCATSZBCOZBDkCARCbBBnxBCQCARb0BBn9BCICARKcBBvaBCACARjbBBvwBCwCARf6BB/6BE4CARX2BEX2BFACAROcBCmcBJ0BAgEZngQfzAQqAgEezwQf2AQoAgEanwQhqAQqAgEvqQQhywQrAgEf1QQl1QRUAgEd0QRL0QRWAgEbpQQnpQRWAgEyoQRNoQRYAgEwqQSgAakE8wECAS6uBCXJBDACASCvBCfIBCwCASGvBDWvBFgCASKxBCu6BDYCASm9BCvGBDYCASSyBC25BDYCASq+BC3FBDYCASW3BFi3BIIBAgEttARZtARkAgErwwRYwwSnAQIBKMAEWcAEZAIBJg==</sads:DebugSymbol.Symbol>
  </Sequence>
  <sap2010:WorkflowViewState.ViewStateManager>
    <sap2010:ViewStateManager>
      <sap2010:ViewStateData Id="Assign_29" sap:VirtualizedContainerService.HintSize="1655,60" />
      <sap2010:ViewStateData Id="Assign_9" sap:VirtualizedContainerService.HintSize="1655,60" />
      <sap2010:ViewStateData Id="Assign_14" sap:VirtualizedContainerService.HintSize="1655,60" />
      <sap2010:ViewStateData Id="Assign_48" sap:VirtualizedContainerService.HintSize="554,62" />
      <sap2010:ViewStateData Id="Assign_49" sap:VirtualizedContainerService.HintSize="554,62" />
      <sap2010:ViewStateData Id="Assign_50" sap:VirtualizedContainerService.HintSize="554,62" />
      <sap2010:ViewStateData Id="Assign_51" sap:VirtualizedContainerService.HintSize="554,62" />
      <sap2010:ViewStateData Id="Assign_70" sap:VirtualizedContainerService.HintSize="242,62" />
      <sap2010:ViewStateData Id="Sequence_39" sap:VirtualizedContainerService.HintSize="264,186">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">True</x:Boolean>
            <x:Boolean x:Key="IsPinned">False</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="Assign_71" sap:VirtualizedContainerService.HintSize="242,62" />
      <sap2010:ViewStateData Id="Sequence_40" sap:VirtualizedContainerService.HintSize="264,186">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">True</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="If_39" sap:VirtualizedContainerService.HintSize="554,340">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">True</x:Boolean>
            <x:Boolean x:Key="IsPinned">False</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="Assign_67" sap:VirtualizedContainerService.HintSize="554,62" />
      <sap2010:ViewStateData Id="IONAPIRequestWizard_20" sap:VirtualizedContainerService.HintSize="554,22" />
      <sap2010:ViewStateData Id="Sequence_26" sap:VirtualizedContainerService.HintSize="680,1036">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">True</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="Assign_52" sap:VirtualizedContainerService.HintSize="532,62" />
      <sap2010:ViewStateData Id="Assign_54" sap:VirtualizedContainerService.HintSize="242,62" />
      <sap2010:ViewStateData Id="Assign_53" sap:VirtualizedContainerService.HintSize="242,62" />
      <sap2010:ViewStateData Id="Sequence_27" sap:VirtualizedContainerService.HintSize="264,186">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">True</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="If_30" sap:VirtualizedContainerService.HintSize="532,364.666666666667">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsAnnotationDocked">True</x:Boolean>
            <x:Boolean x:Key="IsExpanded">True</x:Boolean>
            <x:Boolean x:Key="IsPinned">False</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="Assign_10" sap:VirtualizedContainerService.HintSize="242,62" />
      <sap2010:ViewStateData Id="Assign_11" sap:VirtualizedContainerService.HintSize="242,62" />
      <sap2010:ViewStateData Id="CommentOut_4" sap:VirtualizedContainerService.HintSize="532,77.3333333333333">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsAnnotationDocked">True</x:Boolean>
            <x:Boolean x:Key="IsExpanded">False</x:Boolean>
            <x:Boolean x:Key="IsPinned">False</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="Sequence_5" sap:VirtualizedContainerService.HintSize="554,708">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">True</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="If_3" sap:VirtualizedContainerService.HintSize="680,862" />
      <sap2010:ViewStateData Id="Sequence_6" sap:VirtualizedContainerService.HintSize="1655,2062">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">True</x:Boolean>
            <x:Boolean x:Key="IsPinned">False</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="IONAPIRequestWizard_3" sap:VirtualizedContainerService.HintSize="464,22" />
      <sap2010:ViewStateData Id="Assign_12" sap:VirtualizedContainerService.HintSize="242,62" />
      <sap2010:ViewStateData Id="Assign_13" sap:VirtualizedContainerService.HintSize="242,62" />
      <sap2010:ViewStateData Id="Sequence_7" sap:VirtualizedContainerService.HintSize="264,288">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">True</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="If_4" sap:VirtualizedContainerService.HintSize="464,442" />
      <sap2010:ViewStateData Id="Sequence_8" sap:VirtualizedContainerService.HintSize="486,628">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">True</x:Boolean>
            <x:Boolean x:Key="IsPinned">False</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="If_5" sap:VirtualizedContainerService.HintSize="200,52.6666666666667">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">False</x:Boolean>
            <x:Boolean x:Key="IsPinned">False</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="Template_Apply_1" sap:VirtualizedContainerService.HintSize="528.666666666667,22" />
      <sap2010:ViewStateData Id="Assign_2" sap:VirtualizedContainerService.HintSize="528.666666666667,62" />
      <sap2010:ViewStateData Id="DeserializeJSON_1" sap:VirtualizedContainerService.HintSize="528.666666666667,22" />
      <sap2010:ViewStateData Id="IONAPIRequestWizard_1" sap:VirtualizedContainerService.HintSize="528.666666666667,22" />
      <sap2010:ViewStateData Id="Assign_6" sap:VirtualizedContainerService.HintSize="242,62" />
      <sap2010:ViewStateData Id="Assign_7" sap:VirtualizedContainerService.HintSize="242,62" />
      <sap2010:ViewStateData Id="If_1" sap:VirtualizedContainerService.HintSize="510,216">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">True</x:Boolean>
            <x:Boolean x:Key="IsPinned">False</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="Assign_5" sap:VirtualizedContainerService.HintSize="242,62" />
      <sap2010:ViewStateData Id="Catch`1_1" sap:VirtualizedContainerService.HintSize="514.666666666667,22">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">False</x:Boolean>
            <x:Boolean x:Key="IsPinned">False</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="TryCatch_1" sap:VirtualizedContainerService.HintSize="528.666666666667,454" />
      <sap2010:ViewStateData Id="Sequence_4" sap:VirtualizedContainerService.HintSize="550.666666666667,866">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">True</x:Boolean>
            <x:Boolean x:Key="IsPinned">False</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="If_21" sap:VirtualizedContainerService.HintSize="200,52.6666666666667">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">False</x:Boolean>
            <x:Boolean x:Key="IsPinned">False</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="CommentOut_3" sap:VirtualizedContainerService.HintSize="1655.33333333333,94">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsAnnotationDocked">True</x:Boolean>
            <x:Boolean x:Key="IsExpanded">False</x:Boolean>
            <x:Boolean x:Key="IsPinned">False</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="Assign_8" sap:VirtualizedContainerService.HintSize="242,62" />
      <sap2010:ViewStateData Id="Assign_59" sap:VirtualizedContainerService.HintSize="242,62" />
      <sap2010:ViewStateData Id="Assign_60" sap:VirtualizedContainerService.HintSize="242,62" />
      <sap2010:ViewStateData Id="Sequence_30" sap:VirtualizedContainerService.HintSize="264,186">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">True</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="If_34" sap:VirtualizedContainerService.HintSize="1365.33333333333,364.666666666667">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsAnnotationDocked">True</x:Boolean>
            <x:Boolean x:Key="IsExpanded">True</x:Boolean>
            <x:Boolean x:Key="IsPinned">False</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="Template_Apply_7" sap:VirtualizedContainerService.HintSize="1186.66666666667,22" />
      <sap2010:ViewStateData Id="DeserializeJSON_7" sap:VirtualizedContainerService.HintSize="1186.66666666667,22" />
      <sap2010:ViewStateData Id="IONAPIRequestWizard_21" sap:VirtualizedContainerService.HintSize="1186.66666666667,22">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsAnnotationDocked">False</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="Assign_66" sap:VirtualizedContainerService.HintSize="606.666666666667,62" />
      <sap2010:ViewStateData Id="Assign_58" sap:VirtualizedContainerService.HintSize="242,62" />
      <sap2010:ViewStateData Id="Sequence_35" sap:VirtualizedContainerService.HintSize="264,186">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">True</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="Assign_65" sap:VirtualizedContainerService.HintSize="242,62" />
      <sap2010:ViewStateData Id="Sequence_34" sap:VirtualizedContainerService.HintSize="264,186">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">True</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="If_37" sap:VirtualizedContainerService.HintSize="554,340" />
      <sap2010:ViewStateData Id="Sequence_33" sap:VirtualizedContainerService.HintSize="576,464">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">True</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="ForEach`1_6" sap:VirtualizedContainerService.HintSize="606.666666666667,616.666666666667" />
      <sap2010:ViewStateData Id="Sequence_32" sap:VirtualizedContainerService.HintSize="628.666666666667,842.666666666667">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">True</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="Assign_61" sap:VirtualizedContainerService.HintSize="242,62" />
      <sap2010:ViewStateData Id="If_35" sap:VirtualizedContainerService.HintSize="896.666666666667,996.666666666667" />
      <sap2010:ViewStateData Id="Assign_57" sap:VirtualizedContainerService.HintSize="242,62" />
      <sap2010:ViewStateData Id="If_31" sap:VirtualizedContainerService.HintSize="464,216" />
      <sap2010:ViewStateData Id="Sequence_28" sap:VirtualizedContainerService.HintSize="486,340">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">True</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="If_32" sap:VirtualizedContainerService.HintSize="612,494">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">True</x:Boolean>
            <x:Boolean x:Key="IsPinned">False</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="CommentOut_5" sap:VirtualizedContainerService.HintSize="896.666666666667,590" />
      <sap2010:ViewStateData Id="Sequence_31" sap:VirtualizedContainerService.HintSize="918.666666666667,1750.66666666667">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">True</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="Assign_63" sap:VirtualizedContainerService.HintSize="242,62" />
      <sap2010:ViewStateData Id="If_33" sap:VirtualizedContainerService.HintSize="1186.66666666667,1904.66666666667">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">True</x:Boolean>
            <x:Boolean x:Key="IsPinned">False</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="Sequence_29" sap:VirtualizedContainerService.HintSize="1208.66666666667,2214.66666666667">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">True</x:Boolean>
            <x:Boolean x:Key="IsPinned">False</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="ForEach`1_3" sap:VirtualizedContainerService.HintSize="1239.33333333333,2367.33333333333">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">True</x:Boolean>
            <x:Boolean x:Key="IsPinned">False</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="If_36" sap:VirtualizedContainerService.HintSize="1365.33333333333,2521.33333333333" />
      <sap2010:ViewStateData Id="Assign_36" sap:VirtualizedContainerService.HintSize="242,62" />
      <sap2010:ViewStateData Id="CommentOut_2" sap:VirtualizedContainerService.HintSize="1365.33333333333,58">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">False</x:Boolean>
            <x:Boolean x:Key="IsPinned">False</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="Assign_21" sap:VirtualizedContainerService.HintSize="590,61.3333333333333" />
      <sap2010:ViewStateData Id="IONAPIRequestWizard_7" sap:VirtualizedContainerService.HintSize="590,22" />
      <sap2010:ViewStateData Id="Assign_22" sap:VirtualizedContainerService.HintSize="590,61.3333333333333" />
      <sap2010:ViewStateData Id="Template_Apply_4" sap:VirtualizedContainerService.HintSize="590,22" />
      <sap2010:ViewStateData Id="DeserializeJSON_4" sap:VirtualizedContainerService.HintSize="590,22" />
      <sap2010:ViewStateData Id="IONAPIRequestWizard_8" sap:VirtualizedContainerService.HintSize="590,22">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsAnnotationDocked">False</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="Assign_23" sap:VirtualizedContainerService.HintSize="242,61.3333333333333" />
      <sap2010:ViewStateData Id="Sequence_15" sap:VirtualizedContainerService.HintSize="264,185.333333333333">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">True</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="If_14" sap:VirtualizedContainerService.HintSize="464,337.333333333333">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">True</x:Boolean>
            <x:Boolean x:Key="IsPinned">False</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="If_15" sap:VirtualizedContainerService.HintSize="590,489.333333333333">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">True</x:Boolean>
            <x:Boolean x:Key="IsPinned">False</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="Sequence_16" sap:VirtualizedContainerService.HintSize="612,1064">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">True</x:Boolean>
            <x:Boolean x:Key="IsPinned">False</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="If_16" sap:VirtualizedContainerService.HintSize="464,52.6666666666667">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">False</x:Boolean>
            <x:Boolean x:Key="IsPinned">False</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="If_20" sap:VirtualizedContainerService.HintSize="464,214">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">True</x:Boolean>
            <x:Boolean x:Key="IsPinned">False</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="CommentOut_1" sap:VirtualizedContainerService.HintSize="1365.33333333333,58">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">False</x:Boolean>
            <x:Boolean x:Key="IsPinned">False</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="Sequence_19" sap:VirtualizedContainerService.HintSize="1387.33333333333,3246">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">True</x:Boolean>
            <x:Boolean x:Key="IsPinned">False</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="If_2" sap:VirtualizedContainerService.HintSize="1655.33333333333,3400" />
      <sap2010:ViewStateData Id="Sequence_3" sap:VirtualizedContainerService.HintSize="1677,6060">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">True</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="RehostedWorkflowDesigner.Workflow_1" sap:VirtualizedContainerService.HintSize="1717,6140" />
    </sap2010:ViewStateManager>
  </sap2010:WorkflowViewState.ViewStateManager>
</Activity>