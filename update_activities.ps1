# PowerShell script to update remaining activities with DisplayName attributes
$filePath = "ProcessPOInvoiceAPI_Ind.xaml"
$content = Get-Content $filePath -Raw

# Define replacements for remaining Assign activities
$replacements = @{
    '<Assign sap2010:WorkflowViewState.IdRef="Assign_1109">' = '<Assign DisplayName="ProcessPOInvoiceAPI_Ind_Assign_commentStatus_482" sap2010:WorkflowViewState.IdRef="Assign_1109">'
    '<Assign sap2010:WorkflowViewState.IdRef="Assign_1110">' = '<Assign DisplayName="ProcessPOInvoiceAPI_Ind_Assign_Status_483" sap2010:WorkflowViewState.IdRef="Assign_1110">'
    '<Assign sap2010:WorkflowViewState.IdRef="Assign_1111">' = '<Assign DisplayName="ProcessPOInvoiceAPI_Ind_Assign_commentStatus_484" sap2010:WorkflowViewState.IdRef="Assign_1111">'
    '<Assign sap2010:WorkflowViewState.IdRef="Assign_767">' = '<Assign DisplayName="ProcessPOInvoiceAPI_Ind_Assign_optimizerSuccess_485" sap2010:WorkflowViewState.IdRef="Assign_767">'
    '<Assign sap2010:WorkflowViewState.IdRef="Assign_914">' = '<Assign DisplayName="ProcessPOInvoiceAPI_Ind_Assign_APResp_486" sap2010:WorkflowViewState.IdRef="Assign_914">'
    '<Assign sap2010:WorkflowViewState.IdRef="Assign_913">' = '<Assign DisplayName="ProcessPOInvoiceAPI_Ind_Assign_APResp_487" sap2010:WorkflowViewState.IdRef="Assign_913">'
    '<Assign sap2010:WorkflowViewState.IdRef="Assign_912">' = '<Assign DisplayName="ProcessPOInvoiceAPI_Ind_Assign_APResp_488" sap2010:WorkflowViewState.IdRef="Assign_912">'
    '<Assign sap2010:WorkflowViewState.IdRef="Assign_771">' = '<Assign DisplayName="ProcessPOInvoiceAPI_Ind_Assign_inbnValue_489" sap2010:WorkflowViewState.IdRef="Assign_771">'
    '<Assign sap2010:WorkflowViewState.IdRef="Assign_772">' = '<Assign DisplayName="ProcessPOInvoiceAPI_Ind_Assign_bkid_490" sap2010:WorkflowViewState.IdRef="Assign_772">'
    '<Assign sap2010:WorkflowViewState.IdRef="Assign_773">' = '<Assign DisplayName="ProcessPOInvoiceAPI_Ind_Assign_correlationID_491" sap2010:WorkflowViewState.IdRef="Assign_773">'
}

# Apply replacements
foreach ($old in $replacements.Keys) {
    $content = $content -replace [regex]::Escape($old), $replacements[$old]
}

# Save the updated content
Set-Content $filePath $content -Encoding UTF8
Write-Host "Updated activities with DisplayName attributes"