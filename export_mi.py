#!/usr/bin/env python3
"""
ExportMI Python Script
Converted from ExportMI.xaml workflow

This script retrieves purchase order information from M3 ERP system
using the EXPORTMI/Select API endpoint.
"""

import requests
import json
import logging
from typing import List, Dict, Optional, Tuple
from dataclasses import dataclass


@dataclass
class ExportMIResult:
    """Result object for ExportMI operation"""
    vendor_id: str = ""
    company: str = ""
    division: str = ""
    currency_code: str = ""
    payment_terms: str = ""
    payment_method: str = ""
    status: str = ""
    comment_status: str = ""
    result_list: List[str] = None
    
    def __post_init__(self):
        if self.result_list is None:
            self.result_list = []


class ExportMIClient:
    """Client for ExportMI API operations"""
    
    def __init__(self, tenant_id: str, log_file: Optional[str] = None):
        """
        Initialize ExportMI client
        
        Args:
            tenant_id: The tenant ID for API calls
            log_file: Optional log file path for logging
        """
        self.tenant_id = tenant_id
        self.log_file = log_file
        self.base_url = f"{tenant_id}M3/m3api-rest/v2/execute/EXPORTMI/Select"
        
        # Setup logging
        logging.basicConfig(level=logging.INFO)
        self.logger = logging.getLogger(__name__)
        
        if log_file:
            file_handler = logging.FileHandler(log_file)
            file_handler.setLevel(logging.INFO)
            formatter = logging.Formatter('%(asctime)s - %(message)s')
            file_handler.setFormatter(formatter)
            self.logger.addHandler(file_handler)
    
    def _log_message(self, message: str):
        """Log message to both logger and file if specified"""
        self.logger.info(message)
        if self.log_file:
            try:
                with open(self.log_file, 'a', encoding='utf-8') as f:
                    f.write(f"{message}\n")
            except Exception as e:
                self.logger.error(f"Failed to write to log file: {e}")
    
    def get_po_details(self, po_number: str) -> ExportMIResult:
        """
        Get purchase order details from M3 ERP system
        
        Args:
            po_number: Purchase order number to query
            
        Returns:
            ExportMIResult object with PO details or error status
        """
        result = ExportMIResult()
        
        try:
            # Build the SQL query for MPHEAD table
            query = f"IACONO,IADIVI,IASUNO,IACUCD,IATEPY,IAPYME from MPHEAD where IAPUNO = '{po_number}'"
            
            # Prepare API request
            url = f"{self.base_url}"
            
            headers = {
                'Accept': 'application/json',
                'Content-Type': 'application/json'
            }
            
            params = {
                'QERY': query,
                'SEPC': '~',
                'HDRS': '0',
                'dateformat': 'YMD8',
                'excludeempty': 'false',
                'righttrim': 'true',
                'format': 'PRETTY',
                'extendedresult': 'false'
            }
            
            self._log_message(f"Making API call to retrieve PO details for: {po_number}")
            
            # Make API request
            response = requests.get(url, headers=headers, params=params, timeout=30)
            
            if response.status_code == 200:
                self._process_successful_response(response, result)
            else:
                self._handle_api_error(response, po_number, result)
                
        except requests.exceptions.RequestException as e:
            self._handle_request_exception(e, po_number, result)
        except Exception as e:
            self._handle_general_exception(e, po_number, result)
        
        return result
    
    def _process_successful_response(self, response: requests.Response, result: ExportMIResult):
        """Process successful API response"""
        try:
            response_data = response.json()
            
            # Check if records exist
            records = response_data.get('results', [{}])[0].get('records', [])
            
            if not records:
                result.status = "NEEDSVERIFICATION"
                result.comment_status = "PO number is not available"
                self._log_message(result.comment_status)
            else:
                self._extract_po_data(records[0], result)
                
        except (json.JSONDecodeError, KeyError, IndexError) as e:
            result.status = "FAILURE"
            result.comment_status = f"Failed to parse API response: {str(e)}"
            self._log_message(result.comment_status)
    
    def _extract_po_data(self, record: Dict, result: ExportMIResult):
        """Extract PO data from API response record"""
        try:
            # Parse the REPL field which contains tilde-separated values
            repl_data = record.get('REPL', '').split('~')
            
            if len(repl_data) >= 6:
                result.company = repl_data[0]
                result.division = repl_data[1] 
                result.vendor_id = repl_data[2]
                result.currency_code = repl_data[3]
                result.payment_terms = repl_data[4]
                result.payment_method = repl_data[5]
                
                # Build result list
                result.result_list = [
                    result.company,
                    result.division,
                    result.vendor_id,
                    result.currency_code,
                    result.payment_terms,
                    result.payment_method
                ]
                
                self._log_message("Extracted the company and division.")
                self._log_message(f"VendorID : {result.vendor_id}")
                
            else:
                result.status = "FAILURE"
                result.comment_status = "Invalid data format in API response"
                self._log_message(result.comment_status)
                
        except Exception as e:
            result.status = "FAILURE"
            result.comment_status = f"Failed to extract PO data: {str(e)}"
            self._log_message(result.comment_status)
    
    def _handle_api_error(self, response: requests.Response, po_number: str, result: ExportMIResult):
        """Handle API error responses"""
        result.status = "FAILURE"
        result.comment_status = f"Received an IONAPI error while fetching company number and division for the PO {po_number}."
        self._log_message(f"{result.comment_status} Status Code: {response.status_code}")
    
    def _handle_request_exception(self, exception: requests.exceptions.RequestException, po_number: str, result: ExportMIResult):
        """Handle request exceptions"""
        result.status = "FAILURE"
        result.comment_status = f"Network error while fetching data for PO {po_number}: {str(exception)}"
        self._log_message(result.comment_status)
    
    def _handle_general_exception(self, exception: Exception, po_number: str, result: ExportMIResult):
        """Handle general exceptions"""
        result.status = "FAILURE"
        result.comment_status = f"Unexpected error while processing PO {po_number}: {str(exception)}"
        self._log_message(result.comment_status)


def main():
    """Example usage of ExportMI client"""
    # Configuration
    tenant_id = "https://your-tenant.infor.com/"  # Replace with actual tenant ID
    log_file = "export_mi.log"
    po_number = "TEST01"  # Replace with actual PO number
    
    # Create client and get PO details
    client = ExportMIClient(tenant_id, log_file)
    result = client.get_po_details(po_number)
    
    # Display results
    print(f"Status: {result.status}")
    print(f"Comment: {result.comment_status}")
    
    if result.result_list:
        print(f"Company: {result.company}")
        print(f"Division: {result.division}")
        print(f"Vendor ID: {result.vendor_id}")
        print(f"Currency Code: {result.currency_code}")
        print(f"Payment Terms: {result.payment_terms}")
        print(f"Payment Method: {result.payment_method}")
        print(f"Result List: {result.result_list}")


if __name__ == "__main__":
    main()
