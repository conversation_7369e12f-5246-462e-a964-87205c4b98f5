﻿<Activity mc:Ignorable="sap sap2010 sads" x:Class="RehostedWorkflowDesigner.Workflow"
 xmlns="http://schemas.microsoft.com/netfx/2009/xaml/activities"
 xmlns:iai="clr-namespace:Infor.Activities.IONAPI;assembly=Infor.Activities.IONAPI"
 xmlns:ias="clr-namespace:Infor.Activities.Sys;assembly=Infor.Activities.Sys"
 xmlns:iru="clr-namespace:Infor.RPA.Utilities;assembly=Infor.RPA.Utilities"
 xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006"
 xmlns:mva="clr-namespace:Microsoft.VisualBasic.Activities;assembly=System.Activities"
 xmlns:njl="clr-namespace:Newtonsoft.Json.Linq;assembly=Newtonsoft.Json"
 xmlns:s="clr-namespace:System;assembly=mscorlib"
 xmlns:sads="http://schemas.microsoft.com/netfx/2010/xaml/activities/debugger"
 xmlns:sap="http://schemas.microsoft.com/netfx/2009/xaml/activities/presentation"
 xmlns:sap2010="http://schemas.microsoft.com/netfx/2010/xaml/activities/presentation"
 xmlns:scg="clr-namespace:System.Collections.Generic;assembly=mscorlib"
 xmlns:sco="clr-namespace:System.Collections.ObjectModel;assembly=mscorlib"
 xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml">
  <x:Members>
    <x:Property Name="tenantID" Type="InArgument(x:String)" />
    <x:Property Name="commentStatus" Type="OutArgument(x:String)" />
    <x:Property Name="Status" Type="OutArgument(x:String)" />
    <x:Property Name="logfile" Type="InArgument(x:String)" />
    <x:Property Name="M3TotalTableRows" Type="InArgument(scg:List(s:String[]))" />
    <x:Property Name="inbnValue" Type="InArgument(x:String)" />
    <x:Property Name="division" Type="InArgument(x:String)" />
    <x:Property Name="charge" Type="InArgument(x:String)" />
    <x:Property Name="chargeCode" Type="InArgument(x:String)" />
    <x:Property Name="chargeDiff" Type="OutArgument(x:String)" />
    <x:Property Name="DictOcrValues" Type="InArgument(scg:Dictionary(x:String, x:Object))" />
    <x:Property Name="emptyCharge" Type="InArgument(x:Boolean)" />
  </x:Members>
  <mva:VisualBasic.Settings>
    <x:Null />
  </mva:VisualBasic.Settings>
  <sap2010:WorkflowViewState.IdRef>RehostedWorkflowDesigner.Workflow_1</sap2010:WorkflowViewState.IdRef>
  <TextExpression.NamespacesForImplementation>
    <sco:Collection x:TypeArguments="x:String">
      <x:String>System.Activities</x:String>
      <x:String>System.Activities.Statements</x:String>
      <x:String>System.Activities.Expressions</x:String>
      <x:String>System.Activities.Validation</x:String>
      <x:String>System.Activities.XamlIntegration</x:String>
      <x:String>Infor.Activities.Email</x:String>
      <x:String>Microsoft.VisualBasic.Activities</x:String>
      <x:String>System.Activities.Debugger.Symbol</x:String>
      <x:String>System.Collections.Generic</x:String>
      <x:String>System.Windows.Markup</x:String>
      <x:String>Newtonsoft.Json.Linq</x:String>
      <x:String>Newtonsoft.Json</x:String>
      <x:String>Infor.RPA.Utilities</x:String>
      <x:String>System</x:String>
      <x:String>Microsoft.VisualBasic.CompilerServices</x:String>
      <x:String>System.Globalization</x:String>
    </sco:Collection>
  </TextExpression.NamespacesForImplementation>
  <TextExpression.ReferencesForImplementation>
    <sco:Collection x:TypeArguments="AssemblyReference">
      <AssemblyReference>System.Activities</AssemblyReference>
      <AssemblyReference>Infor.Activities.Email</AssemblyReference>
      <AssemblyReference>mscorlib</AssemblyReference>
      <AssemblyReference>PresentationFramework</AssemblyReference>
      <AssemblyReference>WindowsBase</AssemblyReference>
      <AssemblyReference>PresentationCore</AssemblyReference>
      <AssemblyReference>System.Xaml</AssemblyReference>
      <AssemblyReference>Newtonsoft.Json</AssemblyReference>
      <AssemblyReference>Infor.RPA.Utilities</AssemblyReference>
      <AssemblyReference>System.Core</AssemblyReference>
      <AssemblyReference>System</AssemblyReference>
      <AssemblyReference>System.ServiceModel</AssemblyReference>
      <AssemblyReference>System.Runtime.WindowsRuntime</AssemblyReference>
      <AssemblyReference>Microsoft.Bcl.AsyncInterfaces</AssemblyReference>
      <AssemblyReference>System.ComponentModel.Composition</AssemblyReference>
      <AssemblyReference>Microsoft.VisualBasic</AssemblyReference>
      <AssemblyReference>System.Memory</AssemblyReference>
    </sco:Collection>
  </TextExpression.ReferencesForImplementation>
  <Sequence DisplayName="AddCharge_Sequence_1" sap2010:WorkflowViewState.IdRef="Sequence_1">
    <Sequence.Variables>
      <Variable x:TypeArguments="x:String" Name="req2" />
      <Variable x:TypeArguments="iru:ResponseObject" Name="respObj2" />
      <Variable x:TypeArguments="x:Int32" Name="StatusCode2" />
      <Variable x:TypeArguments="iru:ResponseObject" Name="respObj8" />
      <Variable x:TypeArguments="x:String" Name="puno" />
      <Variable x:TypeArguments="x:String" Name="pnli" />
      <Variable x:TypeArguments="x:Decimal" Name="tot" />
      <Variable x:TypeArguments="x:Decimal" Name="totqty" />
      <Variable x:TypeArguments="x:String" Name="ceid" />
      <Variable x:TypeArguments="x:String" Name="cdse" />
      <Variable x:TypeArguments="x:String" Name="grpr" />
      <Variable x:TypeArguments="x:Decimal" Name="totqty1" />
      <Variable x:TypeArguments="x:String" Name="ivqa" />
      <Variable x:TypeArguments="x:Int32" Name="StatusCode8" />
      <Variable x:TypeArguments="x:String" Name="repn" />
      <Variable x:TypeArguments="njl:JToken" Name="out8" />
    </Sequence.Variables>
    <Assign DisplayName="AddCharge_Assign_2_tot" sap2010:WorkflowViewState.IdRef="Assign_21">
      <Assign.To>
        <OutArgument x:TypeArguments="x:Decimal">[tot]</OutArgument>
      </Assign.To>
      <Assign.Value>
        <InArgument x:TypeArguments="x:Decimal">[0]</InArgument>
      </Assign.Value>
    </Assign>
    <Assign DisplayName="AddCharge_Assign_3_totqty" sap2010:WorkflowViewState.IdRef="Assign_37">
      <Assign.To>
        <OutArgument x:TypeArguments="x:Decimal">[totqty]</OutArgument>
      </Assign.To>
      <Assign.Value>
        <InArgument x:TypeArguments="x:Decimal">[0]</InArgument>
      </Assign.Value>
    </Assign>
    <Assign DisplayName="AddCharge_Assign_4_totqty1" sap2010:WorkflowViewState.IdRef="Assign_68">
      <Assign.To>
        <OutArgument x:TypeArguments="x:Decimal">[totqty1]</OutArgument>
      </Assign.To>
      <Assign.Value>
        <InArgument x:TypeArguments="x:Decimal">[0]</InArgument>
      </Assign.Value>
    </Assign>
    <ForEach x:TypeArguments="s:String[]" DisplayName="AddCharge_ForEach_5" sap2010:WorkflowViewState.IdRef="ForEach`1_3" Values="[M3TotalTableRows]">
      <ActivityAction x:TypeArguments="s:String[]">
        <ActivityAction.Argument>
          <DelegateInArgument x:TypeArguments="s:String[]" Name="rows" />
        </ActivityAction.Argument>
        <Sequence DisplayName="AddCharge_Sequence_6" sap2010:WorkflowViewState.IdRef="Sequence_4">
          <If Condition="[rows(0).endswith(&quot;99&quot;)]" sap2010:WorkflowViewState.IdRef="If_37">
            <If.Then>
              <Sequence sap2010:WorkflowViewState.IdRef="Sequence_61">
                <Assign DisplayName="AddCharge_Assign_7_puno" sap2010:WorkflowViewState.IdRef="Assign_17">
                  <Assign.To>
                    <OutArgument x:TypeArguments="x:String">[puno]</OutArgument>
                  </Assign.To>
                  <Assign.Value>
                    <InArgument x:TypeArguments="x:String">[rows(6)]</InArgument>
                  </Assign.Value>
                </Assign>
                <Assign DisplayName="AddCharge_Assign_8_pnli" sap2010:WorkflowViewState.IdRef="Assign_18">
                  <Assign.To>
                    <OutArgument x:TypeArguments="x:String">[pnli]</OutArgument>
                  </Assign.To>
                  <Assign.Value>
                    <InArgument x:TypeArguments="x:String">[rows(0).Substring(0,rows(0).length-2)]</InArgument>
                  </Assign.Value>
                </Assign>
                <Assign sap2010:WorkflowViewState.IdRef="Assign_131">
                  <Assign.To>
                    <OutArgument x:TypeArguments="x:String">[repn]</OutArgument>
                  </Assign.To>
                  <Assign.Value>
                    <InArgument x:TypeArguments="x:String">[rows(1)]</InArgument>
                  </Assign.Value>
                </Assign>
                <Assign DisplayName="AddCharge_Assign_9_req2" sap2010:WorkflowViewState.IdRef="Assign_19">
                  <Assign.To>
                    <OutArgument x:TypeArguments="x:String">[req2]</OutArgument>
                  </Assign.To>
                  <Assign.Value>
                    <InArgument x:TypeArguments="x:String">["F3RCAC,F3CDSE,F3CEID from FGRPCL where F3PUNO = '" + puno +"' and F3SCOC != 0.000000 and F3PNLI = '" + pnli + "' and F3DIVI = '" + division + "' and F3REPN = '" + repn + "'"]</InArgument>
                  </Assign.Value>
                </Assign>
                <iai:IONAPIRequestWizard FileAttachments="{x:Null}" PostData="{x:Null}" ResponseCode="{x:Null}" ContentType="application_json" ContinueOnError="True" DisplayName="AddCharge_IONAPIRequestWizard_10" HttpMethod="GET" sap2010:WorkflowViewState.IdRef="IONAPIRequestWizard_5" Response="[respObj2]" StatusCode="[StatusCode2]" Url="[TenantID + &quot;M3/m3api-rest/v2/execute/EXPORTMI/Select?HDRS=0&amp;dateformat=YMD8&amp;excludeempty=false&amp;righttrim=true&amp;format=PRETTY&amp;extendedresult=false&quot;]">
                  <iai:IONAPIRequestWizard.Headers>
                    <scg:List x:TypeArguments="scg:List(x:String)" Capacity="4">
                      <scg:List x:TypeArguments="x:String" Capacity="4">
                        <x:String>Accept</x:String>
                      </scg:List>
                      <scg:List x:TypeArguments="x:String" Capacity="4">
                        <x:String>application/json</x:String>
                      </scg:List>
                    </scg:List>
                  </iai:IONAPIRequestWizard.Headers>
                  <iai:IONAPIRequestWizard.QueryParameters>
                    <scg:List x:TypeArguments="scg:List(x:String)" Capacity="4">
                      <scg:List x:TypeArguments="x:String" Capacity="4">
                        <x:String>QERY</x:String>
                        <x:String>SEPC</x:String>
                      </scg:List>
                      <scg:List x:TypeArguments="x:String" Capacity="4">
                        <x:String>req2</x:String>
                        <x:String>~</x:String>
                      </scg:List>
                    </scg:List>
                  </iai:IONAPIRequestWizard.QueryParameters>
                </iai:IONAPIRequestWizard>
                <If Condition="[StatusCode2 = 200]" DisplayName="AddCharge_If_11" sap2010:WorkflowViewState.IdRef="If_1">
                  <If.Then>
                    <Sequence DisplayName="AddCharge_Sequence_12" sap2010:WorkflowViewState.IdRef="Sequence_10">
                      <Sequence.Variables>
                        <Variable x:TypeArguments="njl:JToken" Name="out2" />
                      </Sequence.Variables>
                      <Assign DisplayName="AddCharge_Assign_13_out2" sap2010:WorkflowViewState.IdRef="Assign_10">
                        <Assign.To>
                          <OutArgument x:TypeArguments="njl:JToken">[out2]</OutArgument>
                        </Assign.To>
                        <Assign.Value>
                          <InArgument x:TypeArguments="njl:JToken">[JToken.Parse(respObj2.ReadAsText)]</InArgument>
                        </Assign.Value>
                      </Assign>
                      <If Condition="[out2(&quot;results&quot;)(0)(&quot;records&quot;).ToString &lt;&gt; &quot;[]&quot;]" DisplayName="AddCharge_If_14" sap2010:WorkflowViewState.IdRef="If_7">
                        <If.Then>
                          <Sequence DisplayName="AddCharge_Sequence_15" sap2010:WorkflowViewState.IdRef="Sequence_11">
                            <ForEach x:TypeArguments="njl:JToken" DisplayName="AddCharge_ForEach_16" sap2010:WorkflowViewState.IdRef="ForEach`1_6" Values="[out2(&quot;results&quot;)(0)(&quot;records&quot;)]">
                              <ActivityAction x:TypeArguments="njl:JToken">
                                <ActivityAction.Argument>
                                  <DelegateInArgument x:TypeArguments="njl:JToken" Name="item" />
                                </ActivityAction.Argument>
                                <Assign DisplayName="AddCharge_Assign_17_tot" sap2010:WorkflowViewState.IdRef="Assign_20">
                                  <Assign.To>
                                    <OutArgument x:TypeArguments="x:Decimal">[tot]</OutArgument>
                                  </Assign.To>
                                  <Assign.Value>
                                    <InArgument x:TypeArguments="x:Decimal">[tot + Decimal.Parse(item("REPL").ToString.split("~"C)(0), New System.Globalization.CultureInfo("en-US"))]</InArgument>
                                  </Assign.Value>
                                </Assign>
                              </ActivityAction>
                            </ForEach>
                            <Assign DisplayName="AddCharge_Assign_18_totqty" sap2010:WorkflowViewState.IdRef="Assign_36">
                              <Assign.To>
                                <OutArgument x:TypeArguments="x:Decimal">[totqty]</OutArgument>
                              </Assign.To>
                              <Assign.Value>
                                <InArgument x:TypeArguments="x:Decimal">[totqty + Decimal.Parse(rows(3), New System.Globalization.CultureInfo("en-US"))]</InArgument>
                              </Assign.Value>
                            </Assign>
                          </Sequence>
                        </If.Then>
                        <If.Else>
                          <Assign DisplayName="AddCharge_Assign_19_totqty1" sap2010:WorkflowViewState.IdRef="Assign_67">
                            <Assign.To>
                              <OutArgument x:TypeArguments="x:Decimal">[totqty1]</OutArgument>
                            </Assign.To>
                            <Assign.Value>
                              <InArgument x:TypeArguments="x:Decimal">[totqty1 + Decimal.Parse(rows(3), New System.Globalization.CultureInfo("en-US"))]</InArgument>
                            </Assign.Value>
                          </Assign>
                        </If.Else>
                      </If>
                    </Sequence>
                  </If.Then>
                  <If.Else>
                    <Sequence DisplayName="AddCharge_Sequence_20" sap2010:WorkflowViewState.IdRef="Sequence_2">
                      <Assign DisplayName="AddCharge_Assign_21_commentStatus" sap2010:WorkflowViewState.IdRef="Assign_5">
                        <Assign.To>
                          <OutArgument x:TypeArguments="x:String">[commentStatus]</OutArgument>
                        </Assign.To>
                        <Assign.Value>
                          <InArgument x:TypeArguments="x:String">["Received an IONAPI error while fetching lines charges for the PO " + puno +"."]</InArgument>
                        </Assign.Value>
                      </Assign>
                      <Assign DisplayName="AddCharge_Assign_22_Status" sap2010:WorkflowViewState.IdRef="Assign_6">
                        <Assign.To>
                          <OutArgument x:TypeArguments="x:String">[Status]</OutArgument>
                        </Assign.To>
                        <Assign.Value>
                          <InArgument x:TypeArguments="x:String">["FAILURE"]</InArgument>
                        </Assign.Value>
                      </Assign>
                      <ias:Append_Line ErrorCode="{x:Null}" ContinueOnError="True" DisplayName="AddCharge_Append_Line_23" sap2010:WorkflowViewState.IdRef="Append_Line_1" Line="[commentStatus]" Source="[logfile]" />
                    </Sequence>
                  </If.Else>
                </If>
              </Sequence>
            </If.Then>
          </If>
        </Sequence>
      </ActivityAction>
    </ForEach>
    <Assign DisplayName="AddCharge_Assign_24_chargeDiff" sap2010:WorkflowViewState.IdRef="Assign_80">
      <Assign.To>
        <OutArgument x:TypeArguments="x:String">[chargeDiff]</OutArgument>
      </Assign.To>
      <Assign.Value>
        <InArgument x:TypeArguments="x:String" xml:space="preserve">[(Decimal.Parse(charge.ToString(), New System.Globalization.CultureInfo("en-US")) - 
 Decimal.Parse(tot.ToString(), New System.Globalization.CultureInfo("en-US"))
).ToString(New System.Globalization.CultureInfo("en-US"))]</InArgument>
      </Assign.Value>
    </Assign>
    <If Condition="[tot = Decimal.Parse(charge, New System.Globalization.CultureInfo(&quot;en-US&quot;)) OR tot &gt; Decimal.Parse(charge, New System.Globalization.CultureInfo(&quot;en-US&quot;)) OR tot &lt; Decimal.Parse(charge, New System.Globalization.CultureInfo(&quot;en-US&quot;))]" DisplayName="AddCharge_If_25" sap2010:WorkflowViewState.IdRef="If_2">
      <If.Then>
        <If Condition="[tot = 0 and False]" DisplayName="AddCharge_If_26" sap2010:WorkflowViewState.IdRef="If_30">
          <If.Then>
            <Sequence DisplayName="AddCharge_Sequence_27" sap2010:WorkflowViewState.IdRef="Sequence_41">
              <Sequence DisplayName="AddCharge_Sequence_28" sap2010:WorkflowViewState.IdRef="Sequence_40">
                <Assign DisplayName="AddCharge_Assign_29_ceid" sap2010:WorkflowViewState.IdRef="Assign_106">
                  <Assign.To>
                    <OutArgument x:TypeArguments="x:String">[ceid]</OutArgument>
                  </Assign.To>
                  <Assign.Value>
                    <InArgument x:TypeArguments="x:String">[chargeCode]</InArgument>
                  </Assign.Value>
                </Assign>
                <Assign DisplayName="AddCharge_Assign_30_cdse" sap2010:WorkflowViewState.IdRef="Assign_107">
                  <Assign.To>
                    <OutArgument x:TypeArguments="x:String">[cdse]</OutArgument>
                  </Assign.To>
                  <Assign.Value>
                    <InArgument x:TypeArguments="x:String">60</InArgument>
                  </Assign.Value>
                </Assign>
                <Assign DisplayName="AddCharge_Assign_31_grpr" sap2010:WorkflowViewState.IdRef="Assign_108">
                  <Assign.To>
                    <OutArgument x:TypeArguments="x:String">[grpr]</OutArgument>
                  </Assign.To>
                  <Assign.Value>
                    <InArgument x:TypeArguments="x:String">[Math.Round(((Decimal.Parse(charge, New System.Globalization.CultureInfo("en-US")))/totqty),4).ToString(New System.Globalization.CultureInfo("en-US"))]</InArgument>
                  </Assign.Value>
                </Assign>
                <Assign DisplayName="AddCharge_Assign_32_ivqa" sap2010:WorkflowViewState.IdRef="Assign_109">
                  <Assign.To>
                    <OutArgument x:TypeArguments="x:String">[ivqa]</OutArgument>
                  </Assign.To>
                  <Assign.Value>
                    <InArgument x:TypeArguments="x:String">[totqty.ToString]</InArgument>
                  </Assign.Value>
                </Assign>
              </Sequence>
              <iai:IONAPIRequestWizard FileAttachments="{x:Null}" PostData="{x:Null}" ResponseCode="{x:Null}" ContentType="application_json" ContinueOnError="True" DisplayName="AddCharge_IONAPIRequestWizard_33" HttpMethod="GET" sap2010:WorkflowViewState.IdRef="IONAPIRequestWizard_18" Response="[respObj8]" StatusCode="[StatusCode8]" Url="[TenantID+ &quot;M3/m3api-rest/v2/execute/APS450MI/AddLine&quot;]">
                <iai:IONAPIRequestWizard.Headers>
                  <scg:List x:TypeArguments="scg:List(x:String)" Capacity="4">
                    <scg:List x:TypeArguments="x:String" Capacity="4">
                      <x:String>Accept</x:String>
                    </scg:List>
                    <scg:List x:TypeArguments="x:String" Capacity="4">
                      <x:String>application/json</x:String>
                    </scg:List>
                  </scg:List>
                </iai:IONAPIRequestWizard.Headers>
                <iai:IONAPIRequestWizard.QueryParameters>
                  <scg:List x:TypeArguments="scg:List(x:String)" Capacity="4">
                    <scg:List x:TypeArguments="x:String" Capacity="8">
                      <x:String>INBN</x:String>
                      <x:String>RDTP</x:String>
                      <x:String>DIVI</x:String>
                      <x:String>CDSE</x:String>
                      <x:String>CEID</x:String>
                      <x:String>NLAM</x:String>
                    </scg:List>
                    <scg:List x:TypeArguments="x:String" Capacity="8">
                      <x:String>inbnValue</x:String>
                      <x:String>2</x:String>
                      <x:String>division</x:String>
                      <x:String>cdse</x:String>
                      <x:String>ceid</x:String>
                      <x:String>charge</x:String>
                    </scg:List>
                  </scg:List>
                </iai:IONAPIRequestWizard.QueryParameters>
              </iai:IONAPIRequestWizard>
            </Sequence>
          </If.Then>
          <If.Else>
            <ForEach x:TypeArguments="s:String[]" DisplayName="AddCharge_ForEach_34" sap2010:WorkflowViewState.IdRef="ForEach`1_13" Values="[M3TotalTableRows]">
              <ActivityAction x:TypeArguments="s:String[]">
                <ActivityAction.Argument>
                  <DelegateInArgument x:TypeArguments="s:String[]" Name="rows" />
                </ActivityAction.Argument>
                <Sequence DisplayName="AddCharge_Sequence_35" sap2010:WorkflowViewState.IdRef="Sequence_54">
                  <If Condition="[rows(0).endswith(&quot;99&quot;)]" sap2010:WorkflowViewState.IdRef="If_38">
                    <If.Then>
                      <Sequence sap2010:WorkflowViewState.IdRef="Sequence_62">
                        <Assign DisplayName="AddCharge_Assign_36_puno" sap2010:WorkflowViewState.IdRef="Assign_110">
                          <Assign.To>
                            <OutArgument x:TypeArguments="x:String">[puno]</OutArgument>
                          </Assign.To>
                          <Assign.Value>
                            <InArgument x:TypeArguments="x:String">[rows(6)]</InArgument>
                          </Assign.Value>
                        </Assign>
                        <Assign DisplayName="AddCharge_Assign_37_pnli" sap2010:WorkflowViewState.IdRef="Assign_111">
                          <Assign.To>
                            <OutArgument x:TypeArguments="x:String">[pnli]</OutArgument>
                          </Assign.To>
                          <Assign.Value>
                            <InArgument x:TypeArguments="x:String">[rows(0).Substring(0,rows(0).length-2)]</InArgument>
                          </Assign.Value>
                        </Assign>
                        <Assign sap2010:WorkflowViewState.IdRef="Assign_132">
                          <Assign.To>
                            <OutArgument x:TypeArguments="x:String">[repn]</OutArgument>
                          </Assign.To>
                          <Assign.Value>
                            <InArgument x:TypeArguments="x:String">[rows(1)]</InArgument>
                          </Assign.Value>
                        </Assign>
                        <Assign DisplayName="AddCharge_Assign_38_req2" sap2010:WorkflowViewState.IdRef="Assign_112">
                          <Assign.To>
                            <OutArgument x:TypeArguments="x:String">[req2]</OutArgument>
                          </Assign.To>
                          <Assign.Value>
                            <InArgument x:TypeArguments="x:String">["F3SCOC,F3CDSE,F3CEID from FGRPCL where F3PUNO = '" + puno +"' and F3SCOC != 0.000000 and F3PNLI = '" + pnli + "' and F3DIVI = '" + division + "' and F3REPN = '" + repn + "'"]</InArgument>
                          </Assign.Value>
                        </Assign>
                        <iai:IONAPIRequestWizard FileAttachments="{x:Null}" PostData="{x:Null}" ResponseCode="{x:Null}" ContentType="application_json" ContinueOnError="True" DisplayName="AddCharge_IONAPIRequestWizard_39" HttpMethod="GET" sap2010:WorkflowViewState.IdRef="IONAPIRequestWizard_19" Response="[respObj2]" StatusCode="[StatusCode2]" Url="[TenantID + &quot;M3/m3api-rest/v2/execute/EXPORTMI/Select?HDRS=0&amp;dateformat=YMD8&amp;excludeempty=false&amp;righttrim=true&amp;format=PRETTY&amp;extendedresult=false&quot;]">
                          <iai:IONAPIRequestWizard.Headers>
                            <scg:List x:TypeArguments="scg:List(x:String)" Capacity="4">
                              <scg:List x:TypeArguments="x:String" Capacity="4">
                                <x:String>Accept</x:String>
                              </scg:List>
                              <scg:List x:TypeArguments="x:String" Capacity="4">
                                <x:String>application/json</x:String>
                              </scg:List>
                            </scg:List>
                          </iai:IONAPIRequestWizard.Headers>
                          <iai:IONAPIRequestWizard.QueryParameters>
                            <scg:List x:TypeArguments="scg:List(x:String)" Capacity="4">
                              <scg:List x:TypeArguments="x:String" Capacity="4">
                                <x:String>QERY</x:String>
                                <x:String>SEPC</x:String>
                              </scg:List>
                              <scg:List x:TypeArguments="x:String" Capacity="4">
                                <x:String>req2</x:String>
                                <x:String>~</x:String>
                              </scg:List>
                            </scg:List>
                          </iai:IONAPIRequestWizard.QueryParameters>
                        </iai:IONAPIRequestWizard>
                        <If Condition="[StatusCode2 = 200]" DisplayName="AddCharge_If_40" sap2010:WorkflowViewState.IdRef="If_29">
                          <If.Then>
                            <Sequence DisplayName="AddCharge_Sequence_41" sap2010:WorkflowViewState.IdRef="Sequence_52">
                              <Sequence.Variables>
                                <Variable x:TypeArguments="x:String" Name="itno" />
                              </Sequence.Variables>
                              <Sequence DisplayName="AddCharge_Sequence_42" sap2010:WorkflowViewState.IdRef="Sequence_51">
                                <Sequence.Variables>
                                  <Variable x:TypeArguments="njl:JToken" Name="out2" />
                                </Sequence.Variables>
                                <Assign DisplayName="AddCharge_Assign_43_out2" sap2010:WorkflowViewState.IdRef="Assign_113">
                                  <Assign.To>
                                    <OutArgument x:TypeArguments="njl:JToken">[out2]</OutArgument>
                                  </Assign.To>
                                  <Assign.Value>
                                    <InArgument x:TypeArguments="njl:JToken">[JToken.Parse(respObj2.ReadAsText)]</InArgument>
                                  </Assign.Value>
                                </Assign>
                                <Assign DisplayName="AddCharge_Assign_44_itno" sap2010:WorkflowViewState.IdRef="Assign_114">
                                  <Assign.To>
                                    <OutArgument x:TypeArguments="x:String">[itno]</OutArgument>
                                  </Assign.To>
                                  <Assign.Value>
                                    <InArgument x:TypeArguments="x:String">[rows(5)]</InArgument>
                                  </Assign.Value>
                                </Assign>
                                <Assign DisplayName="AddCharge_Assign_45_repn" sap2010:WorkflowViewState.IdRef="Assign_115">
                                  <Assign.To>
                                    <OutArgument x:TypeArguments="x:String">[repn]</OutArgument>
                                  </Assign.To>
                                  <Assign.Value>
                                    <InArgument x:TypeArguments="x:String">[rows(1)]</InArgument>
                                  </Assign.Value>
                                </Assign>
                                <Assign DisplayName="AddCharge_Assign_46_ivqa" sap2010:WorkflowViewState.IdRef="Assign_116">
                                  <Assign.To>
                                    <OutArgument x:TypeArguments="x:String">[ivqa]</OutArgument>
                                  </Assign.To>
                                  <Assign.Value>
                                    <InArgument x:TypeArguments="x:String">[rows(3)]</InArgument>
                                  </Assign.Value>
                                </Assign>
                                <If Condition="[out2(&quot;results&quot;)(0)(&quot;records&quot;).ToString &lt;&gt; &quot;[]&quot;]" DisplayName="AddCharge_If_47" sap2010:WorkflowViewState.IdRef="If_28">
                                  <If.Then>
                                    <Sequence DisplayName="AddCharge_Sequence_48" sap2010:WorkflowViewState.IdRef="Sequence_44">
                                      <ForEach x:TypeArguments="njl:JToken" DisplayName="AddCharge_ForEach_49" sap2010:WorkflowViewState.IdRef="ForEach`1_12" Values="[out2(&quot;results&quot;)(0)(&quot;records&quot;)]">
                                        <ActivityAction x:TypeArguments="njl:JToken">
                                          <ActivityAction.Argument>
                                            <DelegateInArgument x:TypeArguments="njl:JToken" Name="item" />
                                          </ActivityAction.Argument>
                                          <Sequence DisplayName="AddCharge_Sequence_50" sap2010:WorkflowViewState.IdRef="Sequence_43">
                                            <Assign DisplayName="AddCharge_Assign_51_cdse" sap2010:WorkflowViewState.IdRef="Assign_117">
                                              <Assign.To>
                                                <OutArgument x:TypeArguments="x:String">[cdse]</OutArgument>
                                              </Assign.To>
                                              <Assign.Value>
                                                <InArgument x:TypeArguments="x:String">[item("REPL").ToString.split("~"C)(1).ToString()]</InArgument>
                                              </Assign.Value>
                                            </Assign>
                                            <Assign DisplayName="AddCharge_Assign_52_ceid" sap2010:WorkflowViewState.IdRef="Assign_118">
                                              <Assign.To>
                                                <OutArgument x:TypeArguments="x:String">[ceid]</OutArgument>
                                              </Assign.To>
                                              <Assign.Value>
                                                <InArgument x:TypeArguments="x:String">[item("REPL").ToString.split("~"C)(2).ToString()]</InArgument>
                                              </Assign.Value>
                                            </Assign>
                                            <If Condition="[tot = Decimal.Parse(charge, New System.Globalization.CultureInfo(&quot;en-US&quot;))]" DisplayName="AddCharge_If_53" sap2010:WorkflowViewState.IdRef="If_23">
                                              <If.Then>
                                                <Assign DisplayName="AddCharge_Assign_54_grpr" sap2010:WorkflowViewState.IdRef="Assign_119">
                                                  <Assign.To>
                                                    <OutArgument x:TypeArguments="x:String">[grpr]</OutArgument>
                                                  </Assign.To>
                                                  <Assign.Value>
                                                    <InArgument x:TypeArguments="x:String">[Math.Round((Decimal.Parse(item("REPL").ToString.split("~"C)(0), New System.Globalization.CultureInfo("en-US"))),4).ToString(New System.Globalization.CultureInfo("en-US"))]</InArgument>
                                                  </Assign.Value>
                                                </Assign>
                                              </If.Then>
                                              <If.Else>
                                                <Sequence DisplayName="AddCharge_Sequence_55" sap2010:WorkflowViewState.IdRef="Sequence_42">
                                                  <If Condition="[emptyCharge AND tot &gt; 0]" sap2010:WorkflowViewState.IdRef="If_31">
                                                    <If.Then>
                                                      <Assign sap2010:WorkflowViewState.IdRef="Assign_133">
                                                        <Assign.To>
                                                          <OutArgument x:TypeArguments="x:String">[grpr]</OutArgument>
                                                        </Assign.To>
                                                        <Assign.Value>
                                                          <InArgument x:TypeArguments="x:String">0</InArgument>
                                                        </Assign.Value>
                                                      </Assign>
                                                    </If.Then>
                                                    <If.Else>
                                                      <Assign sap2010:WorkflowViewState.IdRef="Assign_134">
                                                        <Assign.To>
                                                          <OutArgument x:TypeArguments="x:String">[grpr]</OutArgument>
                                                        </Assign.To>
                                                        <Assign.Value>
                                                          <InArgument x:TypeArguments="x:String">[Math.Round(((Decimal.Parse(item("REPL").ToString.split("~"C)(0), New System.Globalization.CultureInfo("en-US"))) - (tot - Decimal.Parse(charge, New System.Globalization.CultureInfo("en-US")))/totqty),4).ToString(New System.Globalization.CultureInfo("en-US"))]</InArgument>
                                                        </Assign.Value>
                                                      </Assign>
                                                    </If.Else>
                                                  </If>
                                                </Sequence>
                                              </If.Else>
                                            </If>
                                          </Sequence>
                                        </ActivityAction>
                                      </ForEach>
                                      <If Condition="[emptyCharge AND tot = 0]" sap2010:WorkflowViewState.IdRef="If_33">
                                        <If.Else>
                                          <Sequence sap2010:WorkflowViewState.IdRef="Sequence_57">
                                            <iai:IONAPIRequestWizard FileAttachments="{x:Null}" PostData="{x:Null}" ResponseCode="{x:Null}" ContentType="application_json" ContinueOnError="True" DisplayName="IONAPI Request for Adding lines" HttpMethod="GET" sap2010:WorkflowViewState.IdRef="IONAPIRequestWizard_23" Response="[respObj8]" StatusCode="[StatusCode8]" Url="[TenantID+ &quot;M3/m3api-rest/v2/execute/APS450MI/AddLine&quot;]">
                                              <iai:IONAPIRequestWizard.Headers>
                                                <scg:List x:TypeArguments="scg:List(x:String)" Capacity="4">
                                                  <scg:List x:TypeArguments="x:String" Capacity="4">
                                                    <x:String>Accept</x:String>
                                                  </scg:List>
                                                  <scg:List x:TypeArguments="x:String" Capacity="4">
                                                    <x:String>application/json</x:String>
                                                  </scg:List>
                                                </scg:List>
                                              </iai:IONAPIRequestWizard.Headers>
                                              <iai:IONAPIRequestWizard.QueryParameters>
                                                <scg:List x:TypeArguments="scg:List(x:String)" Capacity="4">
                                                  <scg:List x:TypeArguments="x:String" Capacity="16">
                                                    <x:String>INBN</x:String>
                                                    <x:String>RDTP</x:String>
                                                    <x:String>DIVI</x:String>
                                                    <x:String>CDSE</x:String>
                                                    <x:String>CEID</x:String>
                                                    <x:String>ITNO</x:String>
                                                    <x:String>PNLI</x:String>
                                                    <x:String>PUNO</x:String>
                                                    <x:String>REPN</x:String>
                                                    <x:String>IVQA</x:String>
                                                    <x:String>GRPR</x:String>
                                                  </scg:List>
                                                  <scg:List x:TypeArguments="x:String" Capacity="16">
                                                    <x:String>inbnValue</x:String>
                                                    <x:String>5</x:String>
                                                    <x:String>division</x:String>
                                                    <x:String>cdse</x:String>
                                                    <x:String>ceid</x:String>
                                                    <x:String>itno</x:String>
                                                    <x:String>pnli</x:String>
                                                    <x:String>puno</x:String>
                                                    <x:String>repn</x:String>
                                                    <x:String>ivqa</x:String>
                                                    <x:String>grpr</x:String>
                                                  </scg:List>
                                                </scg:List>
                                              </iai:IONAPIRequestWizard.QueryParameters>
                                            </iai:IONAPIRequestWizard>
                                            <Assign sap2010:WorkflowViewState.IdRef="Assign_135">
                                              <Assign.To>
                                                <OutArgument x:TypeArguments="njl:JToken">[out8]</OutArgument>
                                              </Assign.To>
                                              <Assign.Value>
                                                <InArgument x:TypeArguments="njl:JToken">[JToken.Parse(respObj8.ReadAsText)]</InArgument>
                                              </Assign.Value>
                                            </Assign>
                                            <If Condition="[out8(&quot;results&quot;)(0)(&quot;records&quot;).ToString = &quot;[]&quot;]" sap2010:WorkflowViewState.IdRef="If_32">
                                              <If.Then>
                                                <Sequence sap2010:WorkflowViewState.IdRef="Sequence_55">
                                                  <Assign sap2010:WorkflowViewState.IdRef="Assign_136">
                                                    <Assign.To>
                                                      <OutArgument x:TypeArguments="x:String">[commentStatus]</OutArgument>
                                                    </Assign.To>
                                                    <Assign.Value>
                                                      <InArgument x:TypeArguments="x:String">[(out8("results")(0)("errorMessage")).ToString]</InArgument>
                                                    </Assign.Value>
                                                  </Assign>
                                                  <ias:Append_Line ErrorCode="{x:Null}" ContinueOnError="True" DisplayName="Append Line" sap2010:WorkflowViewState.IdRef="Append_Line_22" Line="[commentStatus]" Source="[logfile]" />
                                                </Sequence>
                                              </If.Then>
                                              <If.Else>
                                                <Sequence sap2010:WorkflowViewState.IdRef="Sequence_56">
                                                  <Assign sap2010:WorkflowViewState.IdRef="Assign_137">
                                                    <Assign.To>
                                                      <OutArgument x:TypeArguments="x:String">[commentStatus]</OutArgument>
                                                    </Assign.To>
                                                    <Assign.Value>
                                                      <InArgument x:TypeArguments="x:String">Line charge added</InArgument>
                                                    </Assign.Value>
                                                  </Assign>
                                                  <ias:Append_Line ErrorCode="{x:Null}" ContinueOnError="True" DisplayName="Append Line" sap2010:WorkflowViewState.IdRef="Append_Line_23" Line="[commentStatus]" Source="[logfile]" />
                                                </Sequence>
                                              </If.Else>
                                            </If>
                                          </Sequence>
                                        </If.Else>
                                      </If>
                                    </Sequence>
                                  </If.Then>
                                  <If.Else>
                                    <Sequence DisplayName="AddCharge_Sequence_59" sap2010:WorkflowViewState.IdRef="Sequence_50">
                                      <If Condition="[tot = 0]" DisplayName="AddCharge_If_60" sap2010:WorkflowViewState.IdRef="If_27">
                                        <If.Then>
                                          <Sequence DisplayName="AddCharge_Sequence_61" sap2010:WorkflowViewState.IdRef="Sequence_49">
                                            <Assign DisplayName="AddCharge_Assign_62_ceid" sap2010:WorkflowViewState.IdRef="Assign_121">
                                              <Assign.To>
                                                <OutArgument x:TypeArguments="x:String">[ceid]</OutArgument>
                                              </Assign.To>
                                              <Assign.Value>
                                                <InArgument x:TypeArguments="x:String">[chargeCode]</InArgument>
                                              </Assign.Value>
                                            </Assign>
                                            <Assign DisplayName="AddCharge_Assign_63_req2" sap2010:WorkflowViewState.IdRef="Assign_122">
                                              <Assign.To>
                                                <OutArgument x:TypeArguments="x:String">[req2]</OutArgument>
                                              </Assign.To>
                                              <Assign.Value>
                                                <InArgument x:TypeArguments="x:String">["F3CDSE from FGRPCL where F3CEID = '" + ceid+"'"]</InArgument>
                                              </Assign.Value>
                                            </Assign>
                                            <iai:IONAPIRequestWizard FileAttachments="{x:Null}" PostData="{x:Null}" ResponseCode="{x:Null}" ContentType="application_json" ContinueOnError="True" DisplayName="AddCharge_IONAPIRequestWizard_64" HttpMethod="GET" sap2010:WorkflowViewState.IdRef="IONAPIRequestWizard_21" Response="[respObj2]" StatusCode="[StatusCode2]" Url="[TenantID + &quot;M3/m3api-rest/v2/execute/EXPORTMI/Select?HDRS=0&amp;dateformat=YMD8&amp;excludeempty=false&amp;righttrim=true&amp;format=PRETTY&amp;extendedresult=false&quot;]">
                                              <iai:IONAPIRequestWizard.Headers>
                                                <scg:List x:TypeArguments="scg:List(x:String)" Capacity="4">
                                                  <scg:List x:TypeArguments="x:String" Capacity="4">
                                                    <x:String>Accept</x:String>
                                                  </scg:List>
                                                  <scg:List x:TypeArguments="x:String" Capacity="4">
                                                    <x:String>application/json</x:String>
                                                  </scg:List>
                                                </scg:List>
                                              </iai:IONAPIRequestWizard.Headers>
                                              <iai:IONAPIRequestWizard.QueryParameters>
                                                <scg:List x:TypeArguments="scg:List(x:String)" Capacity="4">
                                                  <scg:List x:TypeArguments="x:String" Capacity="4">
                                                    <x:String>QERY</x:String>
                                                    <x:String>SEPC</x:String>
                                                  </scg:List>
                                                  <scg:List x:TypeArguments="x:String" Capacity="4">
                                                    <x:String>req2</x:String>
                                                    <x:String>~</x:String>
                                                  </scg:List>
                                                </scg:List>
                                              </iai:IONAPIRequestWizard.QueryParameters>
                                            </iai:IONAPIRequestWizard>
                                            <If Condition="[StatusCode2 = 200]" DisplayName="AddCharge_If_65" sap2010:WorkflowViewState.IdRef="If_26">
                                              <If.Then>
                                                <Sequence DisplayName="AddCharge_Sequence_66" sap2010:WorkflowViewState.IdRef="Sequence_47">
                                                  <Assign DisplayName="AddCharge_Assign_67_out2" sap2010:WorkflowViewState.IdRef="Assign_123">
                                                    <Assign.To>
                                                      <OutArgument x:TypeArguments="njl:JToken">[out2]</OutArgument>
                                                    </Assign.To>
                                                    <Assign.Value>
                                                      <InArgument x:TypeArguments="njl:JToken">[JToken.Parse(respObj2.ReadAsText)]</InArgument>
                                                    </Assign.Value>
                                                  </Assign>
                                                  <If Condition="[out2(&quot;results&quot;)(0)(&quot;records&quot;).ToString &lt;&gt; &quot;[]&quot;]" DisplayName="AddCharge_If_68" sap2010:WorkflowViewState.IdRef="If_24">
                                                    <If.Then>
                                                      <Sequence DisplayName="AddCharge_Sequence_69" sap2010:WorkflowViewState.IdRef="Sequence_45">
                                                        <Assign DisplayName="AddCharge_Assign_70_cdse" sap2010:WorkflowViewState.IdRef="Assign_124">
                                                          <Assign.To>
                                                            <OutArgument x:TypeArguments="x:String">[cdse]</OutArgument>
                                                          </Assign.To>
                                                          <Assign.Value>
                                                            <InArgument x:TypeArguments="x:String">[out2("results")(0)("records")(0)("REPL").ToString.split("~"C)(0)]</InArgument>
                                                          </Assign.Value>
                                                        </Assign>
                                                      </Sequence>
                                                    </If.Then>
                                                    <If.Else>
                                                      <ias:Append_Line ErrorCode="{x:Null}" ContinueOnError="True" DisplayName="AddCharge_Append_Line_71" sap2010:WorkflowViewState.IdRef="Append_Line_17" Line="[&quot;No record available for the charge code: &quot; + ceid]" Source="[logfile]" />
                                                    </If.Else>
                                                  </If>
                                                  <If Condition="[cdse &lt;&gt; &quot;&quot;]" DisplayName="AddCharge_If_72" sap2010:WorkflowViewState.IdRef="If_25">
                                                    <If.Then>
                                                      <Sequence DisplayName="AddCharge_Sequence_73" sap2010:WorkflowViewState.IdRef="Sequence_46">
                                                        <Assign DisplayName="AddCharge_Assign_74_cdse" sap2010:WorkflowViewState.IdRef="Assign_125">
                                                          <Assign.To>
                                                            <OutArgument x:TypeArguments="x:String">[cdse]</OutArgument>
                                                          </Assign.To>
                                                          <Assign.Value>
                                                            <InArgument x:TypeArguments="x:String">[out2("results")(0)("records")(0)("REPL").ToString.split("~"C)(0)]</InArgument>
                                                          </Assign.Value>
                                                        </Assign>
                                                        <If Condition="[emptyCharge AND tot &gt; 0]" sap2010:WorkflowViewState.IdRef="If_34">
                                                          <If.Then>
                                                            <Assign sap2010:WorkflowViewState.IdRef="Assign_138">
                                                              <Assign.To>
                                                                <OutArgument x:TypeArguments="x:String">[grpr]</OutArgument>
                                                              </Assign.To>
                                                              <Assign.Value>
                                                                <InArgument x:TypeArguments="x:String">0</InArgument>
                                                              </Assign.Value>
                                                            </Assign>
                                                          </If.Then>
                                                          <If.Else>
                                                            <Assign sap2010:WorkflowViewState.IdRef="Assign_139">
                                                              <Assign.To>
                                                                <OutArgument x:TypeArguments="x:String">[grpr]</OutArgument>
                                                              </Assign.To>
                                                              <Assign.Value>
                                                                <InArgument x:TypeArguments="x:String">[Math.Round((Decimal.Parse(charge, New System.Globalization.CultureInfo("en-US")))/totqty1,4).ToString(New System.Globalization.CultureInfo("en-US"))]</InArgument>
                                                              </Assign.Value>
                                                            </Assign>
                                                          </If.Else>
                                                        </If>
                                                        <If Condition="[emptyCharge AND tot = 0]" sap2010:WorkflowViewState.IdRef="If_36">
                                                          <If.Else>
                                                            <Sequence sap2010:WorkflowViewState.IdRef="Sequence_60">
                                                              <iai:IONAPIRequestWizard FileAttachments="{x:Null}" PostData="{x:Null}" ResponseCode="{x:Null}" ContentType="application_json" ContinueOnError="True" DisplayName="IONAPI Request for Adding lines" HttpMethod="GET" sap2010:WorkflowViewState.IdRef="IONAPIRequestWizard_24" Response="[respObj8]" StatusCode="[StatusCode8]" Url="[TenantID+ &quot;M3/m3api-rest/v2/execute/APS450MI/AddLine&quot;]">
                                                                <iai:IONAPIRequestWizard.Headers>
                                                                  <scg:List x:TypeArguments="scg:List(x:String)" Capacity="4">
                                                                    <scg:List x:TypeArguments="x:String" Capacity="4">
                                                                      <x:String>Accept</x:String>
                                                                    </scg:List>
                                                                    <scg:List x:TypeArguments="x:String" Capacity="4">
                                                                      <x:String>application/json</x:String>
                                                                    </scg:List>
                                                                  </scg:List>
                                                                </iai:IONAPIRequestWizard.Headers>
                                                                <iai:IONAPIRequestWizard.QueryParameters>
                                                                  <scg:List x:TypeArguments="scg:List(x:String)" Capacity="4">
                                                                    <scg:List x:TypeArguments="x:String" Capacity="16">
                                                                      <x:String>INBN</x:String>
                                                                      <x:String>RDTP</x:String>
                                                                      <x:String>DIVI</x:String>
                                                                      <x:String>CDSE</x:String>
                                                                      <x:String>CEID</x:String>
                                                                      <x:String>ITNO</x:String>
                                                                      <x:String>PNLI</x:String>
                                                                      <x:String>PUNO</x:String>
                                                                      <x:String>REPN</x:String>
                                                                      <x:String>IVQA</x:String>
                                                                      <x:String>GRPR</x:String>
                                                                    </scg:List>
                                                                    <scg:List x:TypeArguments="x:String" Capacity="16">
                                                                      <x:String>inbnValue</x:String>
                                                                      <x:String>5</x:String>
                                                                      <x:String>division</x:String>
                                                                      <x:String>cdse</x:String>
                                                                      <x:String>ceid</x:String>
                                                                      <x:String>itno</x:String>
                                                                      <x:String>pnli</x:String>
                                                                      <x:String>puno</x:String>
                                                                      <x:String>repn</x:String>
                                                                      <x:String>ivqa</x:String>
                                                                      <x:String>grpr</x:String>
                                                                    </scg:List>
                                                                  </scg:List>
                                                                </iai:IONAPIRequestWizard.QueryParameters>
                                                              </iai:IONAPIRequestWizard>
                                                              <Assign sap2010:WorkflowViewState.IdRef="Assign_140">
                                                                <Assign.To>
                                                                  <OutArgument x:TypeArguments="njl:JToken">[out8]</OutArgument>
                                                                </Assign.To>
                                                                <Assign.Value>
                                                                  <InArgument x:TypeArguments="njl:JToken">[JToken.Parse(respObj8.ReadAsText)]</InArgument>
                                                                </Assign.Value>
                                                              </Assign>
                                                              <If Condition="[out8(&quot;results&quot;)(0)(&quot;records&quot;).ToString = &quot;[]&quot;]" sap2010:WorkflowViewState.IdRef="If_35">
                                                                <If.Then>
                                                                  <Sequence sap2010:WorkflowViewState.IdRef="Sequence_58">
                                                                    <Assign sap2010:WorkflowViewState.IdRef="Assign_141">
                                                                      <Assign.To>
                                                                        <OutArgument x:TypeArguments="x:String">[commentStatus]</OutArgument>
                                                                      </Assign.To>
                                                                      <Assign.Value>
                                                                        <InArgument x:TypeArguments="x:String">[(out8("results")(0)("errorMessage")).ToString]</InArgument>
                                                                      </Assign.Value>
                                                                    </Assign>
                                                                    <ias:Append_Line ErrorCode="{x:Null}" ContinueOnError="True" DisplayName="Append Line" sap2010:WorkflowViewState.IdRef="Append_Line_24" Line="[commentStatus]" Source="[logfile]" />
                                                                  </Sequence>
                                                                </If.Then>
                                                                <If.Else>
                                                                  <Sequence sap2010:WorkflowViewState.IdRef="Sequence_59">
                                                                    <Assign sap2010:WorkflowViewState.IdRef="Assign_142">
                                                                      <Assign.To>
                                                                        <OutArgument x:TypeArguments="x:String">[commentStatus]</OutArgument>
                                                                      </Assign.To>
                                                                      <Assign.Value>
                                                                        <InArgument x:TypeArguments="x:String">Line charge added</InArgument>
                                                                      </Assign.Value>
                                                                    </Assign>
                                                                    <ias:Append_Line ErrorCode="{x:Null}" ContinueOnError="True" DisplayName="Append Line" sap2010:WorkflowViewState.IdRef="Append_Line_25" Line="[commentStatus]" Source="[logfile]" />
                                                                  </Sequence>
                                                                </If.Else>
                                                              </If>
                                                            </Sequence>
                                                          </If.Else>
                                                        </If>
                                                      </Sequence>
                                                    </If.Then>
                                                    <If.Else>
                                                      <ias:Append_Line ErrorCode="{x:Null}" ContinueOnError="True" DisplayName="AddCharge_Append_Line_78" sap2010:WorkflowViewState.IdRef="Append_Line_19" Line="[&quot;No record available for the charge code: &quot; + ceid]" Source="[logfile]" />
                                                    </If.Else>
                                                  </If>
                                                </Sequence>
                                              </If.Then>
                                              <If.Else>
                                                <Sequence DisplayName="AddCharge_Sequence_79" sap2010:WorkflowViewState.IdRef="Sequence_48">
                                                  <Assign DisplayName="AddCharge_Assign_80_commentStatus" sap2010:WorkflowViewState.IdRef="Assign_127">
                                                    <Assign.To>
                                                      <OutArgument x:TypeArguments="x:String">[commentStatus]</OutArgument>
                                                    </Assign.To>
                                                    <Assign.Value>
                                                      <InArgument x:TypeArguments="x:String">["Received an IONAPI error while fetching lines charges for the PO " + puno +"."]</InArgument>
                                                    </Assign.Value>
                                                  </Assign>
                                                  <Assign DisplayName="AddCharge_Assign_81_Status" sap2010:WorkflowViewState.IdRef="Assign_128">
                                                    <Assign.To>
                                                      <OutArgument x:TypeArguments="x:String">[Status]</OutArgument>
                                                    </Assign.To>
                                                    <Assign.Value>
                                                      <InArgument x:TypeArguments="x:String">["FAILURE"]</InArgument>
                                                    </Assign.Value>
                                                  </Assign>
                                                  <ias:Append_Line ErrorCode="{x:Null}" ContinueOnError="True" DisplayName="AddCharge_Append_Line_82" sap2010:WorkflowViewState.IdRef="Append_Line_20" Line="[commentStatus]" Source="[logfile]" />
                                                </Sequence>
                                              </If.Else>
                                            </If>
                                          </Sequence>
                                        </If.Then>
                                      </If>
                                    </Sequence>
                                  </If.Else>
                                </If>
                              </Sequence>
                            </Sequence>
                          </If.Then>
                          <If.Else>
                            <Sequence DisplayName="AddCharge_Sequence_83" sap2010:WorkflowViewState.IdRef="Sequence_53">
                              <Assign DisplayName="AddCharge_Assign_84_commentStatus" sap2010:WorkflowViewState.IdRef="Assign_129">
                                <Assign.To>
                                  <OutArgument x:TypeArguments="x:String">[commentStatus]</OutArgument>
                                </Assign.To>
                                <Assign.Value>
                                  <InArgument x:TypeArguments="x:String">["Received an IONAPI error while fetching lines charges for the PO " + puno +"."]</InArgument>
                                </Assign.Value>
                              </Assign>
                              <Assign DisplayName="AddCharge_Assign_85_Status" sap2010:WorkflowViewState.IdRef="Assign_130">
                                <Assign.To>
                                  <OutArgument x:TypeArguments="x:String">[Status]</OutArgument>
                                </Assign.To>
                                <Assign.Value>
                                  <InArgument x:TypeArguments="x:String">["FAILURE"]</InArgument>
                                </Assign.Value>
                              </Assign>
                              <ias:Append_Line ErrorCode="{x:Null}" ContinueOnError="True" DisplayName="AddCharge_Append_Line_86" sap2010:WorkflowViewState.IdRef="Append_Line_21" Line="[commentStatus]" Source="[logfile]" />
                            </Sequence>
                          </If.Else>
                        </If>
                      </Sequence>
                    </If.Then>
                  </If>
                </Sequence>
              </ActivityAction>
            </ForEach>
          </If.Else>
        </If>
      </If.Then>
    </If>
    <sads:DebugSymbol.Symbol>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</sads:DebugSymbol.Symbol>
  </Sequence>
  <sap2010:WorkflowViewState.ViewStateManager>
    <sap2010:ViewStateManager>
      <sap2010:ViewStateData Id="Assign_21" sap:VirtualizedContainerService.HintSize="866,60" />
      <sap2010:ViewStateData Id="Assign_37" sap:VirtualizedContainerService.HintSize="866,60" />
      <sap2010:ViewStateData Id="Assign_68" sap:VirtualizedContainerService.HintSize="866,60" />
      <sap2010:ViewStateData Id="Assign_17" sap:VirtualizedContainerService.HintSize="242,60" />
      <sap2010:ViewStateData Id="Assign_18" sap:VirtualizedContainerService.HintSize="242,60" />
      <sap2010:ViewStateData Id="Assign_131" sap:VirtualizedContainerService.HintSize="242,60" />
      <sap2010:ViewStateData Id="Assign_19" sap:VirtualizedContainerService.HintSize="242,60" />
      <sap2010:ViewStateData Id="IONAPIRequestWizard_5" sap:VirtualizedContainerService.HintSize="242,22" />
      <sap2010:ViewStateData Id="Assign_10" sap:VirtualizedContainerService.HintSize="576,60" />
      <sap2010:ViewStateData Id="Assign_20" sap:VirtualizedContainerService.HintSize="242,60" />
      <sap2010:ViewStateData Id="ForEach`1_6" sap:VirtualizedContainerService.HintSize="287,208" />
      <sap2010:ViewStateData Id="Assign_36" sap:VirtualizedContainerService.HintSize="287,60" />
      <sap2010:ViewStateData Id="Sequence_11" sap:VirtualizedContainerService.HintSize="309,432">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">True</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="Assign_67" sap:VirtualizedContainerService.HintSize="242,60" />
      <sap2010:ViewStateData Id="If_7" sap:VirtualizedContainerService.HintSize="576,580">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">True</x:Boolean>
            <x:Boolean x:Key="IsPinned">False</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="Sequence_10" sap:VirtualizedContainerService.HintSize="598,804">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">True</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="Assign_5" sap:VirtualizedContainerService.HintSize="242,60" />
      <sap2010:ViewStateData Id="Assign_6" sap:VirtualizedContainerService.HintSize="242,60" />
      <sap2010:ViewStateData Id="Append_Line_1" sap:VirtualizedContainerService.HintSize="242,22" />
      <sap2010:ViewStateData Id="Sequence_2" sap:VirtualizedContainerService.HintSize="264,346">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">True</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="If_1" sap:VirtualizedContainerService.HintSize="242,51">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">False</x:Boolean>
            <x:Boolean x:Key="IsPinned">False</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="Sequence_61" sap:VirtualizedContainerService.HintSize="264,637">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">True</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="If_37" sap:VirtualizedContainerService.HintSize="464,785" />
      <sap2010:ViewStateData Id="Sequence_4" sap:VirtualizedContainerService.HintSize="486,909">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">True</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="ForEach`1_3" sap:VirtualizedContainerService.HintSize="866,1057" />
      <sap2010:ViewStateData Id="Assign_80" sap:VirtualizedContainerService.HintSize="866,64" />
      <sap2010:ViewStateData Id="Assign_106" sap:VirtualizedContainerService.HintSize="242,62" />
      <sap2010:ViewStateData Id="Assign_107" sap:VirtualizedContainerService.HintSize="242,62" />
      <sap2010:ViewStateData Id="Assign_108" sap:VirtualizedContainerService.HintSize="242,62" />
      <sap2010:ViewStateData Id="Assign_109" sap:VirtualizedContainerService.HintSize="242,62" />
      <sap2010:ViewStateData Id="Sequence_40" sap:VirtualizedContainerService.HintSize="200,52.6666666666667">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">False</x:Boolean>
            <x:Boolean x:Key="IsPinned">False</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="IONAPIRequestWizard_18" sap:VirtualizedContainerService.HintSize="200,22" />
      <sap2010:ViewStateData Id="Sequence_41" sap:VirtualizedContainerService.HintSize="200,51">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">False</x:Boolean>
            <x:Boolean x:Key="IsPinned">False</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="Assign_110" sap:VirtualizedContainerService.HintSize="242,60" />
      <sap2010:ViewStateData Id="Assign_111" sap:VirtualizedContainerService.HintSize="242,60" />
      <sap2010:ViewStateData Id="Assign_132" sap:VirtualizedContainerService.HintSize="242,60" />
      <sap2010:ViewStateData Id="Assign_112" sap:VirtualizedContainerService.HintSize="242,60" />
      <sap2010:ViewStateData Id="IONAPIRequestWizard_19" sap:VirtualizedContainerService.HintSize="242,22" />
      <sap2010:ViewStateData Id="Assign_113" sap:VirtualizedContainerService.HintSize="2324,60" />
      <sap2010:ViewStateData Id="Assign_114" sap:VirtualizedContainerService.HintSize="2324,60" />
      <sap2010:ViewStateData Id="Assign_115" sap:VirtualizedContainerService.HintSize="2324,60" />
      <sap2010:ViewStateData Id="Assign_116" sap:VirtualizedContainerService.HintSize="2324,60" />
      <sap2010:ViewStateData Id="Assign_117" sap:VirtualizedContainerService.HintSize="798,60" />
      <sap2010:ViewStateData Id="Assign_118" sap:VirtualizedContainerService.HintSize="798,60" />
      <sap2010:ViewStateData Id="Assign_119" sap:VirtualizedContainerService.HintSize="242,60" />
      <sap2010:ViewStateData Id="Assign_133" sap:VirtualizedContainerService.HintSize="242,60" />
      <sap2010:ViewStateData Id="Assign_134" sap:VirtualizedContainerService.HintSize="242,60" />
      <sap2010:ViewStateData Id="If_31" sap:VirtualizedContainerService.HintSize="509,208" />
      <sap2010:ViewStateData Id="Sequence_42" sap:VirtualizedContainerService.HintSize="531,332">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">True</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="If_23" sap:VirtualizedContainerService.HintSize="798,480" />
      <sap2010:ViewStateData Id="Sequence_43" sap:VirtualizedContainerService.HintSize="820,804">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">True</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="ForEach`1_12" sap:VirtualizedContainerService.HintSize="850,952">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">True</x:Boolean>
            <x:Boolean x:Key="IsPinned">False</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="IONAPIRequestWizard_23" sap:VirtualizedContainerService.HintSize="553,22" />
      <sap2010:ViewStateData Id="Assign_135" sap:VirtualizedContainerService.HintSize="553,60" />
      <sap2010:ViewStateData Id="Assign_136" sap:VirtualizedContainerService.HintSize="242,60" />
      <sap2010:ViewStateData Id="Append_Line_22" sap:VirtualizedContainerService.HintSize="242,22" />
      <sap2010:ViewStateData Id="Sequence_55" sap:VirtualizedContainerService.HintSize="264,246">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">True</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="Assign_137" sap:VirtualizedContainerService.HintSize="242,60" />
      <sap2010:ViewStateData Id="Append_Line_23" sap:VirtualizedContainerService.HintSize="242,22" />
      <sap2010:ViewStateData Id="Sequence_56" sap:VirtualizedContainerService.HintSize="264,246">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">True</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="If_32" sap:VirtualizedContainerService.HintSize="553,394" />
      <sap2010:ViewStateData Id="Sequence_57" sap:VirtualizedContainerService.HintSize="575,680">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">True</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="If_33" sap:VirtualizedContainerService.HintSize="850,828" />
      <sap2010:ViewStateData Id="Sequence_44" sap:VirtualizedContainerService.HintSize="872,1944">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">True</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="Assign_121" sap:VirtualizedContainerService.HintSize="1258,60" />
      <sap2010:ViewStateData Id="Assign_122" sap:VirtualizedContainerService.HintSize="1258,60" />
      <sap2010:ViewStateData Id="IONAPIRequestWizard_21" sap:VirtualizedContainerService.HintSize="1258,22" />
      <sap2010:ViewStateData Id="Assign_123" sap:VirtualizedContainerService.HintSize="947,60" />
      <sap2010:ViewStateData Id="Assign_124" sap:VirtualizedContainerService.HintSize="242,60" />
      <sap2010:ViewStateData Id="Sequence_45" sap:VirtualizedContainerService.HintSize="264,184">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">True</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="Append_Line_17" sap:VirtualizedContainerService.HintSize="200,22" />
      <sap2010:ViewStateData Id="If_24" sap:VirtualizedContainerService.HintSize="947,332" />
      <sap2010:ViewStateData Id="Assign_125" sap:VirtualizedContainerService.HintSize="700,60" />
      <sap2010:ViewStateData Id="Assign_138" sap:VirtualizedContainerService.HintSize="242,60" />
      <sap2010:ViewStateData Id="Assign_139" sap:VirtualizedContainerService.HintSize="242,60" />
      <sap2010:ViewStateData Id="If_34" sap:VirtualizedContainerService.HintSize="700,208" />
      <sap2010:ViewStateData Id="IONAPIRequestWizard_24" sap:VirtualizedContainerService.HintSize="553,22" />
      <sap2010:ViewStateData Id="Assign_140" sap:VirtualizedContainerService.HintSize="553,60" />
      <sap2010:ViewStateData Id="Assign_141" sap:VirtualizedContainerService.HintSize="242,60" />
      <sap2010:ViewStateData Id="Append_Line_24" sap:VirtualizedContainerService.HintSize="242,22" />
      <sap2010:ViewStateData Id="Sequence_58" sap:VirtualizedContainerService.HintSize="264,246">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">True</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="Assign_142" sap:VirtualizedContainerService.HintSize="242,60" />
      <sap2010:ViewStateData Id="Append_Line_25" sap:VirtualizedContainerService.HintSize="242,22" />
      <sap2010:ViewStateData Id="Sequence_59" sap:VirtualizedContainerService.HintSize="264,246">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">True</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="If_35" sap:VirtualizedContainerService.HintSize="553,394" />
      <sap2010:ViewStateData Id="Sequence_60" sap:VirtualizedContainerService.HintSize="575,680">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">True</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="If_36" sap:VirtualizedContainerService.HintSize="700,828" />
      <sap2010:ViewStateData Id="Sequence_46" sap:VirtualizedContainerService.HintSize="722,1300">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">True</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="Append_Line_19" sap:VirtualizedContainerService.HintSize="200,22" />
      <sap2010:ViewStateData Id="If_25" sap:VirtualizedContainerService.HintSize="947,1448" />
      <sap2010:ViewStateData Id="Sequence_47" sap:VirtualizedContainerService.HintSize="969,2044">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">True</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="Assign_127" sap:VirtualizedContainerService.HintSize="242,60" />
      <sap2010:ViewStateData Id="Assign_128" sap:VirtualizedContainerService.HintSize="242,60" />
      <sap2010:ViewStateData Id="Append_Line_20" sap:VirtualizedContainerService.HintSize="242,22" />
      <sap2010:ViewStateData Id="Sequence_48" sap:VirtualizedContainerService.HintSize="264,346">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">True</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="If_26" sap:VirtualizedContainerService.HintSize="1258,2192">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">True</x:Boolean>
            <x:Boolean x:Key="IsPinned">False</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="Sequence_49" sap:VirtualizedContainerService.HintSize="1280,2578">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">True</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="If_27" sap:VirtualizedContainerService.HintSize="1405,2726" />
      <sap2010:ViewStateData Id="Sequence_50" sap:VirtualizedContainerService.HintSize="1427,2850">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">True</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="If_28" sap:VirtualizedContainerService.HintSize="2324,2998">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">True</x:Boolean>
            <x:Boolean x:Key="IsPinned">False</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="Sequence_51" sap:VirtualizedContainerService.HintSize="2346,3522">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">True</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="Sequence_52" sap:VirtualizedContainerService.HintSize="2368,3646">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">True</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="Assign_129" sap:VirtualizedContainerService.HintSize="242,60" />
      <sap2010:ViewStateData Id="Assign_130" sap:VirtualizedContainerService.HintSize="242,60" />
      <sap2010:ViewStateData Id="Append_Line_21" sap:VirtualizedContainerService.HintSize="242,22" />
      <sap2010:ViewStateData Id="Sequence_53" sap:VirtualizedContainerService.HintSize="264,346">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">True</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="If_29" sap:VirtualizedContainerService.HintSize="242,51">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">False</x:Boolean>
            <x:Boolean x:Key="IsPinned">False</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="Sequence_62" sap:VirtualizedContainerService.HintSize="264,637">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">True</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="If_38" sap:VirtualizedContainerService.HintSize="464,785" />
      <sap2010:ViewStateData Id="Sequence_54" sap:VirtualizedContainerService.HintSize="486,909">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">True</x:Boolean>
            <x:Boolean x:Key="IsPinned">False</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="ForEach`1_13" sap:VirtualizedContainerService.HintSize="516,1057" />
      <sap2010:ViewStateData Id="If_30" sap:VirtualizedContainerService.HintSize="741,1205">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">True</x:Boolean>
            <x:Boolean x:Key="IsPinned">False</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="If_2" sap:VirtualizedContainerService.HintSize="866,1353">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">True</x:Boolean>
            <x:Boolean x:Key="IsPinned">False</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="Sequence_1" sap:VirtualizedContainerService.HintSize="888,2978">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">True</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="RehostedWorkflowDesigner.Workflow_1" sap:VirtualizedContainerService.HintSize="928,3058" />
    </sap2010:ViewStateManager>
  </sap2010:WorkflowViewState.ViewStateManager>
</Activity>