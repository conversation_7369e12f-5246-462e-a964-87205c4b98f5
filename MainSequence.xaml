﻿<Activity mc:Ignorable="sap sap2010 sads" x:Class="RehostedWorkflowDesigner.Workflow" this:Workflow.configurationFolder="C:\M3VendorInvoiceProcessing" this:Workflow.emailAccount="<EMAIL>" this:Workflow.emailFolder="M3" this:Workflow.numberOfEmails="10" this:Workflow.userIdentifier="38ea2db4-5d81-4da1-83c9-0d86f6f799cc" this:Workflow.distributionType="USER" this:Workflow.enableMessageBoxes="False" this:Workflow.division="UKA" this:Workflow.tenantID="https://mingle-ionapi.eu1.inforcloudsuite.com/INTERSNACK_DEV/" this:Workflow.projectPath="[Environment.GetFolderPath(Environment.SpecialFolder.UserProfile) + &quot;\AppData\Local\InforRPA\M3InvoiceProcessingGenAIV3&quot;]" this:Workflow.datalakeAPILogicalId="infor.ims.imsfromrpastudio" this:Workflow.handleCashDiscount="True" this:Workflow.Match3Way="True" this:Workflow.invoiceSource="OutlookClientEmail" this:Workflow.BrowserName="CHroMe" this:Workflow.Duedate="False" this:Workflow.InvoiceDate="False" this:Workflow.ExpectedReceiptDate="False" this:Workflow.invoiceDateDays="0" this:Workflow.ExpectedReceiptDateDays="0" this:Workflow.Action="Notification" this:Workflow.PreRegister="False" this:Workflow.PeriodTypeInPreRegister="2"
 xmlns="http://schemas.microsoft.com/netfx/2009/xaml/activities"
 xmlns:iai="clr-namespace:Infor.Activities.IONAPI;assembly=Infor.Activities.IONAPI"
 xmlns:ias="clr-namespace:Infor.Activities.Sys;assembly=Infor.Activities.Sys"
 xmlns:iaw="clr-namespace:Infor.Activities.Workflow;assembly=Infor.Activities.Workflow"
 xmlns:iru="clr-namespace:Infor.RPA.Utilities;assembly=Infor.RPA.Utilities"
 xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006"
 xmlns:mva="clr-namespace:Microsoft.VisualBasic.Activities;assembly=System.Activities"
 xmlns:njl="clr-namespace:Newtonsoft.Json.Linq;assembly=Newtonsoft.Json"
 xmlns:s="clr-namespace:System;assembly=mscorlib"
 xmlns:sads="http://schemas.microsoft.com/netfx/2010/xaml/activities/debugger"
 xmlns:sap="http://schemas.microsoft.com/netfx/2009/xaml/activities/presentation"
 xmlns:sap2010="http://schemas.microsoft.com/netfx/2010/xaml/activities/presentation"
 xmlns:scg="clr-namespace:System.Collections.Generic;assembly=mscorlib"
 xmlns:sco="clr-namespace:System.Collections.ObjectModel;assembly=mscorlib"
 xmlns:sd="clr-namespace:System.Data;assembly=System.Data"
 xmlns:this="clr-namespace:RehostedWorkflowDesigner"
 xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml">
  <x:Members>
    <x:Property Name="configurationFolder" Type="InArgument(x:String)" />
    <x:Property Name="emailAccount" Type="InArgument(x:String)" />
    <x:Property Name="emailFolder" Type="InArgument(x:String)" />
    <x:Property Name="numberOfEmails" Type="InArgument(x:Int32)" />
    <x:Property Name="userIdentifier" Type="InArgument(x:String)" />
    <x:Property Name="distributionType" Type="InArgument(x:String)" />
    <x:Property Name="enableMessageBoxes" Type="InArgument(x:Boolean)" />
    <x:Property Name="division" Type="InArgument(x:String)" />
    <x:Property Name="tenantID" Type="InArgument(x:String)" />
    <x:Property Name="projectPath" Type="InArgument(x:String)" />
    <x:Property Name="datalakeAPILogicalId" Type="InArgument(x:String)" />
    <x:Property Name="handleCashDiscount" Type="InArgument(x:Boolean)" />
    <x:Property Name="Match3Way" Type="InArgument(x:Boolean)" />
    <x:Property Name="invoiceSource" Type="InArgument(x:String)" />
    <x:Property Name="BrowserName" Type="InArgument(x:String)" />
    <x:Property Name="Duedate" Type="InArgument(x:Boolean)" />
    <x:Property Name="InvoiceDate" Type="InArgument(x:Boolean)" />
    <x:Property Name="ExpectedReceiptDate" Type="InArgument(x:Boolean)" />
    <x:Property Name="invoiceDateDays" Type="InArgument(x:Int32)" />
    <x:Property Name="ExpectedReceiptDateDays" Type="InArgument(x:Int32)" />
    <x:Property Name="Action" Type="InArgument(x:String)" />
    <x:Property Name="PreRegister" Type="InArgument(x:Boolean)" />
    <x:Property Name="PeriodTypeInPreRegister" Type="InArgument(x:String)" />
  </x:Members>
  <mva:VisualBasic.Settings>
    <x:Null />
  </mva:VisualBasic.Settings>
  <sap2010:WorkflowViewState.IdRef>RehostedWorkflowDesigner.Workflow_1</sap2010:WorkflowViewState.IdRef>
  <TextExpression.NamespacesForImplementation>
    <sco:Collection x:TypeArguments="x:String">
      <x:String>System.Activities</x:String>
      <x:String>System.Activities.Statements</x:String>
      <x:String>System.Activities.Expressions</x:String>
      <x:String>System.Activities.Validation</x:String>
      <x:String>System.Activities.XamlIntegration</x:String>
      <x:String>Infor.Activities.Email</x:String>
      <x:String>Infor.Activities.Workflow</x:String>
      <x:String>Microsoft.VisualBasic.Activities</x:String>
      <x:String>System</x:String>
      <x:String>System.Activities.Debugger.Symbol</x:String>
      <x:String>System.Collections.Generic</x:String>
      <x:String>System.Windows.Markup</x:String>
      <x:String>Microsoft.VisualBasic.CompilerServices</x:String>
      <x:String>System.Linq</x:String>
      <x:String>System.Globalization</x:String>
      <x:String>Infor.RPA.Utilities</x:String>
      <x:String>Newtonsoft.Json.Linq</x:String>
      <x:String>Newtonsoft.Json</x:String>
      <x:String>System.Text.RegularExpressions</x:String>
      <x:String>System.Data</x:String>
      <x:String>System.Xml.Serialization</x:String>
    </sco:Collection>
  </TextExpression.NamespacesForImplementation>
  <TextExpression.ReferencesForImplementation>
    <sco:Collection x:TypeArguments="AssemblyReference">
      <AssemblyReference>System.Activities</AssemblyReference>
      <AssemblyReference>Infor.Activities.Email</AssemblyReference>
      <AssemblyReference>Infor.Activities.Workflow</AssemblyReference>
      <AssemblyReference>mscorlib</AssemblyReference>
      <AssemblyReference>System.Core</AssemblyReference>
      <AssemblyReference>System</AssemblyReference>
      <AssemblyReference>System.ServiceModel</AssemblyReference>
      <AssemblyReference>System.Runtime.WindowsRuntime</AssemblyReference>
      <AssemblyReference>Microsoft.Bcl.AsyncInterfaces</AssemblyReference>
      <AssemblyReference>PresentationFramework</AssemblyReference>
      <AssemblyReference>WindowsBase</AssemblyReference>
      <AssemblyReference>PresentationCore</AssemblyReference>
      <AssemblyReference>System.Xaml</AssemblyReference>
      <AssemblyReference>Microsoft.VisualBasic</AssemblyReference>
      <AssemblyReference>Infor.RPA.Utilities</AssemblyReference>
      <AssemblyReference>Newtonsoft.Json</AssemblyReference>
      <AssemblyReference>System.ComponentModel.Composition</AssemblyReference>
      <AssemblyReference>System.Memory</AssemblyReference>
      <AssemblyReference>System.Collections.Immutable</AssemblyReference>
      <AssemblyReference>System.Data</AssemblyReference>
      <AssemblyReference>System.Xml</AssemblyReference>
    </sco:Collection>
  </TextExpression.ReferencesForImplementation>
  <TryCatch DisplayName="MainSequence_Sequence_1" sap2010:WorkflowViewState.IdRef="TryCatch_3">
    <TryCatch.Variables>
      <Variable x:TypeArguments="x:String" Name="logFile" />
      <Variable x:TypeArguments="scg:IDictionary(x:String, x:Object)" Name="CDOutput" />
      <Variable x:TypeArguments="x:String" Name="model" />
      <Variable x:TypeArguments="x:String" Name="GenAIModel" />
      <Variable x:TypeArguments="x:String" Name="GenAIModelVersion" />
      <Variable x:TypeArguments="scg:Dictionary(x:String, x:String)" Default="[New Dictionary (Of String, String)]" Name="PromptsDicitonary" />
      <Variable x:TypeArguments="sd:DataTable" Name="dtConfig" />
      <Variable x:TypeArguments="x:String" Name="version" />
      <Variable x:TypeArguments="scg:Dictionary(x:String, x:Object)" Default="[New Dictionary(Of String, Object)]" Name="miscValues" />
      <Variable x:TypeArguments="x:Boolean" Name="logFileExist" />
    </TryCatch.Variables>
    <TryCatch.Try>
      <Sequence DisplayName="MainSequence" sap2010:WorkflowViewState.IdRef="Sequence_54">
        <Sequence.Variables>
          <Variable x:TypeArguments="x:Int32" Name="datalakeResponseCode" />
          <Variable x:TypeArguments="x:Boolean" Name="GenAIPromptFileExists" />
          <Variable x:TypeArguments="scg:IDictionary(x:String, x:Object)" Name="GenAIOutput" />
          <Variable x:TypeArguments="x:String" Name="GenAIStatus" />
          <Variable x:TypeArguments="njl:JToken" Name="value" />
          <Variable x:TypeArguments="iru:ResponseObject" Name="resp" />
          <Variable x:TypeArguments="scg:List(x:String)" Name="files" />
          <Variable x:TypeArguments="scg:List(x:String)" Default="[New List(Of String)]" Name="ListPromptFiles" />
          <Variable x:TypeArguments="x:String" Name="strStartTime" />
          <Variable x:TypeArguments="x:String" Name="testing" />
        </Sequence.Variables>
        <Assign sap2010:WorkflowViewState.IdRef="Assign_112">
          <Assign.To>
            <OutArgument x:TypeArguments="x:String">[strStartTime]</OutArgument>
          </Assign.To>
          <Assign.Value>
            <InArgument x:TypeArguments="x:String">[System.DateTime.Now.ToString("yyyy/MM/dd HH:mm:ss")]</InArgument>
          </Assign.Value>
        </Assign>
        <Sequence DisplayName="json Config File" sap2010:WorkflowViewState.IdRef="Sequence_60">
          <Sequence.Variables>
            <Variable x:TypeArguments="x:Int32" Name="intCounter" />
            <Variable x:TypeArguments="iru:ResponseObject" Name="GenAIresponse" />
            <Variable x:TypeArguments="x:Int32" Name="GenAIresponseCode" />
            <Variable x:TypeArguments="njl:JToken" Name="GenAIrespToken" />
            <Variable x:TypeArguments="x:String" Name="strjsonText" />
            <Variable x:TypeArguments="njl:JToken" Name="ConfigJson" />
          </Sequence.Variables>
          <ias:File_Delete ErrorCode="{x:Null}" ContinueOnError="True" DisplayName="Delete Existing Config File" sap2010:WorkflowViewState.IdRef="File_Delete_16" Source="[ConfigurationFolder + &quot;\ProcessArguments.json&quot;]" />
          <iai:IONAPIRequestWizard FileAttachments="{x:Null}" PostData="{x:Null}" QueryParameters="{x:Null}" ResponseCode="{x:Null}" ContentType="application_json" ContinueOnError="False" DisplayName="IONAPI Request to download ProcessArguments File" HttpMethod="GET" sap2010:WorkflowViewState.IdRef="IONAPIRequestWizard_5" Response="[GenAIresponse]" StatusCode="[GenAIresponseCode]" Url="[tenantID &amp; &quot;IDM/api/items/search/item?$query=&quot; &amp; Uri.EscapeDataString(&quot;/GenAIPrompt[@RESOURCENAME = &quot;&quot;ProcessArguments.json&quot;&quot;] SORTBY(@LASTCHANGEDTS DESCENDING)&quot;)]">
            <iai:IONAPIRequestWizard.Headers>
              <scg:List x:TypeArguments="scg:List(x:String)" Capacity="4">
                <scg:List x:TypeArguments="x:String" Capacity="4">
                  <x:String>Accept</x:String>
                </scg:List>
                <scg:List x:TypeArguments="x:String" Capacity="4">
                  <x:String>application/json</x:String>
                </scg:List>
              </scg:List>
            </iai:IONAPIRequestWizard.Headers>
          </iai:IONAPIRequestWizard>
          <Sequence DisplayName="Download Process Arguments File" sap2010:WorkflowViewState.IdRef="Sequence_55">
            <Sequence.Variables>
              <Variable x:TypeArguments="x:String" Name="variable2" />
              <Variable x:TypeArguments="njl:JToken" Name="version" />
              <Variable x:TypeArguments="njl:JToken" Name="GenAIPromptUrl" />
              <Variable x:TypeArguments="x:String" Name="url" />
              <Variable x:TypeArguments="x:String" Name="ConfigPath" />
            </Sequence.Variables>
            <Assign sap2010:WorkflowViewState.IdRef="Assign_115">
              <Assign.To>
                <OutArgument x:TypeArguments="njl:JToken">[GenAIrespToken]</OutArgument>
              </Assign.To>
              <Assign.Value>
                <InArgument x:TypeArguments="njl:JToken">[GenAIresponse.readasjson]</InArgument>
              </Assign.Value>
            </Assign>
            <ias:JQTransform ErrorCode="{x:Null}" ContinueOnError="False" DisplayName="JQ Transform" sap2010:WorkflowViewState.IdRef="JQTransform_5" JQ=".key|.item|.resrs|.res[0]|.url" JSON="[GenAIPromptUrl]" Raw="False">
              <ias:JQTransform.Text>
                <scg:List x:TypeArguments="scg:List(x:String)" Capacity="4">
                  <scg:List x:TypeArguments="x:String" Capacity="4">
                    <x:String>key</x:String>
                  </scg:List>
                  <scg:List x:TypeArguments="x:String" Capacity="4">
                    <x:String>GenAIrespToken</x:String>
                  </scg:List>
                </scg:List>
              </ias:JQTransform.Text>
            </ias:JQTransform>
            <Assign sap2010:WorkflowViewState.IdRef="Assign_116">
              <Assign.To>
                <OutArgument x:TypeArguments="x:String">[url]</OutArgument>
              </Assign.To>
              <Assign.Value>
                <InArgument x:TypeArguments="x:String">[GenAIPromptUrl.tostring]</InArgument>
              </Assign.Value>
            </Assign>
            <Assign sap2010:WorkflowViewState.IdRef="Assign_117">
              <Assign.To>
                <OutArgument x:TypeArguments="x:String">[url]</OutArgument>
              </Assign.To>
              <Assign.Value>
                <InArgument x:TypeArguments="x:String">[url.trim().substring(1,url.length()-2)]</InArgument>
              </Assign.Value>
            </Assign>
            <ias:DownloadFile_URL ErrorCode="{x:Null}" Async="False" ContinueOnError="False" DisplayName="Download File By URL" sap2010:WorkflowViewState.IdRef="DownloadFile_URL_3" Name="ProcessArguments.json" OutputFile="[ConfigPath]" Target="[configurationFolder]" URL="[url]" />
          </Sequence>
          <ias:File_Read ErrorCode="{x:Null}" ContinueOnError="False" DisplayName="Read Text File" sap2010:WorkflowViewState.IdRef="File_Read_1" Source="[ConfigurationFolder + &quot;\ProcessArguments.json&quot;]" Text="[strjsonText]" />
          <ias:DeserializeJSON ErrorCode="{x:Null}" ContinueOnError="False" DisplayName="Deserialize JSON" sap2010:WorkflowViewState.IdRef="DeserializeJSON_1" JTokenObject="[ConfigJson]" JTokenString="[strjsonText]" />
          <Assign sap2010:WorkflowViewState.IdRef="Assign_126">
            <Assign.To>
              <OutArgument x:TypeArguments="scg:Dictionary(x:String, x:Object)">[miscValues]</OutArgument>
            </Assign.To>
            <Assign.Value>
              <InArgument x:TypeArguments="scg:Dictionary(x:String, x:Object)" xml:space="preserve">[JArray.Parse(ConfigJson.ToString()).
        ToDictionary(Function(p) p("name").ToString(),
                     Function(p) CType(p("value"), Object))]</InArgument>
            </Assign.Value>
          </Assign>
        </Sequence>
        <iaw:InvokeWorkflow ResponseCode="{x:Null}" Arguments="[New Dictionary(Of String, Object) From {{&quot;configurationFolder&quot;,configurationFolder},{&quot;directoriesNames&quot;,miscValues(&quot;directoriesNames&quot;).ToString.split(&quot;|&quot;c).ToList},{&quot;logFolderName&quot;,miscValues(&quot;logFolderName&quot;).ToString}}]" ContinueOnError="False" DisplayName="CreateDirectories Invoke Workflow" sap2010:WorkflowViewState.IdRef="InvokeWorkflow_24" OutputArguments="[CDOutput]" WorkflowFile="[projectPath+&quot;\CreateDirectories.xaml&quot;]" />
        <Assign sap2010:WorkflowViewState.IdRef="Assign_85">
          <Assign.To>
            <OutArgument x:TypeArguments="x:String">[logFile]</OutArgument>
          </Assign.To>
          <Assign.Value>
            <InArgument x:TypeArguments="x:String">[CDOutput("logFile").ToString]</InArgument>
          </Assign.Value>
        </Assign>
        <ias:Append_Line ErrorCode="{x:Null}" ContinueOnError="True" DisplayName="Empty Append Line" sap2010:WorkflowViewState.IdRef="Append_Line_27" Line="------------------------------------------------------------------------------------------------" Source="[logFile]" />
        <ias:Append_Line ErrorCode="{x:Null}" ContinueOnError="True" DisplayName="Start time Append Line" sap2010:WorkflowViewState.IdRef="Append_Line_28" Line="[&quot;Start Time : &quot; +strStartTime]" Source="[logFile]" />
        <ias:Append_Line ErrorCode="{x:Null}" ContinueOnError="True" DisplayName="CreateDirectories Append Line" sap2010:WorkflowViewState.IdRef="Append_Line_29" Line="CreateDirectories Workflow execution completed." Source="[logFile]" />
        <Assign DisplayName="AuthUser Assign" sap2010:WorkflowViewState.IdRef="Assign_86">
          <Assign.To>
            <OutArgument x:TypeArguments="x:Object">[miscValues("authUser")]</OutArgument>
          </Assign.To>
          <Assign.Value>
            <InArgument x:TypeArguments="x:String">[If(miscValues("authUser").ToString="NA","",miscValues("authUser").ToString)]</InArgument>
          </Assign.Value>
        </Assign>
        <Assign DisplayName="handleCashDiscount Assign" sap2010:WorkflowViewState.IdRef="Assign_87">
          <Assign.To>
            <OutArgument x:TypeArguments="x:Object">[miscValues("handleCashDiscount")]</OutArgument>
          </Assign.To>
          <Assign.Value>
            <InArgument x:TypeArguments="x:Boolean">[handleCashDiscount]</InArgument>
          </Assign.Value>
        </Assign>
        <Assign DisplayName="invoiceSource Assign" sap2010:WorkflowViewState.IdRef="Assign_127">
          <Assign.To>
            <OutArgument x:TypeArguments="x:Object">[miscValues("invoiceSource")]</OutArgument>
          </Assign.To>
          <Assign.Value>
            <InArgument x:TypeArguments="x:String">[invoiceSource]</InArgument>
          </Assign.Value>
        </Assign>
        <Sequence DisplayName="PromptsDicitonary Sequence" sap2010:WorkflowViewState.IdRef="Sequence_50">
          <Assign sap2010:WorkflowViewState.IdRef="Assign_88">
            <Assign.To>
              <OutArgument x:TypeArguments="scg:List(x:String)">[ListPromptFiles]</OutArgument>
            </Assign.To>
            <Assign.Value>
              <InArgument x:TypeArguments="scg:List(x:String)">[miscValues("strPromptFilesMapping").ToString.Split("|"c).Select(Function(x) x.Split(";"c)(0)).ToList]</InArgument>
            </Assign.Value>
          </Assign>
          <Assign sap2010:WorkflowViewState.IdRef="Assign_89">
            <Assign.To>
              <OutArgument x:TypeArguments="scg:Dictionary(x:String, x:String)">[PromptsDicitonary]</OutArgument>
            </Assign.To>
            <Assign.Value>
              <InArgument x:TypeArguments="scg:Dictionary(x:String, x:String)" xml:space="preserve">[miscValues("strPromptFilesMapping").ToString.Split("|"c).
    Select(Function(part) part.Split(";"c)).
    ToDictionary(Function(parts) parts(0), Function(parts) parts(1))]</InArgument>
            </Assign.Value>
          </Assign>
        </Sequence>
        <Switch x:TypeArguments="x:Boolean" DisplayName="IfUseGenAIExtraction - Get Prompt Files" Expression="[CType(miscValues(&quot;useGenAIExtraction&quot;).ToString,Boolean)]" sap2010:WorkflowViewState.IdRef="Switch`1_6">
          <ForEach x:TypeArguments="x:String" x:Key="True" DisplayName="ForEach prompt file" sap2010:WorkflowViewState.IdRef="ForEach`1_4" Values="[ListPromptFiles]">
            <ActivityAction x:TypeArguments="x:String">
              <ActivityAction.Argument>
                <DelegateInArgument x:TypeArguments="x:String" Name="promptfile" />
              </ActivityAction.Argument>
              <Sequence DisplayName="get Prompt Files - Sequence" sap2010:WorkflowViewState.IdRef="Sequence_51">
                <Sequence.Variables>
                  <Variable x:TypeArguments="x:String" Name="GenAIPromptFileURL" />
                  <Variable x:TypeArguments="x:String" Name="strFIleName" />
                </Sequence.Variables>
                <Assign sap2010:WorkflowViewState.IdRef="Assign_90">
                  <Assign.To>
                    <OutArgument x:TypeArguments="x:String">[strFIleName]</OutArgument>
                  </Assign.To>
                  <Assign.Value>
                    <InArgument x:TypeArguments="x:String">[PromptsDicitonary(promptfile)]</InArgument>
                  </Assign.Value>
                </Assign>
                <Assign DisplayName="Assign Updated" sap2010:WorkflowViewState.IdRef="Assign_91">
                  <Assign.To>
                    <OutArgument x:TypeArguments="x:String">[GenAIPromptFileURL]</OutArgument>
                  </Assign.To>
                  <Assign.Value>
                    <InArgument x:TypeArguments="x:String">[tenantID+"IDM/api/items/search/item?%24query=%2FGenAIPrompt%5B%40RESOURCENAME%20%3D%20%22" + promptfile + ".txt%22%5D%20SORTBY(%40LASTCHANGEDTS%20DESCENDING)"]</InArgument>
                  </Assign.Value>
                </Assign>
                <ias:File_Delete ErrorCode="{x:Null}" ContinueOnError="True" DisplayName="Delete File" sap2010:WorkflowViewState.IdRef="File_Delete_15" Source="[configurationFolder+&quot;\&quot;+strFIleName+&quot;.txt&quot;]" />
                <iaw:InvokeWorkflow ResponseCode="{x:Null}" Arguments="[New Dictionary(Of String, Object) From {{&quot;configurationFolder&quot;,configurationFolder},{&quot;logFile&quot;,logFile},{&quot;GenAIPromptFileURL&quot;,GenAIPromptFileURL},{&quot;fileName&quot;,strFIleName}}]" ContinueOnError="False" DisplayName="Invoke getGenAIPrompt download" sap2010:WorkflowViewState.IdRef="InvokeWorkflow_25" OutputArguments="[GenAIOutput]" WorkflowFile="[projectPath+&quot;\getGenAIPrompt.xaml&quot;]" />
                <Assign sap2010:WorkflowViewState.IdRef="Assign_92">
                  <Assign.To>
                    <OutArgument x:TypeArguments="x:String">[GenAIStatus]</OutArgument>
                  </Assign.To>
                  <Assign.Value>
                    <InArgument x:TypeArguments="x:String">[CType(GenAIOutput("GenAIStatus"), String)]</InArgument>
                  </Assign.Value>
                </Assign>
                <Assign sap2010:WorkflowViewState.IdRef="Assign_93">
                  <Assign.To>
                    <OutArgument x:TypeArguments="x:String">[model]</OutArgument>
                  </Assign.To>
                  <Assign.Value>
                    <InArgument x:TypeArguments="x:String">[CType(GenAIOutput("GenAIModel"), String)]</InArgument>
                  </Assign.Value>
                </Assign>
                <Assign sap2010:WorkflowViewState.IdRef="Assign_94">
                  <Assign.To>
                    <OutArgument x:TypeArguments="x:String">[version]</OutArgument>
                  </Assign.To>
                  <Assign.Value>
                    <InArgument x:TypeArguments="x:String">[CType(GenAIOutput("GenAIModelVersion"), String)]</InArgument>
                  </Assign.Value>
                </Assign>
                <If Condition="[GenAIModel = &quot;&quot;]" sap2010:WorkflowViewState.IdRef="If_38">
                  <If.Then>
                    <Assign sap2010:WorkflowViewState.IdRef="Assign_95">
                      <Assign.To>
                        <OutArgument x:TypeArguments="x:String">[GenAIModel]</OutArgument>
                      </Assign.To>
                      <Assign.Value>
                        <InArgument x:TypeArguments="x:String">[CType(GenAIOutput("GenAIModel"), String)]</InArgument>
                      </Assign.Value>
                    </Assign>
                  </If.Then>
                </If>
                <If Condition="[GenAIModelVersion = &quot;&quot;]" sap2010:WorkflowViewState.IdRef="If_39">
                  <If.Then>
                    <Assign sap2010:WorkflowViewState.IdRef="Assign_96">
                      <Assign.To>
                        <OutArgument x:TypeArguments="x:String">[GenAIModelVersion]</OutArgument>
                      </Assign.To>
                      <Assign.Value>
                        <InArgument x:TypeArguments="x:String">[CType(GenAIOutput("GenAIModelVersion"), String)]</InArgument>
                      </Assign.Value>
                    </Assign>
                  </If.Then>
                </If>
              </Sequence>
            </ActivityAction>
          </ForEach>
          <ias:Append_Line ErrorCode="{x:Null}" x:Key="False" ContinueOnError="True" DisplayName="Log useGenAIExtraction False" sap2010:WorkflowViewState.IdRef="Append_Line_31" Line="[&quot;useGenAIExtraction value is &quot;+miscValues(&quot;useGenAIExtraction&quot;).ToString+Environment.NewLine+&quot;notificationFailureCount Prompt file is downloaded.&quot;]" Source="[logFile]" />
        </Switch>
        <Sequence DisplayName="Add Values to miscValues" sap2010:WorkflowViewState.IdRef="Sequence_52">
          <Assign DisplayName="GenAIModel Assign" sap2010:WorkflowViewState.IdRef="Assign_136">
            <Assign.To>
              <OutArgument x:TypeArguments="x:Object">[miscValues("PeriodTypeInPreRegister")]</OutArgument>
            </Assign.To>
            <Assign.Value>
              <InArgument x:TypeArguments="x:Object">[PeriodTypeInPreRegister]</InArgument>
            </Assign.Value>
          </Assign>
          <Assign DisplayName="GenAIModel Assign" sap2010:WorkflowViewState.IdRef="Assign_130">
            <Assign.To>
              <OutArgument x:TypeArguments="x:Object">[miscValues("Action")]</OutArgument>
            </Assign.To>
            <Assign.Value>
              <InArgument x:TypeArguments="x:Object">[Action]</InArgument>
            </Assign.Value>
          </Assign>
          <Assign DisplayName="GenAIModel Assign" sap2010:WorkflowViewState.IdRef="Assign_131">
            <Assign.To>
              <OutArgument x:TypeArguments="x:Object">[miscValues("ExpectedReceiptDateDays")]</OutArgument>
            </Assign.To>
            <Assign.Value>
              <InArgument x:TypeArguments="x:Object">[ExpectedReceiptDateDays]</InArgument>
            </Assign.Value>
          </Assign>
          <Assign DisplayName="GenAIModel Assign" sap2010:WorkflowViewState.IdRef="Assign_132">
            <Assign.To>
              <OutArgument x:TypeArguments="x:Object">[miscValues("invoiceDateDays")]</OutArgument>
            </Assign.To>
            <Assign.Value>
              <InArgument x:TypeArguments="x:Object">[invoiceDateDays]</InArgument>
            </Assign.Value>
          </Assign>
          <Assign DisplayName="GenAIModel Assign" sap2010:WorkflowViewState.IdRef="Assign_133">
            <Assign.To>
              <OutArgument x:TypeArguments="x:Object">[miscValues("ExpectedReceiptDate")]</OutArgument>
            </Assign.To>
            <Assign.Value>
              <InArgument x:TypeArguments="x:Object">[ExpectedReceiptDate]</InArgument>
            </Assign.Value>
          </Assign>
          <Assign DisplayName="GenAIModel Assign" sap2010:WorkflowViewState.IdRef="Assign_134">
            <Assign.To>
              <OutArgument x:TypeArguments="x:Object">[miscValues("InvoiceDate")]</OutArgument>
            </Assign.To>
            <Assign.Value>
              <InArgument x:TypeArguments="x:Object">[InvoiceDate]</InArgument>
            </Assign.Value>
          </Assign>
          <Assign DisplayName="GenAIModel Assign" sap2010:WorkflowViewState.IdRef="Assign_135">
            <Assign.To>
              <OutArgument x:TypeArguments="x:Object">[miscValues("Duedate")]</OutArgument>
            </Assign.To>
            <Assign.Value>
              <InArgument x:TypeArguments="x:Object">[Duedate]</InArgument>
            </Assign.Value>
          </Assign>
          <Assign DisplayName="GenAIModel Assign" sap2010:WorkflowViewState.IdRef="Assign_129">
            <Assign.To>
              <OutArgument x:TypeArguments="x:Object">[miscValues("PreRegister")]</OutArgument>
            </Assign.To>
            <Assign.Value>
              <InArgument x:TypeArguments="x:Object">[PreRegister]</InArgument>
            </Assign.Value>
          </Assign>
          <Assign DisplayName="GenAIModel Assign" sap2010:WorkflowViewState.IdRef="Assign_97">
            <Assign.To>
              <OutArgument x:TypeArguments="x:Object">[miscValues("GenAIModel")]</OutArgument>
            </Assign.To>
            <Assign.Value>
              <InArgument x:TypeArguments="x:String">[GenAIModel]</InArgument>
            </Assign.Value>
          </Assign>
          <Assign DisplayName="GenAIModelVersion Assign" sap2010:WorkflowViewState.IdRef="Assign_98">
            <Assign.To>
              <OutArgument x:TypeArguments="x:Object">[miscValues("GenAIModelVersion")]</OutArgument>
            </Assign.To>
            <Assign.Value>
              <InArgument x:TypeArguments="x:Object">[GenAIModelVersion]</InArgument>
            </Assign.Value>
          </Assign>
          <Assign DisplayName="Assign configurationFolder" sap2010:WorkflowViewState.IdRef="Assign_99">
            <Assign.To>
              <OutArgument x:TypeArguments="x:Object">[miscValues("configurationFolder")]</OutArgument>
            </Assign.To>
            <Assign.Value>
              <InArgument x:TypeArguments="x:Object">[configurationFolder]</InArgument>
            </Assign.Value>
          </Assign>
          <Assign DisplayName="Assign emailAccount" sap2010:WorkflowViewState.IdRef="Assign_100">
            <Assign.To>
              <OutArgument x:TypeArguments="x:Object">[miscValues("emailAccount")]</OutArgument>
            </Assign.To>
            <Assign.Value>
              <InArgument x:TypeArguments="x:Object">[emailAccount]</InArgument>
            </Assign.Value>
          </Assign>
          <Assign DisplayName="Assign emailFolder" sap2010:WorkflowViewState.IdRef="Assign_101">
            <Assign.To>
              <OutArgument x:TypeArguments="x:Object">[miscValues("emailFolder")]</OutArgument>
            </Assign.To>
            <Assign.Value>
              <InArgument x:TypeArguments="x:Object">[emailFolder]</InArgument>
            </Assign.Value>
          </Assign>
          <Assign DisplayName="Assign numberOfEmails" sap2010:WorkflowViewState.IdRef="Assign_102">
            <Assign.To>
              <OutArgument x:TypeArguments="x:Object">[miscValues("numberOfEmails")]</OutArgument>
            </Assign.To>
            <Assign.Value>
              <InArgument x:TypeArguments="x:Object">[numberOfEmails]</InArgument>
            </Assign.Value>
          </Assign>
          <Assign DisplayName="Assign userIdentifier" sap2010:WorkflowViewState.IdRef="Assign_103">
            <Assign.To>
              <OutArgument x:TypeArguments="x:Object">[miscValues("userIdentifier")]</OutArgument>
            </Assign.To>
            <Assign.Value>
              <InArgument x:TypeArguments="x:Object">[userIdentifier]</InArgument>
            </Assign.Value>
          </Assign>
          <Assign DisplayName="Assign distributionType" sap2010:WorkflowViewState.IdRef="Assign_104">
            <Assign.To>
              <OutArgument x:TypeArguments="x:Object">[miscValues("distributionType")]</OutArgument>
            </Assign.To>
            <Assign.Value>
              <InArgument x:TypeArguments="x:Object">[distributionType]</InArgument>
            </Assign.Value>
          </Assign>
          <Assign DisplayName="Assign division" sap2010:WorkflowViewState.IdRef="Assign_105">
            <Assign.To>
              <OutArgument x:TypeArguments="x:Object">[miscValues("division")]</OutArgument>
            </Assign.To>
            <Assign.Value>
              <InArgument x:TypeArguments="x:Object">[division]</InArgument>
            </Assign.Value>
          </Assign>
          <Assign DisplayName="Assign tenantID" sap2010:WorkflowViewState.IdRef="Assign_106">
            <Assign.To>
              <OutArgument x:TypeArguments="x:Object">[miscValues("tenantID")]</OutArgument>
            </Assign.To>
            <Assign.Value>
              <InArgument x:TypeArguments="x:Object">[tenantID]</InArgument>
            </Assign.Value>
          </Assign>
          <Assign DisplayName="Assign projectPath" sap2010:WorkflowViewState.IdRef="Assign_107">
            <Assign.To>
              <OutArgument x:TypeArguments="x:Object">[miscValues("projectPath")]</OutArgument>
            </Assign.To>
            <Assign.Value>
              <InArgument x:TypeArguments="x:Object">[projectPath]</InArgument>
            </Assign.Value>
          </Assign>
          <Assign DisplayName="Assign datalakeAPILogicalId" sap2010:WorkflowViewState.IdRef="Assign_108">
            <Assign.To>
              <OutArgument x:TypeArguments="x:Object">[miscValues("datalakeAPILogicalId")]</OutArgument>
            </Assign.To>
            <Assign.Value>
              <InArgument x:TypeArguments="x:Object">[datalakeAPILogicalId]</InArgument>
            </Assign.Value>
          </Assign>
          <Assign DisplayName="Assign Match3Way" sap2010:WorkflowViewState.IdRef="Assign_109">
            <Assign.To>
              <OutArgument x:TypeArguments="x:Object">[miscValues("Match3Way")]</OutArgument>
            </Assign.To>
            <Assign.Value>
              <InArgument x:TypeArguments="x:Object">[Match3Way]</InArgument>
            </Assign.Value>
          </Assign>
          <Assign DisplayName="Assign Start Time" sap2010:WorkflowViewState.IdRef="Assign_110">
            <Assign.To>
              <OutArgument x:TypeArguments="x:Object">[miscValues("StartTime")]</OutArgument>
            </Assign.To>
            <Assign.Value>
              <InArgument x:TypeArguments="x:Object">[strStartTime]</InArgument>
            </Assign.Value>
          </Assign>
          <Assign DisplayName="Assign DlComments" sap2010:WorkflowViewState.IdRef="Assign_113">
            <Assign.To>
              <OutArgument x:TypeArguments="x:Object">[miscValues("Comments")]</OutArgument>
            </Assign.To>
            <Assign.Value>
              <InArgument x:TypeArguments="x:Object">[""]</InArgument>
            </Assign.Value>
          </Assign>
          <Assign DisplayName="Assign geoc" sap2010:WorkflowViewState.IdRef="Assign_137">
            <Assign.To>
              <OutArgument x:TypeArguments="x:Object">[miscValues("geoc")]</OutArgument>
            </Assign.To>
            <Assign.Value>
              <InArgument x:TypeArguments="x:Object">[""]</InArgument>
            </Assign.Value>
          </Assign>
          <Assign DisplayName="Assign Additional Info" sap2010:WorkflowViewState.IdRef="Assign_114">
            <Assign.To>
              <OutArgument x:TypeArguments="x:Object">[miscValues("addInfo")]</OutArgument>
            </Assign.To>
            <Assign.Value>
              <InArgument x:TypeArguments="x:Object">[""]</InArgument>
            </Assign.Value>
          </Assign>
          <Assign DisplayName="Assign Additional Info" sap2010:WorkflowViewState.IdRef="Assign_128">
            <Assign.To>
              <OutArgument x:TypeArguments="x:Object">[miscValues("BrowserName")]</OutArgument>
            </Assign.To>
            <Assign.Value>
              <InArgument x:TypeArguments="x:Object">[BrowserName]</InArgument>
            </Assign.Value>
          </Assign>
        </Sequence>
        <If Condition="[Ctype(miscValues(&quot;processEmails&quot;).ToString,Boolean) and (miscValues(&quot;invoiceSource&quot;).ToString =&quot;OutlookClientEmail&quot; or  miscValues(&quot;invoiceSource&quot;).ToString =&quot;OutlookGraphEmail&quot;)]" DisplayName="IfProcessEmails" sap2010:WorkflowViewState.IdRef="If_40">
          <If.Then>
            <iaw:InvokeWorkflow OutputArguments="{x:Null}" ResponseCode="{x:Null}" Arguments="[New Dictionary(Of String, Object) From {{&quot;configurationFolder&quot;,configurationFolder},{&quot;emailAccount&quot;,emailAccount},{&quot;emailFolder&quot;,emailFolder},{&quot;numberOfEmails&quot;,numberOfEmails},{&quot;logFile&quot;,logFile},{&quot;tenantID&quot;,tenantID},&#xA;{&quot;colemanAPI&quot;,miscValues(&quot;colemanAPI&quot;).ToString},{&quot;userIdentifier&quot;,userIdentifier},{&quot;distributionType&quot;,distributionType},{&quot;projectPath&quot;,projectPath},{&quot;enableMessageBoxes&quot;,enableMessageBoxes},&#xA;{&quot;poFilterValues&quot;,miscValues(&quot;poFilterValues&quot;).ToString},{&quot;poFilterCondition&quot;,miscValues(&quot;poFilterCondition&quot;).ToString},{&quot;datalakeAPILogicalId&quot;,miscValues(&quot;datalakeAPILogicalId&quot;).ToString},&#xA;{&quot;chargeCode&quot;,miscValues(&quot;chargeCode&quot;).ToString},{&quot;discountCode&quot;,miscValues(&quot;discountCode&quot;).ToString},{&quot;authUser&quot;,miscValues(&quot;authUser&quot;).ToString},{&quot;imsAPIUrl&quot;,miscValues(&quot;imsAPIUrl&quot;).ToString},&#xA;{&quot;extractNumericFromPO&quot;,CType(miscValues(&quot;extractNumericFromPO&quot;).ToString,Boolean)},{&quot;vatCodeConfig&quot;,miscValues(&quot;vatCodeConfig&quot;).ToString},&#xA;{&quot;poDiscountsHandlingConfig&quot;,CType(miscValues(&quot;poDiscountsHandlingConfig&quot;).ToString,Boolean)},{&quot;matchVendorItemCode&quot;,CType(miscValues(&quot;matchVendorItemCode&quot;).ToString,Boolean)},&#xA;{&quot;invoiceSource&quot;,miscValues(&quot;invoiceSource&quot;).ToString},{&quot;extractFromWidgetDatalake&quot;,CType(miscValues(&quot;extractFromWidgetDatalake&quot;).ToString,Boolean)},&#xA;{&quot;approvalRequired&quot;,CType(miscValues(&quot;approvalRequired&quot;).ToString,Boolean)},{&quot;approvalWorkflow&quot;,miscValues(&quot;approvalWorkflow&quot;).ToString},&#xA;{&quot;checkAmountBussinessRule&quot;,miscValues(&quot;checkAmountBussinessRule&quot;).ToString},{&quot;processExpenseInvoice&quot;,CType(miscValues(&quot;processExpenseInvoice&quot;).ToString,Boolean)},&#xA;{&quot;miscValues&quot;,miscValues},{&quot;MasterDownloads&quot;,configurationFolder+&quot;\OutlookDownloads\MasterDownloads&quot;}}]" ContinueOnError="False" DisplayName="GetOutlookEmails Invoke Workflow" sap2010:WorkflowViewState.IdRef="InvokeWorkflow_26" WorkflowFile="[projectPath+&quot;\Outlook_ProcessNew.xaml&quot;]" />
          </If.Then>
          <If.Else>
            <ias:Append_Line ErrorCode="{x:Null}" ContinueOnError="True" DisplayName="Log ProcessEmails False" sap2010:WorkflowViewState.IdRef="Append_Line_33" Line="[&quot;ProcessEmails value is &quot;+miscValues(&quot;processEmails&quot;).ToString +&quot;and invoiceSource value is &quot;+miscValues(&quot;invoiceSource&quot;).ToString]" Source="[logFile]" />
          </If.Else>
        </If>
        <If Condition="[Ctype(miscValues(&quot;processFolders&quot;).ToString,Boolean) and (miscValues(&quot;invoiceFolderPath&quot;).ToString &lt;&gt; &quot;&quot; and miscValues(&quot;invoiceFolderPath&quot;).ToString &lt;&gt; &quot;NA&quot;)]" DisplayName="IfProcessFolders" sap2010:WorkflowViewState.IdRef="If_41">
          <If.Then>
            <iaw:InvokeWorkflow OutputArguments="{x:Null}" ResponseCode="{x:Null}" Arguments="[New Dictionary(Of String, Object) From {{&quot;configurationFolder&quot;,configurationFolder},{&quot;logFile&quot;,logFile},{&quot;tenantID&quot;,tenantID},{&quot;colemanAPI&quot;,miscValues(&quot;colemanAPI&quot;).ToString},{&quot;userIdentifier&quot;,userIdentifier},{&quot;distributionType&quot;,distributionType},{&quot;projectPath&quot;,projectPath},{&quot;enableMessageBoxes&quot;,enableMessageBoxes},{&quot;poFilterValues&quot;,miscValues(&quot;poFilterValues&quot;).ToString},{&quot;poFilterCondition&quot;,miscValues(&quot;poFilterCondition&quot;).ToString},{&quot;datalakeAPILogicalId&quot;,miscValues(&quot;datalakeAPILogicalId&quot;).ToString},{&quot;chargeCode&quot;,miscValues(&quot;chargeCode&quot;).ToString},{&quot;discountCode&quot;,miscValues(&quot;discountCode&quot;).ToString},{&quot;authUser&quot;,miscValues(&quot;authUser&quot;).ToString},{&quot;imsAPIUrl&quot;,miscValues(&quot;imsAPIUrl&quot;).ToString},{&quot;invoiceFolderPath&quot;,miscValues(&quot;invoiceFolderPath&quot;).ToString},{&quot;vatCodeConfig&quot;,miscValues(&quot;vatCodeConfig&quot;).ToString},{&quot;poDiscountsHandlingConfig&quot;,miscValues(&quot;poDiscountsHandlingConfig&quot;).ToString},{&quot;matchVendorItemCode&quot;,CType(miscValues(&quot;matchVendorItemCode&quot;).ToString,Boolean)},{&quot;extractNumericFromPO&quot;,CType(miscValues(&quot;extractNumericFromPO&quot;).ToString,Boolean)},{&quot;extractFromWidgetDatalake&quot;,CType(miscValues(&quot;extractFromWidgetDatalake&quot;).ToString,Boolean)},{&quot;approvalRequired&quot;,CType(miscValues(&quot;approvalRequired&quot;).ToString,Boolean)},{&quot;approvalWorkflow&quot;,miscValues(&quot;approvalWorkflow&quot;).ToString},{&quot;checkAmountBussinessRule&quot;,miscValues(&quot;checkAmountBussinessRule&quot;).ToString},{&quot;miscValues&quot;,miscValues},{&quot;processExpenseInvoice&quot;,CType(miscValues(&quot;processExpenseInvoice&quot;).ToString,Boolean)}}]" ContinueOnError="False" DisplayName="Process Folders" sap2010:WorkflowViewState.IdRef="InvokeWorkflow_27" WorkflowFile="[projectPath+&quot;\ReadFilesFromFolder.xaml&quot;]" />
          </If.Then>
          <If.Else>
            <ias:Append_Line ErrorCode="{x:Null}" ContinueOnError="True" DisplayName="Log processFoldersFalse" sap2010:WorkflowViewState.IdRef="Append_Line_34" Line="[&quot;processFolders value is &quot;+miscValues(&quot;processFolders&quot;).ToString +&quot; and invoiceFolderPath value is &quot;+miscValues(&quot;invoiceFolderPath&quot;).ToString]" Source="[logFile]" />
          </If.Else>
        </If>
        <If Condition="[Ctype(miscValues(&quot;extractFromWidgetDatalake&quot;).ToString,Boolean) AND Ctype(miscValues(&quot;reprocess&quot;).ToString,Boolean)]" DisplayName="extractFromWidgetDatalake If" sap2010:WorkflowViewState.IdRef="If_43">
          <If.Then>
            <Sequence DisplayName="extractFromWidgetDatalake Sequence" sap2010:WorkflowViewState.IdRef="Sequence_53">
              <iaw:InvokeWorkflow OutputArguments="{x:Null}" Arguments="[New Dictionary(Of String, Object) From {{&quot;configurationFolder&quot;,configurationFolder},{&quot;tenantID&quot;,tenantID},{&quot;colemanAPI&quot;,miscValues(&quot;colemanAPI&quot;).ToString},{&quot;logFile&quot;,logFile},{&quot;distributionType&quot;,distributionType},{&quot;userIdentifier&quot;,userIdentifier},{&quot;projectPath&quot;,projectPath},{&quot;miscValues&quot;,miscValues},{&quot;maxNotReceivedCount&quot;,CType(miscValues(&quot;maxNotReceivedCount&quot;).ToString,Integer)},{&quot;datalakeAPILogicalId&quot;,miscValues(&quot;datalakeAPILogicalId&quot;).ToString},{&quot;chargeCode&quot;,miscValues(&quot;chargeCode&quot;).ToString},{&quot;discountCode&quot;,miscValues(&quot;discountCode&quot;).ToString},{&quot;authUser&quot;,miscValues(&quot;authUser&quot;).ToString},{&quot;imsAPIUrl&quot;,miscValues(&quot;imsAPIUrl&quot;).ToString},{&quot;extractFromWidgetDatalake&quot;,CType(miscValues(&quot;extractFromWidgetDatalake&quot;).ToString,Boolean)},{&quot;approvalRequired&quot;,CType(miscValues(&quot;approvalRequired&quot;).ToString,Boolean)},{&quot;approvalWorkflow&quot;,miscValues(&quot;approvalWorkflow&quot;).ToString},{&quot;checkAmountBussinessRule&quot;,miscValues(&quot;checkAmountBussinessRule&quot;).ToString},{&quot;matchVendorItemCode&quot;,CType(miscValues(&quot;matchVendorItemCode&quot;).ToString,Boolean)},{&quot;enableMessageBoxes&quot;,enableMessageBoxes}}]" ContinueOnError="True" DisplayName="Read Datalake" sap2010:WorkflowViewState.IdRef="InvokeWorkflow_28" ResponseCode="[datalakeResponseCode]" WorkflowFile="[projectPath+&quot;\ExtractFromDatalake - Copy.xaml&quot;]" />
              <If Condition="[datalakeResponseCode&lt;&gt; 200]" sap2010:WorkflowViewState.IdRef="If_42">
                <If.Then>
                  <ias:Append_Line ErrorCode="{x:Null}" ContinueOnError="True" DisplayName="Datalake error Append Line" sap2010:WorkflowViewState.IdRef="Append_Line_35" Line="Error occured while retriving the data from the Datalake." Source="[logFile]" />
                </If.Then>
              </If>
            </Sequence>
          </If.Then>
          <If.Else>
            <ias:Append_Line ErrorCode="{x:Null}" ContinueOnError="True" DisplayName="Log extractFromWidgetDatalake and reprocess in Else block" sap2010:WorkflowViewState.IdRef="Append_Line_36" Line="[&quot;extractFromWidgetDatalake value is &quot;+miscValues(&quot;extractFromWidgetDatalake&quot;).ToString +&quot;and reprocess value is &quot;+miscValues(&quot;reprocess&quot;).ToString]" Source="[logFile]" />
          </If.Else>
        </If>
      </Sequence>
    </TryCatch.Try>
    <TryCatch.Catches>
      <Catch x:TypeArguments="s:Exception" sap2010:WorkflowViewState.IdRef="Catch`1_3">
        <ActivityAction x:TypeArguments="s:Exception">
          <ActivityAction.Argument>
            <DelegateInArgument x:TypeArguments="s:Exception" Name="exception" />
          </ActivityAction.Argument>
          <ias:Append_Line ErrorCode="{x:Null}" ContinueOnError="True" DisplayName="Main Catch Block Append Line" sap2010:WorkflowViewState.IdRef="Append_Line_39" Line="[&quot;&quot;+Environment.NewLine+&quot;M3 Vendor Invoice Procesing Ended with below exception - &quot;+Environment.NewLine+Exception.Message]" Source="[logFile]" />
        </ActivityAction>
      </Catch>
    </TryCatch.Catches>
    <TryCatch.Finally>
      <ias:Append_Line ErrorCode="{x:Null}" ContinueOnError="True" DisplayName="End Time Append Line" sap2010:WorkflowViewState.IdRef="Append_Line_25" Line="[&quot;End Time : &quot; +System.DateTime.Now.ToString(&quot;yyyy/MM/dd HH:mm:ss&quot;)]" Source="[logFile]" />
    </TryCatch.Finally>
    <sads:DebugSymbol.Symbol>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</sads:DebugSymbol.Symbol>
  </TryCatch>
  <sap2010:WorkflowViewState.ViewStateManager>
    <sap2010:ViewStateManager>
      <sap2010:ViewStateData Id="Assign_112" sap:VirtualizedContainerService.HintSize="711,60" />
      <sap2010:ViewStateData Id="File_Delete_16" sap:VirtualizedContainerService.HintSize="264,22" />
      <sap2010:ViewStateData Id="IONAPIRequestWizard_5" sap:VirtualizedContainerService.HintSize="264,22" />
      <sap2010:ViewStateData Id="Assign_115" sap:VirtualizedContainerService.HintSize="242,62" />
      <sap2010:ViewStateData Id="JQTransform_5" sap:VirtualizedContainerService.HintSize="242,22" />
      <sap2010:ViewStateData Id="Assign_116" sap:VirtualizedContainerService.HintSize="242,62" />
      <sap2010:ViewStateData Id="Assign_117" sap:VirtualizedContainerService.HintSize="242,62" />
      <sap2010:ViewStateData Id="DownloadFile_URL_3" sap:VirtualizedContainerService.HintSize="242,22" />
      <sap2010:ViewStateData Id="Sequence_55" sap:VirtualizedContainerService.HintSize="264,514">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">True</x:Boolean>
            <x:Boolean x:Key="IsPinned">False</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="File_Read_1" sap:VirtualizedContainerService.HintSize="264,22" />
      <sap2010:ViewStateData Id="DeserializeJSON_1" sap:VirtualizedContainerService.HintSize="264,22" />
      <sap2010:ViewStateData Id="Assign_126" sap:VirtualizedContainerService.HintSize="264,65.3333333333333" />
      <sap2010:ViewStateData Id="Sequence_60" sap:VirtualizedContainerService.HintSize="711,51">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">False</x:Boolean>
            <x:Boolean x:Key="IsPinned">False</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="InvokeWorkflow_24" sap:VirtualizedContainerService.HintSize="711,22" />
      <sap2010:ViewStateData Id="Assign_85" sap:VirtualizedContainerService.HintSize="711,60" />
      <sap2010:ViewStateData Id="Append_Line_27" sap:VirtualizedContainerService.HintSize="711,22" />
      <sap2010:ViewStateData Id="Append_Line_28" sap:VirtualizedContainerService.HintSize="711,22" />
      <sap2010:ViewStateData Id="Append_Line_29" sap:VirtualizedContainerService.HintSize="711,22" />
      <sap2010:ViewStateData Id="Assign_86" sap:VirtualizedContainerService.HintSize="711,60" />
      <sap2010:ViewStateData Id="Assign_87" sap:VirtualizedContainerService.HintSize="711,60" />
      <sap2010:ViewStateData Id="Assign_127" sap:VirtualizedContainerService.HintSize="711,60" />
      <sap2010:ViewStateData Id="Assign_88" sap:VirtualizedContainerService.HintSize="242,60" />
      <sap2010:ViewStateData Id="Assign_89" sap:VirtualizedContainerService.HintSize="242,64" />
      <sap2010:ViewStateData Id="Sequence_50" sap:VirtualizedContainerService.HintSize="711,288">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">True</x:Boolean>
            <x:Boolean x:Key="IsPinned">False</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="Assign_90" sap:VirtualizedContainerService.HintSize="242,60" />
      <sap2010:ViewStateData Id="Assign_91" sap:VirtualizedContainerService.HintSize="242,60" />
      <sap2010:ViewStateData Id="File_Delete_15" sap:VirtualizedContainerService.HintSize="242,22" />
      <sap2010:ViewStateData Id="InvokeWorkflow_25" sap:VirtualizedContainerService.HintSize="242,22" />
      <sap2010:ViewStateData Id="Assign_92" sap:VirtualizedContainerService.HintSize="242,60" />
      <sap2010:ViewStateData Id="Assign_93" sap:VirtualizedContainerService.HintSize="242,60" />
      <sap2010:ViewStateData Id="Assign_94" sap:VirtualizedContainerService.HintSize="242,60" />
      <sap2010:ViewStateData Id="Assign_95" sap:VirtualizedContainerService.HintSize="242,62" />
      <sap2010:ViewStateData Id="If_38" sap:VirtualizedContainerService.HintSize="242,51">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">False</x:Boolean>
            <x:Boolean x:Key="IsPinned">False</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="Assign_96" sap:VirtualizedContainerService.HintSize="242,60" />
      <sap2010:ViewStateData Id="If_39" sap:VirtualizedContainerService.HintSize="242,51">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">False</x:Boolean>
            <x:Boolean x:Key="IsPinned">False</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="Sequence_51" sap:VirtualizedContainerService.HintSize="200,51">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">False</x:Boolean>
            <x:Boolean x:Key="IsPinned">False</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="ForEach`1_4" sap:VirtualizedContainerService.HintSize="287,208">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">True</x:Boolean>
            <x:Boolean x:Key="IsPinned">False</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="Append_Line_31" sap:VirtualizedContainerService.HintSize="200,22" />
      <sap2010:ViewStateData Id="Switch`1_6" sap:VirtualizedContainerService.HintSize="711,51">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">False</x:Boolean>
            <x:Boolean x:Key="IsPinned">False</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="Assign_136" sap:VirtualizedContainerService.HintSize="242,60" />
      <sap2010:ViewStateData Id="Assign_130" sap:VirtualizedContainerService.HintSize="242,60" />
      <sap2010:ViewStateData Id="Assign_131" sap:VirtualizedContainerService.HintSize="242,60" />
      <sap2010:ViewStateData Id="Assign_132" sap:VirtualizedContainerService.HintSize="242,60" />
      <sap2010:ViewStateData Id="Assign_133" sap:VirtualizedContainerService.HintSize="242,60" />
      <sap2010:ViewStateData Id="Assign_134" sap:VirtualizedContainerService.HintSize="242,60" />
      <sap2010:ViewStateData Id="Assign_135" sap:VirtualizedContainerService.HintSize="242,60" />
      <sap2010:ViewStateData Id="Assign_129" sap:VirtualizedContainerService.HintSize="242,60" />
      <sap2010:ViewStateData Id="Assign_97" sap:VirtualizedContainerService.HintSize="242,60" />
      <sap2010:ViewStateData Id="Assign_98" sap:VirtualizedContainerService.HintSize="242,60" />
      <sap2010:ViewStateData Id="Assign_99" sap:VirtualizedContainerService.HintSize="242,60" />
      <sap2010:ViewStateData Id="Assign_100" sap:VirtualizedContainerService.HintSize="242,60" />
      <sap2010:ViewStateData Id="Assign_101" sap:VirtualizedContainerService.HintSize="242,60" />
      <sap2010:ViewStateData Id="Assign_102" sap:VirtualizedContainerService.HintSize="242,60" />
      <sap2010:ViewStateData Id="Assign_103" sap:VirtualizedContainerService.HintSize="242,60" />
      <sap2010:ViewStateData Id="Assign_104" sap:VirtualizedContainerService.HintSize="242,60" />
      <sap2010:ViewStateData Id="Assign_105" sap:VirtualizedContainerService.HintSize="242,60" />
      <sap2010:ViewStateData Id="Assign_106" sap:VirtualizedContainerService.HintSize="242,60" />
      <sap2010:ViewStateData Id="Assign_107" sap:VirtualizedContainerService.HintSize="242,60" />
      <sap2010:ViewStateData Id="Assign_108" sap:VirtualizedContainerService.HintSize="242,60" />
      <sap2010:ViewStateData Id="Assign_109" sap:VirtualizedContainerService.HintSize="242,60" />
      <sap2010:ViewStateData Id="Assign_110" sap:VirtualizedContainerService.HintSize="242,60" />
      <sap2010:ViewStateData Id="Assign_113" sap:VirtualizedContainerService.HintSize="242,60" />
      <sap2010:ViewStateData Id="Assign_137" sap:VirtualizedContainerService.HintSize="242,60" />
      <sap2010:ViewStateData Id="Assign_114" sap:VirtualizedContainerService.HintSize="242,60" />
      <sap2010:ViewStateData Id="Assign_128" sap:VirtualizedContainerService.HintSize="242,60" />
      <sap2010:ViewStateData Id="Sequence_52" sap:VirtualizedContainerService.HintSize="711,2684">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">True</x:Boolean>
            <x:Boolean x:Key="IsPinned">False</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="InvokeWorkflow_26" sap:VirtualizedContainerService.HintSize="200,22" />
      <sap2010:ViewStateData Id="Append_Line_33" sap:VirtualizedContainerService.HintSize="200,22" />
      <sap2010:ViewStateData Id="If_40" sap:VirtualizedContainerService.HintSize="711,208">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">True</x:Boolean>
            <x:Boolean x:Key="IsPinned">False</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="InvokeWorkflow_27" sap:VirtualizedContainerService.HintSize="200,22" />
      <sap2010:ViewStateData Id="Append_Line_34" sap:VirtualizedContainerService.HintSize="200,22" />
      <sap2010:ViewStateData Id="If_41" sap:VirtualizedContainerService.HintSize="711,208">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">True</x:Boolean>
            <x:Boolean x:Key="IsPinned">False</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="InvokeWorkflow_28" sap:VirtualizedContainerService.HintSize="464,22" />
      <sap2010:ViewStateData Id="Append_Line_35" sap:VirtualizedContainerService.HintSize="200,22" />
      <sap2010:ViewStateData Id="If_42" sap:VirtualizedContainerService.HintSize="464,208">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">True</x:Boolean>
            <x:Boolean x:Key="IsPinned">False</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="Sequence_53" sap:VirtualizedContainerService.HintSize="486,394">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">True</x:Boolean>
            <x:Boolean x:Key="IsPinned">False</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="Append_Line_36" sap:VirtualizedContainerService.HintSize="200,22" />
      <sap2010:ViewStateData Id="If_43" sap:VirtualizedContainerService.HintSize="711,542" />
      <sap2010:ViewStateData Id="Sequence_54" sap:VirtualizedContainerService.HintSize="733,5144">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">True</x:Boolean>
            <x:Boolean x:Key="IsPinned">False</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="Append_Line_39" sap:VirtualizedContainerService.HintSize="200,22" />
      <sap2010:ViewStateData Id="Catch`1_3" sap:VirtualizedContainerService.HintSize="737,21">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">False</x:Boolean>
            <x:Boolean x:Key="IsPinned">False</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="Append_Line_25" sap:VirtualizedContainerService.HintSize="200,22" />
      <sap2010:ViewStateData Id="TryCatch_3" sap:VirtualizedContainerService.HintSize="751,5491" />
      <sap2010:ViewStateData Id="RehostedWorkflowDesigner.Workflow_1" sap:VirtualizedContainerService.HintSize="791,5691" />
    </sap2010:ViewStateManager>
  </sap2010:WorkflowViewState.ViewStateManager>
</Activity>