﻿<Activity mc:Ignorable="sap sap2010 sads" x:Class="RehostedWorkflowDesigner.Workflow" this:Workflow.configurationFolder="C:\M3VendorInvoiceProcessingIntersnack" this:Workflow.emailAccount="<EMAIL>" this:Workflow.emailFolder="BioPartner" this:Workflow.numberOfEmails="10" this:Workflow.userIdentifier="4d350a48-6202-4e0e-9770-e3434fc35412" this:Workflow.distributionType="USER" this:Workflow.enableMessageBoxes="False" this:Workflow.division="NOA" this:Workflow.tenantID="https://mingle-ionapi.eu1.inforcloudsuite.com/INTERSNACK_DEV/" this:Workflow.projectPath="[Environment.GetFolderPath(Environment.SpecialFolder.UserProfile) + &quot;\AppData\Local\InforRPA\M3InvoiceProcessingGenAIV3&quot;]" this:Workflow.datalakeAPILogicalId="infor.ims.imsfromrpastudio" this:Workflow.handleCashDiscount="True" this:Workflow.Match3Way="True"
 xmlns="http://schemas.microsoft.com/netfx/2009/xaml/activities"
 xmlns:iad="clr-namespace:Infor.Activities.Debug;assembly=Infor.Activities.Debug"
 xmlns:iae="clr-namespace:Infor.Activities.Excel;assembly=Infor.Activities.Excel"
 xmlns:iai="clr-namespace:Infor.Activities.IONAPI;assembly=Infor.Activities.IONAPI"
 xmlns:ias="clr-namespace:Infor.Activities.Sys;assembly=Infor.Activities.Sys"
 xmlns:iaw="clr-namespace:Infor.Activities.Workflow;assembly=Infor.Activities.Workflow"
 xmlns:iru="clr-namespace:Infor.RPA.Utilities;assembly=Infor.RPA.Utilities"
 xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006"
 xmlns:mva="clr-namespace:Microsoft.VisualBasic.Activities;assembly=System.Activities"
 xmlns:njl="clr-namespace:Newtonsoft.Json.Linq;assembly=Newtonsoft.Json"
 xmlns:s="clr-namespace:System;assembly=mscorlib"
 xmlns:sads="http://schemas.microsoft.com/netfx/2010/xaml/activities/debugger"
 xmlns:sap="http://schemas.microsoft.com/netfx/2009/xaml/activities/presentation"
 xmlns:sap2010="http://schemas.microsoft.com/netfx/2010/xaml/activities/presentation"
 xmlns:scg="clr-namespace:System.Collections.Generic;assembly=mscorlib"
 xmlns:sco="clr-namespace:System.Collections.ObjectModel;assembly=mscorlib"
 xmlns:sd="clr-namespace:System.Data;assembly=System.Data"
 xmlns:this="clr-namespace:RehostedWorkflowDesigner"
 xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml">
  <x:Members>
    <x:Property Name="configurationFolder" Type="InArgument(x:String)" />
    <x:Property Name="emailAccount" Type="InArgument(x:String)" />
    <x:Property Name="emailFolder" Type="InArgument(x:String)" />
    <x:Property Name="numberOfEmails" Type="InArgument(x:Int32)" />
    <x:Property Name="userIdentifier" Type="InArgument(x:String)" />
    <x:Property Name="distributionType" Type="InArgument(x:String)" />
    <x:Property Name="enableMessageBoxes" Type="InArgument(x:Boolean)" />
    <x:Property Name="division" Type="InArgument(x:String)" />
    <x:Property Name="tenantID" Type="InArgument(x:String)" />
    <x:Property Name="projectPath" Type="InArgument(x:String)" />
    <x:Property Name="datalakeAPILogicalId" Type="InArgument(x:String)" />
    <x:Property Name="handleCashDiscount" Type="InArgument(x:Boolean)" />
    <x:Property Name="Match3Way" Type="InArgument(x:Boolean)" />
  </x:Members>
  <mva:VisualBasic.Settings>
    <x:Null />
  </mva:VisualBasic.Settings>
  <sap2010:WorkflowViewState.IdRef>RehostedWorkflowDesigner.Workflow_1</sap2010:WorkflowViewState.IdRef>
  <TextExpression.NamespacesForImplementation>
    <sco:Collection x:TypeArguments="x:String">
      <x:String>System.Activities</x:String>
      <x:String>System.Activities.Statements</x:String>
      <x:String>System.Activities.Expressions</x:String>
      <x:String>System.Activities.Validation</x:String>
      <x:String>System.Activities.XamlIntegration</x:String>
      <x:String>Infor.Activities.Email</x:String>
      <x:String>Infor.Activities.Workflow</x:String>
      <x:String>Microsoft.VisualBasic.Activities</x:String>
      <x:String>System</x:String>
      <x:String>System.Activities.Debugger.Symbol</x:String>
      <x:String>System.Collections.Generic</x:String>
      <x:String>System.Windows.Markup</x:String>
      <x:String>Microsoft.VisualBasic.CompilerServices</x:String>
      <x:String>System.Linq</x:String>
      <x:String>System.Globalization</x:String>
      <x:String>Infor.RPA.Utilities</x:String>
      <x:String>Newtonsoft.Json.Linq</x:String>
      <x:String>Newtonsoft.Json</x:String>
      <x:String>System.Text.RegularExpressions</x:String>
      <x:String>System.Data</x:String>
      <x:String>System.Xml.Serialization</x:String>
    </sco:Collection>
  </TextExpression.NamespacesForImplementation>
  <TextExpression.ReferencesForImplementation>
    <sco:Collection x:TypeArguments="AssemblyReference">
      <AssemblyReference>System.Activities</AssemblyReference>
      <AssemblyReference>Infor.Activities.Email</AssemblyReference>
      <AssemblyReference>Infor.Activities.Workflow</AssemblyReference>
      <AssemblyReference>mscorlib</AssemblyReference>
      <AssemblyReference>System.Core</AssemblyReference>
      <AssemblyReference>System</AssemblyReference>
      <AssemblyReference>System.ServiceModel</AssemblyReference>
      <AssemblyReference>System.Runtime.WindowsRuntime</AssemblyReference>
      <AssemblyReference>Microsoft.Bcl.AsyncInterfaces</AssemblyReference>
      <AssemblyReference>PresentationFramework</AssemblyReference>
      <AssemblyReference>WindowsBase</AssemblyReference>
      <AssemblyReference>PresentationCore</AssemblyReference>
      <AssemblyReference>System.Xaml</AssemblyReference>
      <AssemblyReference>Microsoft.VisualBasic</AssemblyReference>
      <AssemblyReference>Infor.RPA.Utilities</AssemblyReference>
      <AssemblyReference>Newtonsoft.Json</AssemblyReference>
      <AssemblyReference>System.ComponentModel.Composition</AssemblyReference>
      <AssemblyReference>System.Memory</AssemblyReference>
      <AssemblyReference>System.Collections.Immutable</AssemblyReference>
      <AssemblyReference>System.Data</AssemblyReference>
      <AssemblyReference>System.Xml</AssemblyReference>
    </sco:Collection>
  </TextExpression.ReferencesForImplementation>
  <TryCatch DisplayName="TryCatch - MainSequence" sap2010:WorkflowViewState.IdRef="TryCatch_3">
    <TryCatch.Variables>
      <Variable x:TypeArguments="x:String" Name="logFile" />
      <Variable x:TypeArguments="scg:IDictionary(x:String, x:Object)" Name="CDOutput" />
      <Variable x:TypeArguments="x:String" Name="model" />
      <Variable x:TypeArguments="x:String" Name="GenAIModel" />
      <Variable x:TypeArguments="x:String" Name="GenAIModelVersion" />
      <Variable x:TypeArguments="scg:Dictionary(x:String, x:String)" Default="[New Dictionary (Of String, String)]" Name="PromptsDicitonary" />
      <Variable x:TypeArguments="sd:DataTable" Name="dtConfig" />
      <Variable x:TypeArguments="x:String" Name="version" />
    </TryCatch.Variables>
    <TryCatch.Try>
      <Sequence DisplayName="MainSequence" sap2010:WorkflowViewState.IdRef="Sequence_54">
        <Sequence.Variables>
          <Variable x:TypeArguments="x:Int32" Name="datalakeResponseCode" />
          <Variable x:TypeArguments="scg:Dictionary(x:String, x:Object)" Default="[New Dictionary(Of String, Object)]" Name="miscValues" />
          <Variable x:TypeArguments="x:Boolean" Name="GenAIPromptFileExists" />
          <Variable x:TypeArguments="scg:IDictionary(x:String, x:Object)" Name="GenAIOutput" />
          <Variable x:TypeArguments="x:String" Name="GenAIStatus" />
          <Variable x:TypeArguments="njl:JToken" Name="value" />
          <Variable x:TypeArguments="iru:ResponseObject" Name="resp" />
          <Variable x:TypeArguments="scg:List(x:String)" Name="files" />
          <Variable x:TypeArguments="x:Boolean" Name="logFileExist" />
          <Variable x:TypeArguments="x:Int32" Default="5" Name="numberOfParts" />
          <Variable x:TypeArguments="scg:List(x:String)" Default="[New List(Of String)]" Name="ListPromptFiles" />
          <Variable x:TypeArguments="x:String" Name="strStartTime" />
        </Sequence.Variables>
        <Assign sap2010:WorkflowViewState.IdRef="Assign_112">
          <Assign.To>
            <OutArgument x:TypeArguments="x:String">[strStartTime]</OutArgument>
          </Assign.To>
          <Assign.Value>
            <InArgument x:TypeArguments="x:String">[System.DateTime.Now.ToString("yyyy/MM/dd HH:mm:ss")]</InArgument>
          </Assign.Value>
        </Assign>
        <Sequence DisplayName="Download Config File" sap2010:WorkflowViewState.IdRef="Sequence_49">
          <Sequence.Variables>
            <Variable x:TypeArguments="x:Int32" Name="intCounter" />
            <Variable x:TypeArguments="iru:ResponseObject" Name="GenAIresponse" />
            <Variable x:TypeArguments="x:Int32" Name="GenAIresponseCode" />
            <Variable x:TypeArguments="njl:JToken" Name="GenAIrespToken" />
          </Sequence.Variables>
          <ias:File_Delete ErrorCode="{x:Null}" ContinueOnError="True" DisplayName="Delete File" sap2010:WorkflowViewState.IdRef="File_Delete_14" Source="[ConfigurationFolder + &quot;\RPAConfig.xlsx&quot;]" />
          <iai:IONAPIRequestWizard FileAttachments="{x:Null}" PostData="{x:Null}" QueryParameters="{x:Null}" ResponseCode="{x:Null}" ContentType="application_json" ContinueOnError="True" DisplayName="IONAPI Request to download RPAConfig File" HttpMethod="GET" sap2010:WorkflowViewState.IdRef="IONAPIRequestWizard_4" Response="[GenAIresponse]" StatusCode="[GenAIresponseCode]" Url="[tenantID+&quot;IDM/api/items/search/item?%24query=%2FGenAIPrompt%5B%40RESOURCENAME%20%3D%20%22RPAConfig.xlsx%22%5D%20SORTBY(%40LASTCHANGEDTS%20DESCENDING)&quot;]">
            <iai:IONAPIRequestWizard.Headers>
              <scg:List x:TypeArguments="scg:List(x:String)" Capacity="4">
                <scg:List x:TypeArguments="x:String" Capacity="4">
                  <x:String>Accept</x:String>
                </scg:List>
                <scg:List x:TypeArguments="x:String" Capacity="4">
                  <x:String>application/json</x:String>
                </scg:List>
              </scg:List>
            </iai:IONAPIRequestWizard.Headers>
          </iai:IONAPIRequestWizard>
          <If Condition="[GenAIresponseCode=200]" DisplayName="Check If ION API Success" sap2010:WorkflowViewState.IdRef="If_37">
            <If.Then>
              <Sequence sap2010:WorkflowViewState.IdRef="Sequence_44">
                <Sequence.Variables>
                  <Variable x:TypeArguments="x:String" Name="variable2" />
                  <Variable x:TypeArguments="njl:JToken" Name="version" />
                  <Variable x:TypeArguments="njl:JToken" Name="GenAIPromptUrl" />
                  <Variable x:TypeArguments="x:String" Name="url" />
                  <Variable x:TypeArguments="x:String" Name="ConfigPath" />
                </Sequence.Variables>
                <Assign sap2010:WorkflowViewState.IdRef="Assign_74">
                  <Assign.To>
                    <OutArgument x:TypeArguments="njl:JToken">[GenAIrespToken]</OutArgument>
                  </Assign.To>
                  <Assign.Value>
                    <InArgument x:TypeArguments="njl:JToken">[GenAIresponse.readasjson]</InArgument>
                  </Assign.Value>
                </Assign>
                <ias:JQTransform ErrorCode="{x:Null}" ContinueOnError="True" DisplayName="JQ Transform" sap2010:WorkflowViewState.IdRef="JQTransform_4" JQ=".key|.item|.resrs|.res[0]|.url" JSON="[GenAIPromptUrl]" Raw="False">
                  <ias:JQTransform.Text>
                    <scg:List x:TypeArguments="scg:List(x:String)" Capacity="4">
                      <scg:List x:TypeArguments="x:String" Capacity="4">
                        <x:String>key</x:String>
                      </scg:List>
                      <scg:List x:TypeArguments="x:String" Capacity="4">
                        <x:String>GenAIrespToken</x:String>
                      </scg:List>
                    </scg:List>
                  </ias:JQTransform.Text>
                </ias:JQTransform>
                <Assign sap2010:WorkflowViewState.IdRef="Assign_75">
                  <Assign.To>
                    <OutArgument x:TypeArguments="x:String">[url]</OutArgument>
                  </Assign.To>
                  <Assign.Value>
                    <InArgument x:TypeArguments="x:String">[GenAIPromptUrl.tostring]</InArgument>
                  </Assign.Value>
                </Assign>
                <Assign sap2010:WorkflowViewState.IdRef="Assign_76">
                  <Assign.To>
                    <OutArgument x:TypeArguments="x:String">[url]</OutArgument>
                  </Assign.To>
                  <Assign.Value>
                    <InArgument x:TypeArguments="x:String">[url.trim().substring(1,url.length()-2)]</InArgument>
                  </Assign.Value>
                </Assign>
                <ias:DownloadFile_URL ErrorCode="{x:Null}" Async="False" ContinueOnError="True" DisplayName="Download File By URL" sap2010:WorkflowViewState.IdRef="DownloadFile_URL_2" Name="RPAConfig.xlsx" OutputFile="[ConfigPath]" Target="[configurationFolder]" URL="[url]" />
              </Sequence>
            </If.Then>
            <If.Else>
              <Sequence sap2010:WorkflowViewState.IdRef="Sequence_45">
                <Assign sap2010:WorkflowViewState.IdRef="Assign_77">
                  <Assign.To>
                    <OutArgument x:TypeArguments="x:String">[GenAIStatus]</OutArgument>
                  </Assign.To>
                  <Assign.Value>
                    <InArgument x:TypeArguments="x:String">failure</InArgument>
                  </Assign.Value>
                </Assign>
                <ias:Append_Line ErrorCode="{x:Null}" ContinueOnError="True" DisplayName="Append Line" sap2010:WorkflowViewState.IdRef="Append_Line_26" Line="[&quot;Exception raised while downloading Gen AI Prompt file:&quot;+GenAIresponse.readAsText]" Source="[logFile]" />
              </Sequence>
            </If.Else>
          </If>
          <iae:ReadRange ErrorCode="{x:Null}" ContinueOnError="False" DataTable="[dtConfig]" DisplayName="Read Range" HasHeaderRow="True" sap2010:WorkflowViewState.IdRef="ReadRange_2" WorkbookPath="[ConfigurationFolder + &quot;\RPAConfig.xlsx&quot;]" WorksheetName="ProcessArguments">
            <iae:ReadRange.CellRange>
              <InArgument x:TypeArguments="x:String">
                <Literal x:TypeArguments="x:String" Value="" />
              </InArgument>
            </iae:ReadRange.CellRange>
          </iae:ReadRange>
          <Assign sap2010:WorkflowViewState.IdRef="Assign_78">
            <Assign.To>
              <OutArgument x:TypeArguments="x:Int32">[intCounter]</OutArgument>
            </Assign.To>
            <Assign.Value>
              <InArgument x:TypeArguments="x:Int32">0</InArgument>
            </Assign.Value>
          </Assign>
          <ForEach x:TypeArguments="x:Int32" DisplayName="ForEach Config Row" sap2010:WorkflowViewState.IdRef="ForEach`1_3" Values="[Enumerable.Range(intCounter,dtConfig.Rows.Count)]">
            <ActivityAction x:TypeArguments="x:Int32">
              <ActivityAction.Argument>
                <DelegateInArgument x:TypeArguments="x:Int32" Name="item" />
              </ActivityAction.Argument>
              <Switch x:TypeArguments="x:String" DisplayName="Assign Key and Values" Expression="[dtConfig.Rows(item)(2).ToString]" sap2010:WorkflowViewState.IdRef="Switch`1_5">
                <Sequence x:Key="Boolean" DisplayName="Boolean Sequence" sap2010:WorkflowViewState.IdRef="Sequence_46">
                  <Assign sap2010:WorkflowViewState.IdRef="Assign_79">
                    <Assign.To>
                      <OutArgument x:TypeArguments="x:Object">[miscValues(dtConfig.Rows(item)(0).ToString)]</OutArgument>
                    </Assign.To>
                    <Assign.Value>
                      <InArgument x:TypeArguments="x:Boolean">[CType(dtConfig.Rows(item)(1).ToString,Boolean)]</InArgument>
                    </Assign.Value>
                  </Assign>
                  <Assign sap2010:WorkflowViewState.IdRef="Assign_80">
                    <Assign.To>
                      <OutArgument x:TypeArguments="x:Int32">[intCounter]</OutArgument>
                    </Assign.To>
                    <Assign.Value>
                      <InArgument x:TypeArguments="x:Int32">[intCounter+1]</InArgument>
                    </Assign.Value>
                  </Assign>
                </Sequence>
                <Sequence x:Key="String" DisplayName="String Sequence" sap2010:WorkflowViewState.IdRef="Sequence_47">
                  <Assign sap2010:WorkflowViewState.IdRef="Assign_81">
                    <Assign.To>
                      <OutArgument x:TypeArguments="x:Object">[miscValues(dtConfig.Rows(item)(0).ToString)]</OutArgument>
                    </Assign.To>
                    <Assign.Value>
                      <InArgument x:TypeArguments="x:String">[CType(dtConfig.Rows(item)(1).ToString,String)]</InArgument>
                    </Assign.Value>
                  </Assign>
                  <Assign sap2010:WorkflowViewState.IdRef="Assign_82">
                    <Assign.To>
                      <OutArgument x:TypeArguments="x:Int32">[intCounter]</OutArgument>
                    </Assign.To>
                    <Assign.Value>
                      <InArgument x:TypeArguments="x:Int32">[intCounter+1]</InArgument>
                    </Assign.Value>
                  </Assign>
                </Sequence>
                <Sequence x:Key="Int32" DisplayName="Int32 Sequence" sap2010:WorkflowViewState.IdRef="Sequence_48">
                  <Assign sap2010:WorkflowViewState.IdRef="Assign_83">
                    <Assign.To>
                      <OutArgument x:TypeArguments="x:Object">[miscValues(dtConfig.Rows(item)(0).ToString)]</OutArgument>
                    </Assign.To>
                    <Assign.Value>
                      <InArgument x:TypeArguments="x:Int32">[CType(dtConfig.Rows(item)(1).ToString,Integer)]</InArgument>
                    </Assign.Value>
                  </Assign>
                  <Assign sap2010:WorkflowViewState.IdRef="Assign_84">
                    <Assign.To>
                      <OutArgument x:TypeArguments="x:Int32">[intCounter]</OutArgument>
                    </Assign.To>
                    <Assign.Value>
                      <InArgument x:TypeArguments="x:Int32">[intCounter+1]</InArgument>
                    </Assign.Value>
                  </Assign>
                </Sequence>
              </Switch>
            </ActivityAction>
          </ForEach>
        </Sequence>
        <iaw:InvokeWorkflow ResponseCode="{x:Null}" Arguments="[New Dictionary(Of String, Object) From {{&quot;configurationFolder&quot;,configurationFolder},{&quot;directoriesNames&quot;,miscValues(&quot;directoriesNames&quot;).ToString.split(&quot;|&quot;c).ToList},{&quot;logFolderName&quot;,miscValues(&quot;logFolderName&quot;).ToString}}]" ContinueOnError="False" DisplayName="CreateDirectories Invoke Workflow" sap2010:WorkflowViewState.IdRef="InvokeWorkflow_24" OutputArguments="[CDOutput]" WorkflowFile="[projectPath+&quot;\CreateDirectories.xaml&quot;]" />
        <Assign sap2010:WorkflowViewState.IdRef="Assign_85">
          <Assign.To>
            <OutArgument x:TypeArguments="x:String">[logFile]</OutArgument>
          </Assign.To>
          <Assign.Value>
            <InArgument x:TypeArguments="x:String">[CDOutput("logFile").ToString]</InArgument>
          </Assign.Value>
        </Assign>
        <ias:Append_Line ErrorCode="{x:Null}" ContinueOnError="True" DisplayName="Empty Append Line" sap2010:WorkflowViewState.IdRef="Append_Line_27" Line="------------------------------------------------------------------------------------------------" Source="[logFile]" />
        <ias:Append_Line ErrorCode="{x:Null}" ContinueOnError="True" DisplayName="Start time Append Line" sap2010:WorkflowViewState.IdRef="Append_Line_28" Line="[&quot;Start Time : &quot; +strStartTime]" Source="[logFile]" />
        <ias:Append_Line ErrorCode="{x:Null}" ContinueOnError="True" DisplayName="CreateDirectories Append Line" sap2010:WorkflowViewState.IdRef="Append_Line_29" Line="CreateDirectories Workflow execution completed." Source="[logFile]" />
        <TryCatch DisplayName="TryCatch - ION API file" sap2010:WorkflowViewState.IdRef="TryCatch_4">
          <TryCatch.Try>
            <iai:IONAPIRequestWizard FileAttachments="{x:Null}" Headers="{x:Null}" PostData="{x:Null}" QueryParameters="{x:Null}" ResponseCode="{x:Null}" StatusCode="{x:Null}" ContentType="application_json" ContinueOnError="False" DisplayName="IONAPI Request" HttpMethod="GET" sap2010:WorkflowViewState.IdRef="IONAPIRequestWizard_5" Response="[resp]" Url="[miscValues(&quot;IONAPI&quot;).ToString]" />
          </TryCatch.Try>
          <TryCatch.Catches>
            <Catch x:TypeArguments="s:Exception" sap2010:WorkflowViewState.IdRef="Catch`1_4">
              <ActivityAction x:TypeArguments="s:Exception">
                <ActivityAction.Argument>
                  <DelegateInArgument x:TypeArguments="s:Exception" Name="exception" />
                </ActivityAction.Argument>
                <ias:Append_Line ErrorCode="{x:Null}" ContinueOnError="True" DisplayName="Append Line Catch Block IFS ION API" sap2010:WorkflowViewState.IdRef="Append_Line_30" Line="ION API connection is missing." Source="[logFile]" />
              </ActivityAction>
            </Catch>
          </TryCatch.Catches>
        </TryCatch>
        <Assign DisplayName="AuthUser Assign" sap2010:WorkflowViewState.IdRef="Assign_86">
          <Assign.To>
            <OutArgument x:TypeArguments="x:Object">[miscValues("authUser")]</OutArgument>
          </Assign.To>
          <Assign.Value>
            <InArgument x:TypeArguments="x:String">[If(miscValues("authUser").ToString="NA","",miscValues("authUser").ToString)]</InArgument>
          </Assign.Value>
        </Assign>
        <Assign DisplayName="handleCashDiscount Assign" sap2010:WorkflowViewState.IdRef="Assign_87">
          <Assign.To>
            <OutArgument x:TypeArguments="x:Object">[miscValues("handleCashDiscount")]</OutArgument>
          </Assign.To>
          <Assign.Value>
            <InArgument x:TypeArguments="x:Boolean">[handleCashDiscount]</InArgument>
          </Assign.Value>
        </Assign>
        <Sequence DisplayName="PromptsDicitonary Sequence" sap2010:WorkflowViewState.IdRef="Sequence_50">
          <Assign sap2010:WorkflowViewState.IdRef="Assign_88">
            <Assign.To>
              <OutArgument x:TypeArguments="scg:List(x:String)">[ListPromptFiles]</OutArgument>
            </Assign.To>
            <Assign.Value>
              <InArgument x:TypeArguments="scg:List(x:String)">[miscValues("strPromptFilesMapping").ToString.Split("|"c).Select(Function(x) x.Split(";"c)(0)).ToList]</InArgument>
            </Assign.Value>
          </Assign>
          <Assign sap2010:WorkflowViewState.IdRef="Assign_89">
            <Assign.To>
              <OutArgument x:TypeArguments="scg:Dictionary(x:String, x:String)">[PromptsDicitonary]</OutArgument>
            </Assign.To>
            <Assign.Value>
              <InArgument x:TypeArguments="scg:Dictionary(x:String, x:String)" xml:space="preserve">[miscValues("strPromptFilesMapping").ToString.Split("|"c).
    Select(Function(part) part.Split(";"c)).
    ToDictionary(Function(parts) parts(0), Function(parts) parts(1))]</InArgument>
            </Assign.Value>
          </Assign>
        </Sequence>
        <Switch x:TypeArguments="x:Boolean" DisplayName="IfUseGenAIExtraction - Get Prompt Files" Expression="[CType(miscValues(&quot;useGenAIExtraction&quot;).ToString,Boolean)]" sap2010:WorkflowViewState.IdRef="Switch`1_6">
          <ForEach x:TypeArguments="x:String" x:Key="True" DisplayName="ForEach prompt file" sap2010:WorkflowViewState.IdRef="ForEach`1_4" Values="[ListPromptFiles]">
            <ActivityAction x:TypeArguments="x:String">
              <ActivityAction.Argument>
                <DelegateInArgument x:TypeArguments="x:String" Name="promptfile" />
              </ActivityAction.Argument>
              <Sequence DisplayName="get Prompt Files - Sequence" sap2010:WorkflowViewState.IdRef="Sequence_51">
                <Sequence.Variables>
                  <Variable x:TypeArguments="x:String" Name="GenAIPromptFileURL" />
                  <Variable x:TypeArguments="x:String" Name="strFIleName" />
                </Sequence.Variables>
                <Assign sap2010:WorkflowViewState.IdRef="Assign_90">
                  <Assign.To>
                    <OutArgument x:TypeArguments="x:String">[strFIleName]</OutArgument>
                  </Assign.To>
                  <Assign.Value>
                    <InArgument x:TypeArguments="x:String">[PromptsDicitonary(promptfile)]</InArgument>
                  </Assign.Value>
                </Assign>
                <Assign DisplayName="Assign Updated" sap2010:WorkflowViewState.IdRef="Assign_91">
                  <Assign.To>
                    <OutArgument x:TypeArguments="x:String">[GenAIPromptFileURL]</OutArgument>
                  </Assign.To>
                  <Assign.Value>
                    <InArgument x:TypeArguments="x:String">[tenantID+"IDM/api/items/search/item?%24query=%2FGenAIPrompt%5B%40RESOURCENAME%20%3D%20%22" + promptfile + ".txt%22%5D%20SORTBY(%40LASTCHANGEDTS%20DESCENDING)"]</InArgument>
                  </Assign.Value>
                </Assign>
                <ias:File_Delete ErrorCode="{x:Null}" ContinueOnError="True" DisplayName="Delete File" sap2010:WorkflowViewState.IdRef="File_Delete_15" Source="[configurationFolder+&quot;\&quot;+strFIleName+&quot;.txt&quot;]" />
                <iaw:InvokeWorkflow ResponseCode="{x:Null}" Arguments="[New Dictionary(Of String, Object) From {{&quot;configurationFolder&quot;,configurationFolder},{&quot;logFile&quot;,logFile},{&quot;GenAIPromptFileURL&quot;,GenAIPromptFileURL},{&quot;fileName&quot;,strFIleName}}]" ContinueOnError="False" DisplayName="Invoke getGenAIPrompt download" sap2010:WorkflowViewState.IdRef="InvokeWorkflow_25" OutputArguments="[GenAIOutput]" WorkflowFile="[projectPath+&quot;\getGenAIPrompt.xaml&quot;]" />
                <Assign sap2010:WorkflowViewState.IdRef="Assign_92">
                  <Assign.To>
                    <OutArgument x:TypeArguments="x:String">[GenAIStatus]</OutArgument>
                  </Assign.To>
                  <Assign.Value>
                    <InArgument x:TypeArguments="x:String">[CType(GenAIOutput("GenAIStatus"), String)]</InArgument>
                  </Assign.Value>
                </Assign>
                <Assign sap2010:WorkflowViewState.IdRef="Assign_93">
                  <Assign.To>
                    <OutArgument x:TypeArguments="x:String">[model]</OutArgument>
                  </Assign.To>
                  <Assign.Value>
                    <InArgument x:TypeArguments="x:String">[CType(GenAIOutput("GenAIModel"), String)]</InArgument>
                  </Assign.Value>
                </Assign>
                <Assign sap2010:WorkflowViewState.IdRef="Assign_94">
                  <Assign.To>
                    <OutArgument x:TypeArguments="x:String">[version]</OutArgument>
                  </Assign.To>
                  <Assign.Value>
                    <InArgument x:TypeArguments="x:String">[CType(GenAIOutput("GenAIModelVersion"), String)]</InArgument>
                  </Assign.Value>
                </Assign>
                <If Condition="[GenAIModel = &quot;&quot;]" sap2010:WorkflowViewState.IdRef="If_38">
                  <If.Then>
                    <Assign sap2010:WorkflowViewState.IdRef="Assign_95">
                      <Assign.To>
                        <OutArgument x:TypeArguments="x:String">[GenAIModel]</OutArgument>
                      </Assign.To>
                      <Assign.Value>
                        <InArgument x:TypeArguments="x:String">[CType(GenAIOutput("GenAIModel"), String)]</InArgument>
                      </Assign.Value>
                    </Assign>
                  </If.Then>
                </If>
                <If Condition="[GenAIModelVersion = &quot;&quot;]" sap2010:WorkflowViewState.IdRef="If_39">
                  <If.Then>
                    <Assign sap2010:WorkflowViewState.IdRef="Assign_96">
                      <Assign.To>
                        <OutArgument x:TypeArguments="x:String">[GenAIModelVersion]</OutArgument>
                      </Assign.To>
                      <Assign.Value>
                        <InArgument x:TypeArguments="x:String">[CType(GenAIOutput("GenAIModelVersion"), String)]</InArgument>
                      </Assign.Value>
                    </Assign>
                  </If.Then>
                </If>
              </Sequence>
            </ActivityAction>
          </ForEach>
          <ias:Append_Line ErrorCode="{x:Null}" x:Key="False" ContinueOnError="True" DisplayName="Log useGenAIExtraction False" sap2010:WorkflowViewState.IdRef="Append_Line_31" Line="[&quot;useGenAIExtraction value is &quot;+miscValues(&quot;useGenAIExtraction&quot;).ToString+Environment.NewLine+&quot;notificationFailureCount Prompt file is downloaded.&quot;]" Source="[logFile]" />
        </Switch>
        <ias:Append_Line ErrorCode="{x:Null}" ContinueOnError="True" DisplayName="Log useGenAIExtraction Status" sap2010:WorkflowViewState.IdRef="Append_Line_32" Line="[&quot;GenAIStatus (Prompt files download) value is &quot;+GenAIStatus.ToString]" Source="[logFile]" />
        <Sequence DisplayName="Add Values to miscValues" sap2010:WorkflowViewState.IdRef="Sequence_52">
          <Assign DisplayName="GenAIModel Assign" sap2010:WorkflowViewState.IdRef="Assign_97">
            <Assign.To>
              <OutArgument x:TypeArguments="x:Object">[miscValues("GenAIModel")]</OutArgument>
            </Assign.To>
            <Assign.Value>
              <InArgument x:TypeArguments="x:String">[GenAIModel]</InArgument>
            </Assign.Value>
          </Assign>
          <Assign DisplayName="GenAIModelVersion Assign" sap2010:WorkflowViewState.IdRef="Assign_98">
            <Assign.To>
              <OutArgument x:TypeArguments="x:Object">[miscValues("GenAIModelVersion")]</OutArgument>
            </Assign.To>
            <Assign.Value>
              <InArgument x:TypeArguments="x:Object">[GenAIModelVersion]</InArgument>
            </Assign.Value>
          </Assign>
          <Assign DisplayName="Assign configurationFolder" sap2010:WorkflowViewState.IdRef="Assign_99">
            <Assign.To>
              <OutArgument x:TypeArguments="x:Object">[miscValues("configurationFolder")]</OutArgument>
            </Assign.To>
            <Assign.Value>
              <InArgument x:TypeArguments="x:Object">[configurationFolder]</InArgument>
            </Assign.Value>
          </Assign>
          <Assign DisplayName="Assign emailAccount" sap2010:WorkflowViewState.IdRef="Assign_100">
            <Assign.To>
              <OutArgument x:TypeArguments="x:Object">[miscValues("emailAccount")]</OutArgument>
            </Assign.To>
            <Assign.Value>
              <InArgument x:TypeArguments="x:Object">[emailAccount]</InArgument>
            </Assign.Value>
          </Assign>
          <Assign DisplayName="Assign emailFolder" sap2010:WorkflowViewState.IdRef="Assign_101">
            <Assign.To>
              <OutArgument x:TypeArguments="x:Object">[miscValues("emailFolder")]</OutArgument>
            </Assign.To>
            <Assign.Value>
              <InArgument x:TypeArguments="x:Object">[emailFolder]</InArgument>
            </Assign.Value>
          </Assign>
          <Assign DisplayName="Assign numberOfEmails" sap2010:WorkflowViewState.IdRef="Assign_102">
            <Assign.To>
              <OutArgument x:TypeArguments="x:Object">[miscValues("numberOfEmails")]</OutArgument>
            </Assign.To>
            <Assign.Value>
              <InArgument x:TypeArguments="x:Object">[numberOfEmails]</InArgument>
            </Assign.Value>
          </Assign>
          <Assign DisplayName="Assign userIdentifier" sap2010:WorkflowViewState.IdRef="Assign_103">
            <Assign.To>
              <OutArgument x:TypeArguments="x:Object">[miscValues("userIdentifier")]</OutArgument>
            </Assign.To>
            <Assign.Value>
              <InArgument x:TypeArguments="x:Object">[userIdentifier]</InArgument>
            </Assign.Value>
          </Assign>
          <Assign DisplayName="Assign distributionType" sap2010:WorkflowViewState.IdRef="Assign_104">
            <Assign.To>
              <OutArgument x:TypeArguments="x:Object">[miscValues("distributionType")]</OutArgument>
            </Assign.To>
            <Assign.Value>
              <InArgument x:TypeArguments="x:Object">[distributionType]</InArgument>
            </Assign.Value>
          </Assign>
          <Assign DisplayName="Assign division" sap2010:WorkflowViewState.IdRef="Assign_105">
            <Assign.To>
              <OutArgument x:TypeArguments="x:Object">[miscValues("division")]</OutArgument>
            </Assign.To>
            <Assign.Value>
              <InArgument x:TypeArguments="x:Object">[division]</InArgument>
            </Assign.Value>
          </Assign>
          <Assign DisplayName="Assign tenantID" sap2010:WorkflowViewState.IdRef="Assign_106">
            <Assign.To>
              <OutArgument x:TypeArguments="x:Object">[miscValues("tenantID")]</OutArgument>
            </Assign.To>
            <Assign.Value>
              <InArgument x:TypeArguments="x:Object">[tenantID]</InArgument>
            </Assign.Value>
          </Assign>
          <Assign DisplayName="Assign projectPath" sap2010:WorkflowViewState.IdRef="Assign_107">
            <Assign.To>
              <OutArgument x:TypeArguments="x:Object">[miscValues("projectPath")]</OutArgument>
            </Assign.To>
            <Assign.Value>
              <InArgument x:TypeArguments="x:Object">[projectPath]</InArgument>
            </Assign.Value>
          </Assign>
          <Assign DisplayName="Assign datalakeAPILogicalId" sap2010:WorkflowViewState.IdRef="Assign_108">
            <Assign.To>
              <OutArgument x:TypeArguments="x:Object">[miscValues("datalakeAPILogicalId")]</OutArgument>
            </Assign.To>
            <Assign.Value>
              <InArgument x:TypeArguments="x:Object">[datalakeAPILogicalId]</InArgument>
            </Assign.Value>
          </Assign>
          <Assign DisplayName="Assign Match3Way" sap2010:WorkflowViewState.IdRef="Assign_109">
            <Assign.To>
              <OutArgument x:TypeArguments="x:Object">[miscValues("Match3Way")]</OutArgument>
            </Assign.To>
            <Assign.Value>
              <InArgument x:TypeArguments="x:Object">[Match3Way]</InArgument>
            </Assign.Value>
          </Assign>
          <Assign DisplayName="Assign Start Time" sap2010:WorkflowViewState.IdRef="Assign_110">
            <Assign.To>
              <OutArgument x:TypeArguments="x:Object">[miscValues("StartTime")]</OutArgument>
            </Assign.To>
            <Assign.Value>
              <InArgument x:TypeArguments="x:Object">[strStartTime]</InArgument>
            </Assign.Value>
          </Assign>
          <Assign DisplayName="Assign DlComments" sap2010:WorkflowViewState.IdRef="Assign_113">
            <Assign.To>
              <OutArgument x:TypeArguments="x:Object">[miscValues("Comments")]</OutArgument>
            </Assign.To>
            <Assign.Value>
              <InArgument x:TypeArguments="x:Object">[""]</InArgument>
            </Assign.Value>
          </Assign>
          <Assign DisplayName="Assign Additional Info" sap2010:WorkflowViewState.IdRef="Assign_114">
            <Assign.To>
              <OutArgument x:TypeArguments="x:Object">[miscValues("addInfo")]</OutArgument>
            </Assign.To>
            <Assign.Value>
              <InArgument x:TypeArguments="x:Object">[""]</InArgument>
            </Assign.Value>
          </Assign>
        </Sequence>
        <iad:CommentOut DisplayName="Comment Out" sap2010:WorkflowViewState.IdRef="CommentOut_3">
          <iad:CommentOut.Activities>
            <Assign DisplayName="Assign - Misc Values" sap2010:WorkflowViewState.IdRef="Assign_111">
              <Assign.To>
                <OutArgument x:TypeArguments="scg:Dictionary(x:String, x:Object)">[miscValues]</OutArgument>
              </Assign.To>
              <Assign.Value>
                <InArgument x:TypeArguments="scg:Dictionary(x:String, x:Object)" xml:space="preserve">[New Dictionary(Of String, Object) From {
    {"ERP", ERP},
    {"useGenAIExtraction", useGenAIExtraction},
    {"processDeliveryNote", processDeliveryNote},
	{"businessRuleForTolerance", businessRuleForTolerance},
    {"businessRuleForGUID", businessRuleForGUID},
	{"division", division},
    {"company", company},
    {"includeDistribution", includeDistribution},
	{"businessRuleForDivisionGLCode", businessRuleForDivisionGLCode},
    {"useBusinessRuleForTelerance", useBusinessRuleForTelerance},
	{"enableStagingForExpenseInException",enableStagingForExpenseInException},
	{"mandateGEOC",mandateGEOC},
	{"SplittingDoc",SplittingDoc},
	{"groupByTransDate",groupByTransDate},
	{"createInvoiceIrrespectiveTolerance",createInvoiceIrrespectiveTolerance},
	{"autoAllocateOpenLines",autoAllocateOpenLines},
	{"AutomateValidation",AutomateValidation},
	{"vatCodeConfig",vatCodeConfig},
	{"AutomateApproval",AutomateApproval},
	{"SendEmail",SendEmail},
	{"BusinessRuleAPResp",BusinessRuleAPResp},
	{"GenAIModel",GenAIModel},
	{"GenAIModelVersion",GenAIModelVersion}
}]</InArgument>
              </Assign.Value>
            </Assign>
          </iad:CommentOut.Activities>
        </iad:CommentOut>
        <If Condition="[Ctype(miscValues(&quot;processEmails&quot;).ToString,Boolean) and (miscValues(&quot;invoiceSource&quot;).ToString =&quot;OutlookClientEmail&quot; or  miscValues(&quot;invoiceSource&quot;).ToString =&quot;OutlookGraphEmail&quot;)]" DisplayName="IfProcessEmails" sap2010:WorkflowViewState.IdRef="If_40">
          <If.Then>
            <iaw:InvokeWorkflow OutputArguments="{x:Null}" ResponseCode="{x:Null}" Arguments="[New Dictionary(Of String, Object) From {{&quot;configurationFolder&quot;,configurationFolder},{&quot;emailAccount&quot;,emailAccount},{&quot;emailFolder&quot;,emailFolder},{&quot;numberOfEmails&quot;,numberOfEmails},{&quot;logFile&quot;,logFile},{&quot;tenantID&quot;,tenantID},&#xA;{&quot;colemanAPI&quot;,miscValues(&quot;colemanAPI&quot;).ToString},{&quot;userIdentifier&quot;,userIdentifier},{&quot;distributionType&quot;,distributionType},{&quot;projectPath&quot;,projectPath},{&quot;enableMessageBoxes&quot;,enableMessageBoxes},&#xA;{&quot;poFilterValues&quot;,miscValues(&quot;poFilterValues&quot;).ToString},{&quot;poFilterCondition&quot;,miscValues(&quot;poFilterCondition&quot;).ToString},{&quot;datalakeAPILogicalId&quot;,miscValues(&quot;datalakeAPILogicalId&quot;).ToString},&#xA;{&quot;chargeCode&quot;,miscValues(&quot;chargeCode&quot;).ToString},{&quot;discountCode&quot;,miscValues(&quot;discountCode&quot;).ToString},{&quot;authUser&quot;,miscValues(&quot;authUser&quot;).ToString},{&quot;imsAPIUrl&quot;,miscValues(&quot;imsAPIUrl&quot;).ToString},&#xA;{&quot;extractNumericFromPO&quot;,CType(miscValues(&quot;extractNumericFromPO&quot;).ToString,Boolean)},{&quot;vatCodeConfig&quot;,miscValues(&quot;vatCodeConfig&quot;).ToString},&#xA;{&quot;poDiscountsHandlingConfig&quot;,CType(miscValues(&quot;poDiscountsHandlingConfig&quot;).ToString,Boolean)},{&quot;matchVendorItemCode&quot;,CType(miscValues(&quot;matchVendorItemCode&quot;).ToString,Boolean)},&#xA;{&quot;invoiceSource&quot;,miscValues(&quot;invoiceSource&quot;).ToString},{&quot;extractFromWidgetDatalake&quot;,CType(miscValues(&quot;extractFromWidgetDatalake&quot;).ToString,Boolean)},&#xA;{&quot;approvalRequired&quot;,CType(miscValues(&quot;approvalRequired&quot;).ToString,Boolean)},{&quot;approvalWorkflow&quot;,miscValues(&quot;approvalWorkflow&quot;).ToString},&#xA;{&quot;checkAmountBussinessRule&quot;,miscValues(&quot;checkAmountBussinessRule&quot;).ToString},{&quot;processExpenseInvoice&quot;,CType(miscValues(&quot;processExpenseInvoice&quot;).ToString,Boolean)},&#xA;{&quot;miscValues&quot;,miscValues},{&quot;MasterDownloads&quot;,configurationFolder+&quot;\OutlookDownloads\MasterDownloads&quot;}}]" ContinueOnError="False" DisplayName="GetOutlookEmails Invoke Workflow" sap2010:WorkflowViewState.IdRef="InvokeWorkflow_26" WorkflowFile="[projectPath+&quot;\Outlook_ProcessNew.xaml&quot;]" />
          </If.Then>
          <If.Else>
            <ias:Append_Line ErrorCode="{x:Null}" ContinueOnError="True" DisplayName="Log ProcessEmails False" sap2010:WorkflowViewState.IdRef="Append_Line_33" Line="[&quot;ProcessEmails value is &quot;+miscValues(&quot;processEmails&quot;).ToString +&quot;and invoiceSource value is &quot;+miscValues(&quot;invoiceSource&quot;).ToString]" Source="[logFile]" />
          </If.Else>
        </If>
        <If Condition="[Ctype(miscValues(&quot;processFolders&quot;).ToString,Boolean) and (miscValues(&quot;invoiceFolderPath&quot;).ToString &lt;&gt; &quot;&quot; and miscValues(&quot;invoiceFolderPath&quot;).ToString &lt;&gt; &quot;NA&quot;)]" DisplayName="IfProcessFolders" sap2010:WorkflowViewState.IdRef="If_41">
          <If.Then>
            <iaw:InvokeWorkflow OutputArguments="{x:Null}" ResponseCode="{x:Null}" Arguments="[New Dictionary(Of String, Object) From {{&quot;configurationFolder&quot;,configurationFolder},{&quot;logFile&quot;,logFile},{&quot;tenantID&quot;,tenantID},{&quot;colemanAPI&quot;,miscValues(&quot;colemanAPI&quot;).ToString},{&quot;userIdentifier&quot;,userIdentifier},{&quot;distributionType&quot;,distributionType},{&quot;projectPath&quot;,projectPath},{&quot;enableMessageBoxes&quot;,enableMessageBoxes},{&quot;poFilterValues&quot;,miscValues(&quot;poFilterValues&quot;).ToString},{&quot;poFilterCondition&quot;,miscValues(&quot;poFilterCondition&quot;).ToString},{&quot;datalakeAPILogicalId&quot;,miscValues(&quot;datalakeAPILogicalId&quot;).ToString},{&quot;chargeCode&quot;,miscValues(&quot;chargeCode&quot;).ToString},{&quot;discountCode&quot;,miscValues(&quot;discountCode&quot;).ToString},{&quot;authUser&quot;,miscValues(&quot;authUser&quot;).ToString},{&quot;imsAPIUrl&quot;,miscValues(&quot;imsAPIUrl&quot;).ToString},{&quot;invoiceFolderPath&quot;,miscValues(&quot;invoiceFolderPath&quot;).ToString},{&quot;vatCodeConfig&quot;,miscValues(&quot;vatCodeConfig&quot;).ToString},{&quot;poDiscountsHandlingConfig&quot;,miscValues(&quot;poDiscountsHandlingConfig&quot;).ToString},{&quot;matchVendorItemCode&quot;,CType(miscValues(&quot;matchVendorItemCode&quot;).ToString,Boolean)},{&quot;extractNumericFromPO&quot;,CType(miscValues(&quot;extractNumericFromPO&quot;).ToString,Boolean)},{&quot;extractFromWidgetDatalake&quot;,CType(miscValues(&quot;extractFromWidgetDatalake&quot;).ToString,Boolean)},{&quot;approvalRequired&quot;,CType(miscValues(&quot;approvalRequired&quot;).ToString,Boolean)},{&quot;approvalWorkflow&quot;,miscValues(&quot;approvalWorkflow&quot;).ToString},{&quot;checkAmountBussinessRule&quot;,miscValues(&quot;checkAmountBussinessRule&quot;).ToString},{&quot;miscValues&quot;,miscValues},{&quot;processExpenseInvoice&quot;,CType(miscValues(&quot;processExpenseInvoice&quot;).ToString,Boolean)}}]" ContinueOnError="False" DisplayName="Process Folders" sap2010:WorkflowViewState.IdRef="InvokeWorkflow_27" WorkflowFile="[projectPath+&quot;\ReadFilesFromFolder.xaml&quot;]" />
          </If.Then>
          <If.Else>
            <ias:Append_Line ErrorCode="{x:Null}" ContinueOnError="True" DisplayName="Log processFoldersFalse" sap2010:WorkflowViewState.IdRef="Append_Line_34" Line="[&quot;processFolders value is &quot;+miscValues(&quot;processFolders&quot;).ToString +&quot; and invoiceFolderPath value is &quot;+miscValues(&quot;invoiceFolderPath&quot;).ToString]" Source="[logFile]" />
          </If.Else>
        </If>
        <If Condition="[Ctype(miscValues(&quot;extractFromWidgetDatalake&quot;).ToString,Boolean) AND Ctype(miscValues(&quot;reprocess&quot;).ToString,Boolean)]" DisplayName="extractFromWidgetDatalake If" sap2010:WorkflowViewState.IdRef="If_43">
          <If.Then>
            <Sequence DisplayName="extractFromWidgetDatalake Sequence" sap2010:WorkflowViewState.IdRef="Sequence_53">
              <iaw:InvokeWorkflow OutputArguments="{x:Null}" Arguments="[New Dictionary(Of String, Object) From {{&quot;configurationFolder&quot;,configurationFolder},{&quot;tenantID&quot;,tenantID},{&quot;colemanAPI&quot;,miscValues(&quot;colemanAPI&quot;).ToString},{&quot;logFile&quot;,logFile},{&quot;distributionType&quot;,distributionType},{&quot;userIdentifier&quot;,userIdentifier},{&quot;projectPath&quot;,projectPath},{&quot;miscValues&quot;,miscValues},{&quot;maxNotReceivedCount&quot;,CType(miscValues(&quot;maxNotReceivedCount&quot;).ToString,Integer)},{&quot;datalakeAPILogicalId&quot;,miscValues(&quot;datalakeAPILogicalId&quot;).ToString},{&quot;chargeCode&quot;,miscValues(&quot;chargeCode&quot;).ToString},{&quot;discountCode&quot;,miscValues(&quot;discountCode&quot;).ToString},{&quot;authUser&quot;,miscValues(&quot;authUser&quot;).ToString},{&quot;imsAPIUrl&quot;,miscValues(&quot;imsAPIUrl&quot;).ToString},{&quot;extractFromWidgetDatalake&quot;,CType(miscValues(&quot;extractFromWidgetDatalake&quot;).ToString,Boolean)},{&quot;approvalRequired&quot;,CType(miscValues(&quot;approvalRequired&quot;).ToString,Boolean)},{&quot;approvalWorkflow&quot;,miscValues(&quot;approvalWorkflow&quot;).ToString},{&quot;checkAmountBussinessRule&quot;,miscValues(&quot;checkAmountBussinessRule&quot;).ToString},{&quot;matchVendorItemCode&quot;,CType(miscValues(&quot;matchVendorItemCode&quot;).ToString,Boolean)},{&quot;enableMessageBoxes&quot;,enableMessageBoxes}}]" ContinueOnError="True" DisplayName="Read Datalake" sap2010:WorkflowViewState.IdRef="InvokeWorkflow_28" ResponseCode="[datalakeResponseCode]" WorkflowFile="[projectPath+&quot;\ExtractFromDatalake - Copy.xaml&quot;]" />
              <If Condition="[datalakeResponseCode&lt;&gt; 200]" sap2010:WorkflowViewState.IdRef="If_42">
                <If.Then>
                  <ias:Append_Line ErrorCode="{x:Null}" ContinueOnError="True" DisplayName="Datalake error Append Line" sap2010:WorkflowViewState.IdRef="Append_Line_35" Line="Error occured while retriving the data from the Datalake." Source="[logFile]" />
                </If.Then>
              </If>
            </Sequence>
          </If.Then>
          <If.Else>
            <ias:Append_Line ErrorCode="{x:Null}" ContinueOnError="True" DisplayName="Log extractFromWidgetDatalake and reprocess in Else block" sap2010:WorkflowViewState.IdRef="Append_Line_36" Line="[&quot;extractFromWidgetDatalake value is &quot;+miscValues(&quot;extractFromWidgetDatalake&quot;).ToString +&quot;and reprocess value is &quot;+miscValues(&quot;reprocess&quot;).ToString]" Source="[logFile]" />
          </If.Else>
        </If>
      </Sequence>
    </TryCatch.Try>
    <TryCatch.Catches>
      <Catch x:TypeArguments="s:Exception" sap2010:WorkflowViewState.IdRef="Catch`1_3">
        <ActivityAction x:TypeArguments="s:Exception">
          <ActivityAction.Argument>
            <DelegateInArgument x:TypeArguments="s:Exception" Name="exception" />
          </ActivityAction.Argument>
          <ias:Append_Line ErrorCode="{x:Null}" ContinueOnError="False" DisplayName="Main Catch Block Append Line" sap2010:WorkflowViewState.IdRef="Append_Line_23" Line="[&quot;&quot;+Environment.NewLine+&quot;M3 Vendor Invoice Procesing Ended with below exception - &quot;+Environment.NewLine+Exception.Message]" Source="[logFile]" />
        </ActivityAction>
      </Catch>
    </TryCatch.Catches>
    <TryCatch.Finally>
      <ias:Append_Line ErrorCode="{x:Null}" ContinueOnError="True" DisplayName="End Time Append Line" sap2010:WorkflowViewState.IdRef="Append_Line_25" Line="[&quot;End Time : &quot; +System.DateTime.Now.ToString(&quot;yyyy/MM/dd HH:mm:ss&quot;)]" Source="[logFile]" />
    </TryCatch.Finally>
    <sads:DebugSymbol.Symbol>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</sads:DebugSymbol.Symbol>
  </TryCatch>
  <sap2010:WorkflowViewState.ViewStateManager>
    <sap2010:ViewStateManager>
      <sap2010:ViewStateData Id="Assign_112" sap:VirtualizedContainerService.HintSize="576,62" />
      <sap2010:ViewStateData Id="File_Delete_14" sap:VirtualizedContainerService.HintSize="554,22" />
      <sap2010:ViewStateData Id="IONAPIRequestWizard_4" sap:VirtualizedContainerService.HintSize="554,22" />
      <sap2010:ViewStateData Id="Assign_74" sap:VirtualizedContainerService.HintSize="242,62" />
      <sap2010:ViewStateData Id="JQTransform_4" sap:VirtualizedContainerService.HintSize="242,22" />
      <sap2010:ViewStateData Id="Assign_75" sap:VirtualizedContainerService.HintSize="242,62" />
      <sap2010:ViewStateData Id="Assign_76" sap:VirtualizedContainerService.HintSize="242,62" />
      <sap2010:ViewStateData Id="DownloadFile_URL_2" sap:VirtualizedContainerService.HintSize="242,22" />
      <sap2010:ViewStateData Id="Sequence_44" sap:VirtualizedContainerService.HintSize="264,514">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">True</x:Boolean>
            <x:Boolean x:Key="IsPinned">False</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="Assign_77" sap:VirtualizedContainerService.HintSize="242,62" />
      <sap2010:ViewStateData Id="Append_Line_26" sap:VirtualizedContainerService.HintSize="242,22" />
      <sap2010:ViewStateData Id="Sequence_45" sap:VirtualizedContainerService.HintSize="264,248">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">True</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="If_37" sap:VirtualizedContainerService.HintSize="554,668">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">True</x:Boolean>
            <x:Boolean x:Key="IsPinned">False</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="ReadRange_2" sap:VirtualizedContainerService.HintSize="553,22" />
      <sap2010:ViewStateData Id="Assign_78" sap:VirtualizedContainerService.HintSize="553,60" />
      <sap2010:ViewStateData Id="Assign_79" sap:VirtualizedContainerService.HintSize="242,60" />
      <sap2010:ViewStateData Id="Assign_80" sap:VirtualizedContainerService.HintSize="242,60" />
      <sap2010:ViewStateData Id="Sequence_46" sap:VirtualizedContainerService.HintSize="264,284">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">True</x:Boolean>
            <x:Boolean x:Key="IsPinned">False</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="Assign_81" sap:VirtualizedContainerService.HintSize="242,60" />
      <sap2010:ViewStateData Id="Assign_82" sap:VirtualizedContainerService.HintSize="242,60" />
      <sap2010:ViewStateData Id="Sequence_47" sap:VirtualizedContainerService.HintSize="264,283">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">True</x:Boolean>
            <x:Boolean x:Key="IsPinned">False</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="Assign_83" sap:VirtualizedContainerService.HintSize="242,60" />
      <sap2010:ViewStateData Id="Assign_84" sap:VirtualizedContainerService.HintSize="242,60" />
      <sap2010:ViewStateData Id="Sequence_48" sap:VirtualizedContainerService.HintSize="264,283">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">True</x:Boolean>
            <x:Boolean x:Key="IsPinned">False</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="Switch`1_5" sap:VirtualizedContainerService.HintSize="476,512">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">True</x:Boolean>
            <x:Boolean x:Key="IsPinned">False</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="ForEach`1_3" sap:VirtualizedContainerService.HintSize="553,51">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">False</x:Boolean>
            <x:Boolean x:Key="IsPinned">False</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="Sequence_49" sap:VirtualizedContainerService.HintSize="576,1168.66666666667">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">True</x:Boolean>
            <x:Boolean x:Key="IsPinned">False</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="InvokeWorkflow_24" sap:VirtualizedContainerService.HintSize="576,22" />
      <sap2010:ViewStateData Id="Assign_85" sap:VirtualizedContainerService.HintSize="576,62" />
      <sap2010:ViewStateData Id="Append_Line_27" sap:VirtualizedContainerService.HintSize="576,22" />
      <sap2010:ViewStateData Id="Append_Line_28" sap:VirtualizedContainerService.HintSize="576,22" />
      <sap2010:ViewStateData Id="Append_Line_29" sap:VirtualizedContainerService.HintSize="576,22" />
      <sap2010:ViewStateData Id="IONAPIRequestWizard_5" sap:VirtualizedContainerService.HintSize="200,22" />
      <sap2010:ViewStateData Id="Append_Line_30" sap:VirtualizedContainerService.HintSize="200,22" />
      <sap2010:ViewStateData Id="Catch`1_4" sap:VirtualizedContainerService.HintSize="404.666666666667,22">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">False</x:Boolean>
            <x:Boolean x:Key="IsPinned">False</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="TryCatch_4" sap:VirtualizedContainerService.HintSize="576,298">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">True</x:Boolean>
            <x:Boolean x:Key="IsPinned">False</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="Assign_86" sap:VirtualizedContainerService.HintSize="576,62" />
      <sap2010:ViewStateData Id="Assign_87" sap:VirtualizedContainerService.HintSize="576,60" />
      <sap2010:ViewStateData Id="Assign_88" sap:VirtualizedContainerService.HintSize="242,60" />
      <sap2010:ViewStateData Id="Assign_89" sap:VirtualizedContainerService.HintSize="242,64" />
      <sap2010:ViewStateData Id="Sequence_50" sap:VirtualizedContainerService.HintSize="576,288">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">True</x:Boolean>
            <x:Boolean x:Key="IsPinned">False</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="Assign_90" sap:VirtualizedContainerService.HintSize="464,60" />
      <sap2010:ViewStateData Id="Assign_91" sap:VirtualizedContainerService.HintSize="464,60" />
      <sap2010:ViewStateData Id="File_Delete_15" sap:VirtualizedContainerService.HintSize="464,22" />
      <sap2010:ViewStateData Id="InvokeWorkflow_25" sap:VirtualizedContainerService.HintSize="464,22" />
      <sap2010:ViewStateData Id="Assign_92" sap:VirtualizedContainerService.HintSize="464,60" />
      <sap2010:ViewStateData Id="Assign_93" sap:VirtualizedContainerService.HintSize="464,60" />
      <sap2010:ViewStateData Id="Assign_94" sap:VirtualizedContainerService.HintSize="464,60" />
      <sap2010:ViewStateData Id="Assign_95" sap:VirtualizedContainerService.HintSize="242,62" />
      <sap2010:ViewStateData Id="If_38" sap:VirtualizedContainerService.HintSize="464,51">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">False</x:Boolean>
            <x:Boolean x:Key="IsPinned">False</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="Assign_96" sap:VirtualizedContainerService.HintSize="242,60" />
      <sap2010:ViewStateData Id="If_39" sap:VirtualizedContainerService.HintSize="464,51">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">False</x:Boolean>
            <x:Boolean x:Key="IsPinned">False</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="Sequence_51" sap:VirtualizedContainerService.HintSize="486,1044">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">True</x:Boolean>
            <x:Boolean x:Key="IsPinned">False</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="ForEach`1_4" sap:VirtualizedContainerService.HintSize="294,1043">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">True</x:Boolean>
            <x:Boolean x:Key="IsPinned">False</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="Append_Line_31" sap:VirtualizedContainerService.HintSize="200,22" />
      <sap2010:ViewStateData Id="Switch`1_6" sap:VirtualizedContainerService.HintSize="576,160">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">True</x:Boolean>
            <x:Boolean x:Key="IsPinned">False</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="Append_Line_32" sap:VirtualizedContainerService.HintSize="576,22" />
      <sap2010:ViewStateData Id="Assign_97" sap:VirtualizedContainerService.HintSize="242,60" />
      <sap2010:ViewStateData Id="Assign_98" sap:VirtualizedContainerService.HintSize="242,60" />
      <sap2010:ViewStateData Id="Assign_99" sap:VirtualizedContainerService.HintSize="242,60" />
      <sap2010:ViewStateData Id="Assign_100" sap:VirtualizedContainerService.HintSize="242,60" />
      <sap2010:ViewStateData Id="Assign_101" sap:VirtualizedContainerService.HintSize="242,60" />
      <sap2010:ViewStateData Id="Assign_102" sap:VirtualizedContainerService.HintSize="242,60" />
      <sap2010:ViewStateData Id="Assign_103" sap:VirtualizedContainerService.HintSize="242,60" />
      <sap2010:ViewStateData Id="Assign_104" sap:VirtualizedContainerService.HintSize="242,60" />
      <sap2010:ViewStateData Id="Assign_105" sap:VirtualizedContainerService.HintSize="242,60" />
      <sap2010:ViewStateData Id="Assign_106" sap:VirtualizedContainerService.HintSize="242,60" />
      <sap2010:ViewStateData Id="Assign_107" sap:VirtualizedContainerService.HintSize="242,60" />
      <sap2010:ViewStateData Id="Assign_108" sap:VirtualizedContainerService.HintSize="242,60" />
      <sap2010:ViewStateData Id="Assign_109" sap:VirtualizedContainerService.HintSize="242,60" />
      <sap2010:ViewStateData Id="Assign_110" sap:VirtualizedContainerService.HintSize="242,60" />
      <sap2010:ViewStateData Id="Assign_113" sap:VirtualizedContainerService.HintSize="242,60" />
      <sap2010:ViewStateData Id="Assign_114" sap:VirtualizedContainerService.HintSize="242,60" />
      <sap2010:ViewStateData Id="Sequence_52" sap:VirtualizedContainerService.HintSize="576,1684">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">True</x:Boolean>
            <x:Boolean x:Key="IsPinned">False</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="Assign_111" sap:VirtualizedContainerService.HintSize="242,64" />
      <sap2010:ViewStateData Id="CommentOut_3" sap:VirtualizedContainerService.HintSize="576,58">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">False</x:Boolean>
            <x:Boolean x:Key="IsPinned">False</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="InvokeWorkflow_26" sap:VirtualizedContainerService.HintSize="200,22" />
      <sap2010:ViewStateData Id="Append_Line_33" sap:VirtualizedContainerService.HintSize="200,22" />
      <sap2010:ViewStateData Id="If_40" sap:VirtualizedContainerService.HintSize="576,208">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">True</x:Boolean>
            <x:Boolean x:Key="IsPinned">False</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="InvokeWorkflow_27" sap:VirtualizedContainerService.HintSize="200,22" />
      <sap2010:ViewStateData Id="Append_Line_34" sap:VirtualizedContainerService.HintSize="200,22" />
      <sap2010:ViewStateData Id="If_41" sap:VirtualizedContainerService.HintSize="576,208">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">True</x:Boolean>
            <x:Boolean x:Key="IsPinned">False</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="InvokeWorkflow_28" sap:VirtualizedContainerService.HintSize="200,22" />
      <sap2010:ViewStateData Id="Append_Line_35" sap:VirtualizedContainerService.HintSize="200,22" />
      <sap2010:ViewStateData Id="If_42" sap:VirtualizedContainerService.HintSize="200,52.6666666666667">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">False</x:Boolean>
            <x:Boolean x:Key="IsPinned">False</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="Sequence_53" sap:VirtualizedContainerService.HintSize="222,238.666666666667">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">True</x:Boolean>
            <x:Boolean x:Key="IsPinned">False</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="Append_Line_36" sap:VirtualizedContainerService.HintSize="200,22" />
      <sap2010:ViewStateData Id="If_43" sap:VirtualizedContainerService.HintSize="576,392.666666666667" />
      <sap2010:ViewStateData Id="Sequence_54" sap:VirtualizedContainerService.HintSize="598,5625.33333333333">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">True</x:Boolean>
            <x:Boolean x:Key="IsPinned">False</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="Append_Line_23" sap:VirtualizedContainerService.HintSize="200,22" />
      <sap2010:ViewStateData Id="Catch`1_3" sap:VirtualizedContainerService.HintSize="503.333333333333,22">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">False</x:Boolean>
            <x:Boolean x:Key="IsPinned">False</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="Append_Line_25" sap:VirtualizedContainerService.HintSize="200,22" />
      <sap2010:ViewStateData Id="TryCatch_3" sap:VirtualizedContainerService.HintSize="616.666666666667,5943.33333333333" />
      <sap2010:ViewStateData Id="RehostedWorkflowDesigner.Workflow_1" sap:VirtualizedContainerService.HintSize="656.666666666667,6103.33333333333" />
    </sap2010:ViewStateManager>
  </sap2010:WorkflowViewState.ViewStateManager>
</Activity>