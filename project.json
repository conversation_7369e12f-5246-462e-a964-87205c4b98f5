{"name": "M3InvoiceProcessingGenAIV3", "description": "M3InvoiceProcessingGenAIV3Merged", "main": "MainSequence.xaml", "dependencies": {"mscorlib": "[*******]", "PresentationFramework": "[*******]", "System.Xaml": "[*******]", "System": "[*******]", "System.Activities": "[*******]", "System.Activities.Presentation": "[*******]", "YDock": "[*******]", "System.Windows.Forms": "[*******]", "WindowsBase": "[*******]", "NLog": "[*******]", "PresentationCore": "[*******]", "Newtonsoft.Json": "[********]", "System.Drawing": "[*******]", "System.Workflow.Activities": "[*******]", "System.Core": "[*******]", "UiRecorder": "[*******]", "System.IO.Compression": "[*******]", "Microsoft.WindowsAPICodePack.Shell": "[*******]", "Microsoft.Web.WebView2.Core": "[1.0.818.41]", "Microsoft.Web.WebView2.WinForms": "[1.0.818.41]", "System.Net.Http": "[*******]", "System.Configuration": "[*******]", "System.Activities.Core.Presentation": "[*******]", "Microsoft.CSharp": "[*******]", "System.Xml": "[*******]", "System.Workflow.ComponentModel": "[*******]", "System.Xml.Linq": "[*******]", "System.IO.Compression.FileSystem": "[*******]", "RestSharp": "[106.12.0.0]"}, "packages": {"Infor.RPA.Utilities": "[*******]", "Infor.RPA.Utility.Editor": "[*******]", "Infor.Activities.Web": "[*******]", "Infor.Activities.Desktop": "[*******]", "Infor.Activities.Debug": "[*******]", "Infor.Activities.Email": "[*******]", "Infor.Activities.Excel": "[*******]", "Infor.Activities.ExcelOnline": "[*******]", "Infor.Activities.HTTPRequests": "[*******]", "Infor.Activities.IONAPI": "[*******]", "Infor.Activities.OCR": "[*******]", "Infor.RPA.OCR": "[*******]", "Infor.Activities.OneDrive": "[*******]", "Infor.Activities.Sys": "[*******]", "Infor.Activities.Datatable": "[*******]", "Infor.Activities.SharePointList": "[*******]", "Infor.Activities.Workflow": "[*******]", "Infor.RPA.Security": "[*******]", "Infor.RPA.Commons": "[*******]"}, "schemaVersion": "1.0", "studioVersion": "2025.06.1\r\n.689", "sourceFiles": [{"fileName": "._project.json", "filePath": "C:\\Users\\<USER>\\AppData\\Local\\InforRPA\\M3InvoiceProcessingGenAIV3"}, {"fileName": "AddCharge.xaml", "filePath": "C:\\Users\\<USER>\\AppData\\Local\\InforRPA\\M3InvoiceProcessingGenAIV3"}, {"fileName": "AddHead.xaml", "filePath": "C:\\Users\\<USER>\\AppData\\Local\\InforRPA\\M3InvoiceProcessingGenAIV3"}, {"fileName": "AddLine.xaml", "filePath": "C:\\Users\\<USER>\\AppData\\Local\\InforRPA\\M3InvoiceProcessingGenAIV3"}, {"fileName": "approval.xaml", "filePath": "C:\\Users\\<USER>\\AppData\\Local\\InforRPA\\M3InvoiceProcessingGenAIV3"}, {"fileName": "ApprovalGUID.xaml", "filePath": "C:\\Users\\<USER>\\AppData\\Local\\InforRPA\\M3InvoiceProcessingGenAIV3"}, {"fileName": "APResp.xaml", "filePath": "C:\\Users\\<USER>\\AppData\\Local\\InforRPA\\M3InvoiceProcessingGenAIV3"}, {"fileName": "BulkFileHandling.xaml", "filePath": "C:\\Users\\<USER>\\AppData\\Local\\InforRPA\\M3InvoiceProcessingGenAIV3"}, {"fileName": "CallColeman.xaml", "filePath": "C:\\Users\\<USER>\\AppData\\Local\\InforRPA\\M3InvoiceProcessingGenAIV3"}, {"fileName": "CheckApproval.xaml", "filePath": "C:\\Users\\<USER>\\AppData\\Local\\InforRPA\\M3InvoiceProcessingGenAIV3"}, {"fileName": "Classification_Split.xaml", "filePath": "C:\\Users\\<USER>\\AppData\\Local\\InforRPA\\M3InvoiceProcessingGenAIV3"}, {"fileName": "CreateDirectories.xaml", "filePath": "C:\\Users\\<USER>\\AppData\\Local\\InforRPA\\M3InvoiceProcessingGenAIV3"}, {"fileName": "DebitNoteCreation _New.xaml", "filePath": "C:\\Users\\<USER>\\AppData\\Local\\InforRPA\\M3InvoiceProcessingGenAIV3"}, {"fileName": "DivisionInfo.xaml", "filePath": "C:\\Users\\<USER>\\AppData\\Local\\InforRPA\\M3InvoiceProcessingGenAIV3"}, {"fileName": "DivisiontoGLCode.xaml", "filePath": "C:\\Users\\<USER>\\AppData\\Local\\InforRPA\\M3InvoiceProcessingGenAIV3"}, {"fileName": "DivisionVendorTolerance-old.xaml", "filePath": "C:\\Users\\<USER>\\AppData\\Local\\InforRPA\\M3InvoiceProcessingGenAIV3"}, {"fileName": "DivisionVendorTolerance.xaml", "filePath": "C:\\Users\\<USER>\\AppData\\Local\\InforRPA\\M3InvoiceProcessingGenAIV3"}, {"fileName": "ExportMI.xaml", "filePath": "C:\\Users\\<USER>\\AppData\\Local\\InforRPA\\M3InvoiceProcessingGenAIV3"}, {"fileName": "export_mi.py", "filePath": "C:\\Users\\<USER>\\AppData\\Local\\InforRPA\\M3InvoiceProcessingGenAIV3"}, {"fileName": "ExtractFromDatalake - Copy.xaml", "filePath": "C:\\Users\\<USER>\\AppData\\Local\\InforRPA\\M3InvoiceProcessingGenAIV3"}, {"fileName": "GenAI.xaml", "filePath": "C:\\Users\\<USER>\\AppData\\Local\\InforRPA\\M3InvoiceProcessingGenAIV3"}, {"fileName": "GenAI_UnExpInv.xaml", "filePath": "C:\\Users\\<USER>\\AppData\\Local\\InforRPA\\M3InvoiceProcessingGenAIV3"}, {"fileName": "getGenAIPrompt.xaml", "filePath": "C:\\Users\\<USER>\\AppData\\Local\\InforRPA\\M3InvoiceProcessingGenAIV3"}, {"fileName": "GetOCRValuesNew.xaml", "filePath": "C:\\Users\\<USER>\\AppData\\Local\\InforRPA\\M3InvoiceProcessingGenAIV3"}, {"fileName": "getvendordetails.xaml", "filePath": "C:\\Users\\<USER>\\AppData\\Local\\InforRPA\\M3InvoiceProcessingGenAIV3"}, {"fileName": "InvoiceThreshold.xaml", "filePath": "C:\\Users\\<USER>\\AppData\\Local\\InforRPA\\M3InvoiceProcessingGenAIV3"}, {"fileName": "LinesExtractionWithPO.xaml", "filePath": "C:\\Users\\<USER>\\AppData\\Local\\InforRPA\\M3InvoiceProcessingGenAIV3"}, {"fileName": "M3InvoiceProcessingGenAIV3.zip", "filePath": "C:\\Users\\<USER>\\AppData\\Local\\InforRPA\\M3InvoiceProcessingGenAIV3"}, {"fileName": "M3InvoiceProcessingGenAIV31.0.12.zip", "filePath": "C:\\Users\\<USER>\\AppData\\Local\\InforRPA\\M3InvoiceProcessingGenAIV3"}, {"fileName": "M3InvoiceProcessingGenAIV31.0.6.zip", "filePath": "C:\\Users\\<USER>\\AppData\\Local\\InforRPA\\M3InvoiceProcessingGenAIV3"}, {"fileName": "M3InvoiceProcessingGenAIV31.0.8.zip", "filePath": "C:\\Users\\<USER>\\AppData\\Local\\InforRPA\\M3InvoiceProcessingGenAIV3"}, {"fileName": "MainSequence.xaml", "filePath": "C:\\Users\\<USER>\\AppData\\Local\\InforRPA\\M3InvoiceProcessingGenAIV3"}, {"fileName": "MNS100Call.xaml", "filePath": "C:\\Users\\<USER>\\AppData\\Local\\InforRPA\\M3InvoiceProcessingGenAIV3"}, {"fileName": "MoveFileToSuccessFailureFolder.xaml", "filePath": "C:\\Users\\<USER>\\AppData\\Local\\InforRPA\\M3InvoiceProcessingGenAIV3"}, {"fileName": "OneDeliveryNotFound.xaml", "filePath": "C:\\Users\\<USER>\\AppData\\Local\\InforRPA\\M3InvoiceProcessingGenAIV3"}, {"fileName": "Outlook_ProcessNew.xaml", "filePath": "C:\\Users\\<USER>\\AppData\\Local\\InforRPA\\M3InvoiceProcessingGenAIV3"}, {"fileName": "PreRegister.xaml", "filePath": "C:\\Users\\<USER>\\AppData\\Local\\InforRPA\\M3InvoiceProcessingGenAIV3"}, {"fileName": "ProcessDeliveryNote.xaml", "filePath": "C:\\Users\\<USER>\\AppData\\Local\\InforRPA\\M3InvoiceProcessingGenAIV3"}, {"fileName": "ProcessDocument.xaml", "filePath": "C:\\Users\\<USER>\\AppData\\Local\\InforRPA\\M3InvoiceProcessingGenAIV3"}, {"fileName": "ProcessExpenseInvoice - Copy.xaml", "filePath": "C:\\Users\\<USER>\\AppData\\Local\\InforRPA\\M3InvoiceProcessingGenAIV3"}, {"fileName": "ProcessExpenseInvoice.xaml", "filePath": "C:\\Users\\<USER>\\AppData\\Local\\InforRPA\\M3InvoiceProcessingGenAIV3"}, {"fileName": "ProcessExpenseWithPO.xaml", "filePath": "C:\\Users\\<USER>\\AppData\\Local\\InforRPA\\M3InvoiceProcessingGenAIV3"}, {"fileName": "ProcessPOInvoice.xaml", "filePath": "C:\\Users\\<USER>\\AppData\\Local\\InforRPA\\M3InvoiceProcessingGenAIV3"}, {"fileName": "ProcessPOInvoiceAPI.xaml", "filePath": "C:\\Users\\<USER>\\AppData\\Local\\InforRPA\\M3InvoiceProcessingGenAIV3"}, {"fileName": "ProcessPOInvoiceAPI_Ind.xaml", "filePath": "C:\\Users\\<USER>\\AppData\\Local\\InforRPA\\M3InvoiceProcessingGenAIV3"}, {"fileName": "ReadFilesFromFolder.xaml", "filePath": "C:\\Users\\<USER>\\AppData\\Local\\InforRPA\\M3InvoiceProcessingGenAIV3"}, {"fileName": "RowsFromDeliveryNote.xaml", "filePath": "C:\\Users\\<USER>\\AppData\\Local\\InforRPA\\M3InvoiceProcessingGenAIV3"}, {"fileName": "SendNotification.xaml", "filePath": "C:\\Users\\<USER>\\AppData\\Local\\InforRPA\\M3InvoiceProcessingGenAIV3"}, {"fileName": "SendtoApprovalWorkflow.xaml", "filePath": "C:\\Users\\<USER>\\AppData\\Local\\InforRPA\\M3InvoiceProcessingGenAIV3"}, {"fileName": "SendToIDM.xaml", "filePath": "C:\\Users\\<USER>\\AppData\\Local\\InforRPA\\M3InvoiceProcessingGenAIV3"}, {"fileName": "SendToWidgetIDM.xaml", "filePath": "C:\\Users\\<USER>\\AppData\\Local\\InforRPA\\M3InvoiceProcessingGenAIV3"}, {"fileName": "SentToInvoiceProcessingResults_Attributes.xaml", "filePath": "C:\\Users\\<USER>\\AppData\\Local\\InforRPA\\M3InvoiceProcessingGenAIV3"}, {"fileName": "SentToInvoiceProcessingResults_Headers.xaml", "filePath": "C:\\Users\\<USER>\\AppData\\Local\\InforRPA\\M3InvoiceProcessingGenAIV3"}, {"fileName": "SentToInvoiceProcessingResults_LineData.xaml", "filePath": "C:\\Users\\<USER>\\AppData\\Local\\InforRPA\\M3InvoiceProcessingGenAIV3"}, {"fileName": "SentToRPAReports.xaml", "filePath": "C:\\Users\\<USER>\\AppData\\Local\\InforRPA\\M3InvoiceProcessingGenAIV3"}, {"fileName": "SupplierInfo.xaml", "filePath": "C:\\Users\\<USER>\\AppData\\Local\\InforRPA\\M3InvoiceProcessingGenAIV3"}, {"fileName": "temp-response.json", "filePath": "C:\\Users\\<USER>\\AppData\\Local\\InforRPA\\M3InvoiceProcessingGenAIV3"}, {"fileName": "update_activities.ps1", "filePath": "C:\\Users\\<USER>\\AppData\\Local\\InforRPA\\M3InvoiceProcessingGenAIV3"}, {"fileName": "vatConfiguration.xaml", "filePath": "C:\\Users\\<USER>\\AppData\\Local\\InforRPA\\M3InvoiceProcessingGenAIV3"}, {"fileName": "vendorID.xaml", "filePath": "C:\\Users\\<USER>\\AppData\\Local\\InforRPA\\M3InvoiceProcessingGenAIV3"}, {"fileName": "VendorIDAddressMatch.xaml", "filePath": "C:\\Users\\<USER>\\AppData\\Local\\InforRPA\\M3InvoiceProcessingGenAIV3"}, {"fileName": "VerifyInvoiceExists.xaml", "filePath": "C:\\Users\\<USER>\\AppData\\Local\\InforRPA\\M3InvoiceProcessingGenAIV3"}], "resources": [], "projectType": "standard", "expressionLanguage": "VBscript", "backgroundProcess": false, "attended": true, "tenantMetadata": [{"tenantId": "DEVMRKT_DEV", "processId": "5EA6AD16-F13A-4D79-8BB5-88E5C0AF86D3", "projectVersion": "1.0.63", "globalPackageNamespace": null, "iprRegistered": false}, {"tenantId": "INTERSNACK_DEV", "processId": "52283BBB-B89E-439B-99EC-E26E4BC2F03A", "projectVersion": "1.0.20", "globalPackageNamespace": null, "iprRegistered": false}, {"tenantId": "IKEAINDUSTRY_DEV", "processId": "694F6C3A-D598-4E9C-9BEB-102087900120", "projectVersion": "1.0.17", "globalPackageNamespace": null, "iprRegistered": false}, {"tenantId": "D2LY9GSPFFP9LWXF_TST", "processId": "D5C84687-8489-4967-8AB8-03AA2C187431", "projectVersion": "1.0.4", "globalPackageNamespace": null, "iprRegistered": false}, {"tenantId": "MANDALA_DEM", "processId": "F830A5F0-6A69-458B-A1DF-12F9FEF3D488", "projectVersion": "1.0.7", "globalPackageNamespace": null, "iprRegistered": false}]}