{"name": "M3InvoiceProcessingGenAIV3", "description": "M3InvoiceProcessingGenAIV3Merged 1.0.22", "main": "MainSequence.xaml", "dependencies": {"mscorlib": "[*******]", "PresentationFramework": "[*******]", "System.Xaml": "[*******]", "System": "[*******]", "System.Activities": "[*******]", "System.Activities.Presentation": "[*******]", "YDock": "[*******]", "System.Windows.Forms": "[*******]", "WindowsBase": "[*******]", "NLog": "[*******]", "PresentationCore": "[*******]", "Newtonsoft.Json": "[********]", "System.Drawing": "[*******]", "System.Workflow.Activities": "[*******]", "System.Core": "[*******]", "UiRecorder": "[*******]", "System.IO.Compression": "[*******]", "Microsoft.WindowsAPICodePack.Shell": "[*******]", "Microsoft.Web.WebView2.Core": "[1.0.818.41]", "Microsoft.Web.WebView2.WinForms": "[1.0.818.41]", "System.Net.Http": "[*******]", "System.Configuration": "[*******]", "System.Activities.Core.Presentation": "[*******]", "Microsoft.CSharp": "[*******]", "System.Xml": "[*******]", "System.Workflow.ComponentModel": "[*******]", "System.Xml.Linq": "[*******]", "System.IO.Compression.FileSystem": "[*******]", "RestSharp": "[106.12.0.0]"}, "packages": {"Infor.RPA.Utilities": "[*******]", "Infor.RPA.Utility.Editor": "[*******]", "Infor.Activities.Web": "[*******]", "Infor.Activities.Desktop": "[*******]", "Infor.Activities.Debug": "[*******]", "Infor.Activities.Email": "[*******]", "Infor.Activities.Excel": "[*******]", "Infor.Activities.HTTPRequests": "[*******]", "Infor.Activities.IONAPI": "[*******]", "Infor.Activities.OCR": "[*******]", "Infor.RPA.OCR": "[*******]", "Infor.Activities.OneDrive": "[*******]", "Infor.Activities.Sys": "[*******]", "Infor.Activities.Datatable": "[*******]", "Infor.Activities.SharePointList": "[*******]", "Infor.Activities.Workflow": "[*******]", "Infor.Activities.ExcelOnline": "[*******]", "Infor.RPA.Security": "[*******]", "Infor.RPA.Commons": "[*******]"}, "schemaVersion": "1.0", "studioVersion": "2025.05.5\r\n.688", "sourceFiles": [{"fileName": "AddCharge.xaml", "filePath": "C:\\Users\\<USER>\\AppData\\Local\\InforRPA\\M3InvoiceProcessingGenAIV3"}, {"fileName": "AddHead.xaml", "filePath": "C:\\Users\\<USER>\\AppData\\Local\\InforRPA\\M3InvoiceProcessingGenAIV3"}, {"fileName": "AddLine.xaml", "filePath": "C:\\Users\\<USER>\\AppData\\Local\\InforRPA\\M3InvoiceProcessingGenAIV3"}, {"fileName": "approval.xaml", "filePath": "C:\\Users\\<USER>\\AppData\\Local\\InforRPA\\M3InvoiceProcessingGenAIV3"}, {"fileName": "ApprovalGUID.xaml", "filePath": "C:\\Users\\<USER>\\AppData\\Local\\InforRPA\\M3InvoiceProcessingGenAIV3"}, {"fileName": "APResp.xaml", "filePath": "C:\\Users\\<USER>\\AppData\\Local\\InforRPA\\M3InvoiceProcessingGenAIV3"}, {"fileName": "APResp2.xaml", "filePath": "C:\\Users\\<USER>\\AppData\\Local\\InforRPA\\M3InvoiceProcessingGenAIV3"}, {"fileName": "BulkFileHandling.xaml", "filePath": "C:\\Users\\<USER>\\AppData\\Local\\InforRPA\\M3InvoiceProcessingGenAIV3"}, {"fileName": "CallColeman.xaml", "filePath": "C:\\Users\\<USER>\\AppData\\Local\\InforRPA\\M3InvoiceProcessingGenAIV3"}, {"fileName": "CheckApproval.xaml", "filePath": "C:\\Users\\<USER>\\AppData\\Local\\InforRPA\\M3InvoiceProcessingGenAIV3"}, {"fileName": "Classification_Split.xaml", "filePath": "C:\\Users\\<USER>\\AppData\\Local\\InforRPA\\M3InvoiceProcessingGenAIV3"}, {"fileName": "CreateDirectories.xaml", "filePath": "C:\\Users\\<USER>\\AppData\\Local\\InforRPA\\M3InvoiceProcessingGenAIV3"}, {"fileName": "DivisionInfo.xaml", "filePath": "C:\\Users\\<USER>\\AppData\\Local\\InforRPA\\M3InvoiceProcessingGenAIV3"}, {"fileName": "DivisiontoGLCode.xaml", "filePath": "C:\\Users\\<USER>\\AppData\\Local\\InforRPA\\M3InvoiceProcessingGenAIV3"}, {"fileName": "DivisionVendorTolerance-old.xaml", "filePath": "C:\\Users\\<USER>\\AppData\\Local\\InforRPA\\M3InvoiceProcessingGenAIV3"}, {"fileName": "DivisionVendorTolerance.xaml", "filePath": "C:\\Users\\<USER>\\AppData\\Local\\InforRPA\\M3InvoiceProcessingGenAIV3"}, {"fileName": "ExportMI.xaml", "filePath": "C:\\Users\\<USER>\\AppData\\Local\\InforRPA\\M3InvoiceProcessingGenAIV3"}, {"fileName": "ExtractFromDatalake - Copy.xaml", "filePath": "C:\\Users\\<USER>\\AppData\\Local\\InforRPA\\M3InvoiceProcessingGenAIV3"}, {"fileName": "GenAI.xaml", "filePath": "C:\\Users\\<USER>\\AppData\\Local\\InforRPA\\M3InvoiceProcessingGenAIV3"}, {"fileName": "GenAI_UnExpInv.xaml", "filePath": "C:\\Users\\<USER>\\AppData\\Local\\InforRPA\\M3InvoiceProcessingGenAIV3"}, {"fileName": "getGenAIPrompt.xaml", "filePath": "C:\\Users\\<USER>\\AppData\\Local\\InforRPA\\M3InvoiceProcessingGenAIV3"}, {"fileName": "GetOCRValuesNew.xaml", "filePath": "C:\\Users\\<USER>\\AppData\\Local\\InforRPA\\M3InvoiceProcessingGenAIV3"}, {"fileName": "getvendordetails.xaml", "filePath": "C:\\Users\\<USER>\\AppData\\Local\\InforRPA\\M3InvoiceProcessingGenAIV3"}, {"fileName": "LinesExtractionWithPO.xaml", "filePath": "C:\\Users\\<USER>\\AppData\\Local\\InforRPA\\M3InvoiceProcessingGenAIV3"}, {"fileName": "M3InvoiceProcessingGenAIV3.zip", "filePath": "C:\\Users\\<USER>\\AppData\\Local\\InforRPA\\M3InvoiceProcessingGenAIV3"}, {"fileName": "M3InvoiceProcessingGenAIV31.0.12.zip", "filePath": "C:\\Users\\<USER>\\AppData\\Local\\InforRPA\\M3InvoiceProcessingGenAIV3"}, {"fileName": "M3InvoiceProcessingGenAIV31.0.6.zip", "filePath": "C:\\Users\\<USER>\\AppData\\Local\\InforRPA\\M3InvoiceProcessingGenAIV3"}, {"fileName": "M3InvoiceProcessingGenAIV31.0.8.zip", "filePath": "C:\\Users\\<USER>\\AppData\\Local\\InforRPA\\M3InvoiceProcessingGenAIV3"}, {"fileName": "MainSequence.xaml", "filePath": "C:\\Users\\<USER>\\AppData\\Local\\InforRPA\\M3InvoiceProcessingGenAIV3"}, {"fileName": "MoveFileToSuccessFailureFolder.xaml", "filePath": "C:\\Users\\<USER>\\AppData\\Local\\InforRPA\\M3InvoiceProcessingGenAIV3"}, {"fileName": "OneDeliveryNotFound.xaml", "filePath": "C:\\Users\\<USER>\\AppData\\Local\\InforRPA\\M3InvoiceProcessingGenAIV3"}, {"fileName": "Outlook_preprocess.xaml", "filePath": "C:\\Users\\<USER>\\AppData\\Local\\InforRPA\\M3InvoiceProcessingGenAIV3"}, {"fileName": "Outlook_ProcessNew.xaml", "filePath": "C:\\Users\\<USER>\\AppData\\Local\\InforRPA\\M3InvoiceProcessingGenAIV3"}, {"fileName": "ProcessDeliveryNote.xaml", "filePath": "C:\\Users\\<USER>\\AppData\\Local\\InforRPA\\M3InvoiceProcessingGenAIV3"}, {"fileName": "ProcessDocument.xaml", "filePath": "C:\\Users\\<USER>\\AppData\\Local\\InforRPA\\M3InvoiceProcessingGenAIV3"}, {"fileName": "ProcessExpenseInvoice - Copy.xaml", "filePath": "C:\\Users\\<USER>\\AppData\\Local\\InforRPA\\M3InvoiceProcessingGenAIV3"}, {"fileName": "ProcessExpenseInvoice.xaml", "filePath": "C:\\Users\\<USER>\\AppData\\Local\\InforRPA\\M3InvoiceProcessingGenAIV3"}, {"fileName": "ProcessExpenseWithPO.xaml", "filePath": "C:\\Users\\<USER>\\AppData\\Local\\InforRPA\\M3InvoiceProcessingGenAIV3"}, {"fileName": "ProcessPOInvoice.xaml", "filePath": "C:\\Users\\<USER>\\AppData\\Local\\InforRPA\\M3InvoiceProcessingGenAIV3"}, {"fileName": "ProcessPOInvoiceAPI.xaml", "filePath": "C:\\Users\\<USER>\\AppData\\Local\\InforRPA\\M3InvoiceProcessingGenAIV3"}, {"fileName": "ProcessPOInvoiceAPI_Ind.xaml", "filePath": "C:\\Users\\<USER>\\AppData\\Local\\InforRPA\\M3InvoiceProcessingGenAIV3"}, {"fileName": "ReadFilesFromFolder.xaml", "filePath": "C:\\Users\\<USER>\\AppData\\Local\\InforRPA\\M3InvoiceProcessingGenAIV3"}, {"fileName": "RowsFromDeliveryNote.xaml", "filePath": "C:\\Users\\<USER>\\AppData\\Local\\InforRPA\\M3InvoiceProcessingGenAIV3"}, {"fileName": "SendNotification.xaml", "filePath": "C:\\Users\\<USER>\\AppData\\Local\\InforRPA\\M3InvoiceProcessingGenAIV3"}, {"fileName": "SendtoApprovalWorkflow.xaml", "filePath": "C:\\Users\\<USER>\\AppData\\Local\\InforRPA\\M3InvoiceProcessingGenAIV3"}, {"fileName": "SendToIDM.xaml", "filePath": "C:\\Users\\<USER>\\AppData\\Local\\InforRPA\\M3InvoiceProcessingGenAIV3"}, {"fileName": "SendToWidgetIDM.xaml", "filePath": "C:\\Users\\<USER>\\AppData\\Local\\InforRPA\\M3InvoiceProcessingGenAIV3"}, {"fileName": "SentToInvoiceProcessingResults_Attributes.xaml", "filePath": "C:\\Users\\<USER>\\AppData\\Local\\InforRPA\\M3InvoiceProcessingGenAIV3"}, {"fileName": "SentToInvoiceProcessingResults_Headers.xaml", "filePath": "C:\\Users\\<USER>\\AppData\\Local\\InforRPA\\M3InvoiceProcessingGenAIV3"}, {"fileName": "SentToInvoiceProcessingResults_LineData.xaml", "filePath": "C:\\Users\\<USER>\\AppData\\Local\\InforRPA\\M3InvoiceProcessingGenAIV3"}, {"fileName": "SentToRPAReports.xaml", "filePath": "C:\\Users\\<USER>\\AppData\\Local\\InforRPA\\M3InvoiceProcessingGenAIV3"}, {"fileName": "SupplierInfo.xaml", "filePath": "C:\\Users\\<USER>\\AppData\\Local\\InforRPA\\M3InvoiceProcessingGenAIV3"}, {"fileName": "temp-response.json", "filePath": "C:\\Users\\<USER>\\AppData\\Local\\InforRPA\\M3InvoiceProcessingGenAIV3"}, {"fileName": "update_activities.ps1", "filePath": "C:\\Users\\<USER>\\AppData\\Local\\InforRPA\\M3InvoiceProcessingGenAIV3"}, {"fileName": "vatConfiguration.xaml", "filePath": "C:\\Users\\<USER>\\AppData\\Local\\InforRPA\\M3InvoiceProcessingGenAIV3"}, {"fileName": "vendorID.xaml", "filePath": "C:\\Users\\<USER>\\AppData\\Local\\InforRPA\\M3InvoiceProcessingGenAIV3"}, {"fileName": "VendorIDAddressMatch.xaml", "filePath": "C:\\Users\\<USER>\\AppData\\Local\\InforRPA\\M3InvoiceProcessingGenAIV3"}, {"fileName": "VerifyInvoiceExists.xaml", "filePath": "C:\\Users\\<USER>\\AppData\\Local\\InforRPA\\M3InvoiceProcessingGenAIV3"}, {"fileName": "temp-response.json", "filePath": "C:\\Users\\<USER>\\AppData\\Local\\InforRPA\\M3InvoiceProcessingGenAIV3\\Temp\\f69e5a30-0b3c-4f29-9d8a-1e6947a44cd8"}], "resources": [], "projectType": "standard", "expressionLanguage": "VBscript", "backgroundProcess": false, "attended": true, "tenantMetadata": [{"tenantId": "DEVMRKT_DEV", "processId": "5EA6AD16-F13A-4D79-8BB5-88E5C0AF86D3", "projectVersion": "1.0.23", "globalPackageNamespace": null, "iprRegistered": false}, {"tenantId": "INTERSNACK_DEV", "processId": "6292DE92-8025-4D47-93DB-671EC5D4FDB8", "projectVersion": "1.0.0", "globalPackageNamespace": null, "iprRegistered": false}]}