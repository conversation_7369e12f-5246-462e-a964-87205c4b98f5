﻿<Activity mc:Ignorable="sap sap2010 sads" x:Class="RehostedWorkflowDesigner.Workflow"
 xmlns="http://schemas.microsoft.com/netfx/2009/xaml/activities"
 xmlns:iai="clr-namespace:Infor.Activities.IONAPI;assembly=Infor.Activities.IONAPI"
 xmlns:ias="clr-namespace:Infor.Activities.Sys;assembly=Infor.Activities.Sys"
 xmlns:iaw="clr-namespace:Infor.Activities.Workflow;assembly=Infor.Activities.Workflow"
 xmlns:iru="clr-namespace:Infor.RPA.Utilities;assembly=Infor.RPA.Utilities"
 xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006"
 xmlns:mva="clr-namespace:Microsoft.VisualBasic.Activities;assembly=System.Activities"
 xmlns:njl="clr-namespace:Newtonsoft.Json.Linq;assembly=Newtonsoft.Json"
 xmlns:s="clr-namespace:System;assembly=mscorlib"
 xmlns:sads="http://schemas.microsoft.com/netfx/2010/xaml/activities/debugger"
 xmlns:sap="http://schemas.microsoft.com/netfx/2009/xaml/activities/presentation"
 xmlns:sap2010="http://schemas.microsoft.com/netfx/2010/xaml/activities/presentation"
 xmlns:scg="clr-namespace:System.Collections.Generic;assembly=mscorlib"
 xmlns:sco="clr-namespace:System.Collections.ObjectModel;assembly=mscorlib"
 xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml">
  <x:Members>
    <x:Property Name="miscValues" Type="InArgument(scg:Dictionary(x:String, x:Object))" />
    <x:Property Name="tenantID" Type="InArgument(x:String)" />
    <x:Property Name="projectPath" Type="InArgument(x:String)" />
    <x:Property Name="APResp" Type="InArgument(x:String)" />
    <x:Property Name="DictOcrValues" Type="InArgument(scg:Dictionary(x:String, x:Object))" />
    <x:Property Name="division" Type="InArgument(x:String)" />
    <x:Property Name="status" Type="InArgument(x:String)" />
    <x:Property Name="distributionType" Type="InArgument(x:String)" />
    <x:Property Name="userIdentifier" Type="InArgument(x:String)" />
    <x:Property Name="emailSubject" Type="InArgument(x:String)" />
    <x:Property Name="emailReceivedTime" Type="InArgument(x:String)" />
    <x:Property Name="ThresholdReached" Type="OutArgument(x:Boolean)" />
    <x:Property Name="lastRunTime" Type="InArgument(x:String)" />
    <x:Property Name="commentStatus" Type="InOutArgument(x:String)" />
    <x:Property Name="logfile" Type="InArgument(x:String)" />
    <x:Property Name="arg_in_duedate" Type="InArgument(x:String)" />
    <x:Property Name="User_GUID" Type="InArgument(x:String)" />
  </x:Members>
  <mva:VisualBasic.Settings>
    <x:Null />
  </mva:VisualBasic.Settings>
  <sap2010:WorkflowViewState.IdRef>RehostedWorkflowDesigner.Workflow_1</sap2010:WorkflowViewState.IdRef>
  <TextExpression.NamespacesForImplementation>
    <sco:Collection x:TypeArguments="x:String">
      <x:String>System.Activities</x:String>
      <x:String>System.Activities.Statements</x:String>
      <x:String>System.Activities.Expressions</x:String>
      <x:String>System.Activities.Validation</x:String>
      <x:String>System.Activities.XamlIntegration</x:String>
      <x:String>Infor.Activities.Email</x:String>
      <x:String>Infor.Activities.IONAPI</x:String>
      <x:String>Infor.Activities.Workflow</x:String>
      <x:String>Infor.RPA.Utilities</x:String>
      <x:String>Microsoft.VisualBasic.Activities</x:String>
      <x:String>Microsoft.VisualBasic.CompilerServices</x:String>
      <x:String>System</x:String>
      <x:String>System.Activities.Debugger.Symbol</x:String>
      <x:String>System.Collections.Generic</x:String>
      <x:String>System.Linq</x:String>
      <x:String>System.Windows.Markup</x:String>
      <x:String>Newtonsoft.Json.Linq</x:String>
      <x:String>Newtonsoft.Json</x:String>
      <x:String>System.Globalization</x:String>
    </sco:Collection>
  </TextExpression.NamespacesForImplementation>
  <TextExpression.ReferencesForImplementation>
    <sco:Collection x:TypeArguments="AssemblyReference">
      <AssemblyReference>System.Activities</AssemblyReference>
      <AssemblyReference>Infor.Activities.Email</AssemblyReference>
      <AssemblyReference>Infor.Activities.IONAPI</AssemblyReference>
      <AssemblyReference>Infor.Activities.Workflow</AssemblyReference>
      <AssemblyReference>Infor.RPA.Utilities</AssemblyReference>
      <AssemblyReference>Microsoft.VisualBasic</AssemblyReference>
      <AssemblyReference>mscorlib</AssemblyReference>
      <AssemblyReference>System.Core</AssemblyReference>
      <AssemblyReference>System</AssemblyReference>
      <AssemblyReference>System.ServiceModel</AssemblyReference>
      <AssemblyReference>System.ComponentModel.Composition</AssemblyReference>
      <AssemblyReference>System.Memory</AssemblyReference>
      <AssemblyReference>System.Runtime.WindowsRuntime</AssemblyReference>
      <AssemblyReference>Microsoft.Bcl.AsyncInterfaces</AssemblyReference>
      <AssemblyReference>PresentationFramework</AssemblyReference>
      <AssemblyReference>WindowsBase</AssemblyReference>
      <AssemblyReference>PresentationCore</AssemblyReference>
      <AssemblyReference>System.Xaml</AssemblyReference>
      <AssemblyReference>Newtonsoft.Json</AssemblyReference>
    </sco:Collection>
  </TextExpression.ReferencesForImplementation>
  <Sequence DisplayName="Threshold Sequence" sap2010:WorkflowViewState.IdRef="Sequence_6">
    <Sequence.Variables>
      <Variable x:TypeArguments="x:String" Name="Threshold" />
      <Variable x:TypeArguments="s:DateTime" Name="LastRunDateaddday" />
      <Variable x:TypeArguments="s:DateTime" Name="newduedate" />
      <Variable x:TypeArguments="x:String" Name="LastRunDate" />
      <Variable x:TypeArguments="njl:JToken" Name="out1" />
      <Variable x:TypeArguments="s:DateTime" Name="newreqdate" />
      <Variable x:TypeArguments="s:DateTime" Name="newdatereq" />
      <Variable x:TypeArguments="s:DateTime" Name="LastrunDateTimeP" />
      <Variable x:TypeArguments="s:String[]" Name="userIdentifierlist" />
    </Sequence.Variables>
    <Assign sap2010:WorkflowViewState.IdRef="Assign_8">
      <Assign.To>
        <OutArgument x:TypeArguments="x:Boolean">[ThresholdReached]</OutArgument>
      </Assign.To>
      <Assign.Value>
        <InArgument x:TypeArguments="x:Boolean">False</InArgument>
      </Assign.Value>
    </Assign>
    <Assign DisplayName="Assign-lastrundatetime" sap2010:WorkflowViewState.IdRef="Assign_87">
      <Assign.To>
        <OutArgument x:TypeArguments="s:DateTime">[LastrunDateTimeP]</OutArgument>
      </Assign.To>
      <Assign.Value>
        <InArgument x:TypeArguments="s:DateTime">[DateTime.Parse(lastRunTime).Date]</InArgument>
      </Assign.Value>
    </Assign>
    <Assign sap2010:Annotation.AnnotationText="lastrundate+1day" DisplayName="Assign-duedate" sap2010:WorkflowViewState.IdRef="Assign_39">
      <Assign.To>
        <OutArgument x:TypeArguments="s:DateTime">[LastRunDateaddday]</OutArgument>
      </Assign.To>
      <Assign.Value>
        <InArgument x:TypeArguments="s:DateTime">[LastrunDateTimeP.AddDays(1)]</InArgument>
      </Assign.Value>
    </Assign>
    <Assign DisplayName="Assign-duedate" sap2010:WorkflowViewState.IdRef="Assign_41">
      <Assign.To>
        <OutArgument x:TypeArguments="x:String">[LastRunDate]</OutArgument>
      </Assign.To>
      <Assign.Value>
        <InArgument x:TypeArguments="x:String">[LastRunDateaddday.ToString("yyyyMMdd")]</InArgument>
      </Assign.Value>
    </Assign>
    <Assign DisplayName="Assign-userIdentifierlist" sap2010:WorkflowViewState.IdRef="Assign_101">
      <Assign.To>
        <OutArgument x:TypeArguments="s:String[]">[userIdentifierlist]</OutArgument>
      </Assign.To>
      <Assign.Value>
        <InArgument x:TypeArguments="s:String[]">[User_GUID.Split(","c)]</InArgument>
      </Assign.Value>
    </Assign>
    <If Condition="[miscValues(&quot;Duedate&quot;).tostring.ToLower =&quot;true&quot;]" sap2010:WorkflowViewState.IdRef="If_11">
      <If.Then>
        <Sequence DisplayName="Call Due Date API" sap2010:WorkflowViewState.IdRef="Sequence_2">
          <Sequence.Variables>
            <Variable x:TypeArguments="iru:ResponseObject" Name="respValueForDueDate" />
            <Variable x:TypeArguments="x:Int32" Name="respCodeForDueDate" />
            <Variable x:TypeArguments="x:String" Name="DueDateAPIOutput" />
          </Sequence.Variables>
          <Sequence DisplayName="DUEdateSequence" sap2010:WorkflowViewState.IdRef="Sequence_12">
            <Sequence.Variables>
              <Variable x:TypeArguments="x:String" Name="ExceptionStatus" />
              <Variable x:TypeArguments="s:DateTime" Name="newduedateinv" />
            </Sequence.Variables>
            <Assign DisplayName="Assign-1" sap2010:WorkflowViewState.IdRef="Assign_88">
              <Assign.To>
                <OutArgument x:TypeArguments="x:String">[DueDateAPIOutput]</OutArgument>
              </Assign.To>
              <Assign.Value>
                <InArgument x:TypeArguments="x:String">[arg_in_duedate]</InArgument>
              </Assign.Value>
            </Assign>
            <If Condition="[string.IsNullOrEmpty(DueDateAPIOutput)]" DisplayName="If-duedateempty" sap2010:WorkflowViewState.IdRef="If_28">
              <If.Then>
                <ias:Append_Line ErrorCode="{x:Null}" ContinueOnError="True" DisplayName="Append Line" sap2010:WorkflowViewState.IdRef="Append_Line_3" Line="Due Date is received as empty" Source="[logFile]" />
              </If.Then>
              <If.Else>
                <Sequence sap2010:WorkflowViewState.IdRef="Sequence_17">
                  <Assign DisplayName="Assign-2" sap2010:WorkflowViewState.IdRef="Assign_89">
                    <Assign.To>
                      <OutArgument x:TypeArguments="s:DateTime">[newduedate]</OutArgument>
                    </Assign.To>
                    <Assign.Value>
                      <InArgument x:TypeArguments="s:DateTime">[DateTime.ParseExact(DueDateAPIOutput, {"dd/MM/yyyy","yyyy-MM-dd","MM/dd/yyyy"}, System.Globalization.CultureInfo.InvariantCulture, Globalization.DateTimeStyles.None)]</InArgument>
                    </Assign.Value>
                  </Assign>
                  <Assign DisplayName="Assign-3" sap2010:WorkflowViewState.IdRef="Assign_91">
                    <Assign.To>
                      <OutArgument x:TypeArguments="x:String">[Threshold]</OutArgument>
                    </Assign.To>
                    <Assign.Value>
                      <InArgument x:TypeArguments="x:String">[newduedate.ToString("yyyyMMdd")]</InArgument>
                    </Assign.Value>
                  </Assign>
                </Sequence>
              </If.Else>
            </If>
            <If Condition="[LastRunDate=Threshold]" DisplayName="If-threshold reached" sap2010:WorkflowViewState.IdRef="If_26">
              <If.Then>
                <Sequence sap2010:WorkflowViewState.IdRef="Sequence_16">
                  <If Condition="[status=&quot;PONOTRECEIVED&quot; and NOT commentStatus.ToLower.Contains(&quot;alert&quot;)]" DisplayName="If-status_Comment" sap2010:WorkflowViewState.IdRef="If_25">
                    <If.Then>
                      <Sequence sap2010:WorkflowViewState.IdRef="Sequence_11">
                        <If Condition="[miscValues(&quot;Action&quot;).tostring = &quot;Notification&quot;]" DisplayName="If-notification" sap2010:WorkflowViewState.IdRef="If_24">
                          <If.Then>
                            <Sequence DisplayName="Duedate_notification Sequence" sap2010:WorkflowViewState.IdRef="Sequence_18">
                              <ForEach x:TypeArguments="x:String" DisplayName="ForEach&lt;String&gt;" sap2010:WorkflowViewState.IdRef="ForEach`1_1" Values="[userIdentifierlist]">
                                <ActivityAction x:TypeArguments="x:String">
                                  <ActivityAction.Argument>
                                    <DelegateInArgument x:TypeArguments="x:String" Name="userIdentifier" />
                                  </ActivityAction.Argument>
                                  <iaw:InvokeWorkflow OutputArguments="{x:Null}" ResponseCode="{x:Null}" Arguments="[New Dictionary(Of String, Object) From {{&quot;status&quot;,&quot;ALERT &quot; + status},{&quot;statusComments&quot;,&quot;ALERT!! Please receive the goods.&quot;},{&quot;distributionType&quot;,distributionType},{&quot;userIdentifier&quot;,userIdentifier},{&quot;emailSubject&quot;,emailSubject},{&quot;emailReceivedTime&quot;,emailReceivedTime},{&quot;tenantID&quot;,tenantID},{&quot;invoiceNumber&quot;,DictOcrValues(&quot;INVOICE_RECEIPT_ID&quot;).ToString},{&quot;poNumber&quot;,DictOcrValues(&quot;PO_NUMBER&quot;).ToString},{&quot;subTotal&quot;,DictOcrValues(&quot;SUBTOTAL&quot;).ToString},{&quot;total&quot;,DictOcrValues(&quot;TOTAL&quot;).ToString},{&quot;drillbackLink&quot;,&quot;LogicalId=lid://infor.m3.m3&amp;program=APS450&amp;fieldNames=W2OBKV,&quot;+DictOcrValues(&quot;INVOICE_RECEIPT_ID&quot;).ToString+&quot;,W3OBKV,,W1OBKV,&quot;+ division+&quot;&amp;includeStartPanel=True&amp;source=MForms&amp;requirePanel=True&amp;sortingOrder=2&amp;view=STD02-01&amp;tableName=FAPIBH&amp;keys=E5CONO,&quot;+miscValues(&quot;company&quot;).ToString+&quot;,E5DIVI,&quot;+division+&quot;,E5INBN,+&amp;parameters=XXSINO,&quot;+DictOcrValues(&quot;INVOICE_RECEIPT_ID&quot;).ToString+&quot;&amp;startpanel=B&quot;},{&quot;invoiceDate&quot;,DictOcrValues(&quot;INVOICE_RECEIPT_DATE&quot;).ToString},{&quot;tax&quot;,DictOcrValues(&quot;VAT_AMOUNT&quot;).ToString},{&quot;charges&quot;,DictOcrValues(&quot;SHIPPING_AND_HANDLING_AMOUNT&quot;).ToString},{&quot;discount&quot;,DictOcrValues(&quot;Invoice_Level_Discount_AMOUNT&quot;).ToString},{&quot;deliveryNote&quot;,DictOcrValues(&quot;DELIVERY_NOTE_DATA&quot;).ToString} ,{&quot;miscValues&quot;,miscValues},{&quot;APResp&quot;,APResp}}]" ContinueOnError="True" DisplayName="Send Notification" sap2010:WorkflowViewState.IdRef="InvokeWorkflow_8" WorkflowFile="[projectPath+&quot;\SendNotification.xaml&quot;]" />
                                </ActivityAction>
                              </ForEach>
                              <ias:Append_Line ErrorCode="{x:Null}" ContinueOnError="True" DisplayName="Append Line" sap2010:WorkflowViewState.IdRef="Append_Line_4" Line="Sent Notification after Duedate" Source="[logFile]" />
                            </Sequence>
                          </If.Then>
                          <If.Else>
                            <If Condition="[miscValues(&quot;Action&quot;).tostring = &quot;Registration&quot;]" DisplayName="If-reg" sap2010:WorkflowViewState.IdRef="If_23">
                              <If.Then>
                                <Assign sap2010:WorkflowViewState.IdRef="Assign_26">
                                  <Assign.To>
                                    <OutArgument x:TypeArguments="x:Boolean">[ThresholdReached]</OutArgument>
                                  </Assign.To>
                                  <Assign.Value>
                                    <InArgument x:TypeArguments="x:Boolean">True</InArgument>
                                  </Assign.Value>
                                </Assign>
                              </If.Then>
                            </If>
                          </If.Else>
                        </If>
                      </Sequence>
                    </If.Then>
                  </If>
                </Sequence>
              </If.Then>
            </If>
          </Sequence>
        </Sequence>
      </If.Then>
      <If.Else>
        <If Condition="[miscValues(&quot;InvoiceDate&quot;).tostring.ToLower =&quot;true&quot;]" DisplayName="If-invoice date" sap2010:WorkflowViewState.IdRef="If_10">
          <If.Then>
            <Sequence DisplayName="Invioce Date" sap2010:WorkflowViewState.IdRef="Sequence_3">
              <Sequence.Variables>
                <Variable x:TypeArguments="x:String" Name="InvoiceDate" />
                <Variable x:TypeArguments="s:DateTime" Name="newinvoicedate" />
                <Variable x:TypeArguments="s:DateTime" Name="newdateinv" />
              </Sequence.Variables>
              <Assign DisplayName="Assign-1" sap2010:WorkflowViewState.IdRef="Assign_3">
                <Assign.To>
                  <OutArgument x:TypeArguments="x:String">[InvoiceDate]</OutArgument>
                </Assign.To>
                <Assign.Value>
                  <InArgument x:TypeArguments="x:String">[DictOcrValues("INVOICE_RECEIPT_DATE").tostring]</InArgument>
                </Assign.Value>
              </Assign>
              <Assign DisplayName="Assign-2" sap2010:WorkflowViewState.IdRef="Assign_35">
                <Assign.To>
                  <OutArgument x:TypeArguments="s:DateTime">[newinvoicedate]</OutArgument>
                </Assign.To>
                <Assign.Value>
                  <InArgument x:TypeArguments="s:DateTime">[DateTime.ParseExact(InvoiceDate, {"dd/MM/yyyy","yyyy-MM-dd","MM/dd/yyyy"}, System.Globalization.CultureInfo.InvariantCulture, Globalization.DateTimeStyles.None)]</InArgument>
                </Assign.Value>
              </Assign>
              <Assign DisplayName="Assign-3" sap2010:WorkflowViewState.IdRef="Assign_37">
                <Assign.To>
                  <OutArgument x:TypeArguments="s:DateTime">[newdateinv]</OutArgument>
                </Assign.To>
                <Assign.Value>
                  <InArgument x:TypeArguments="s:DateTime">[newinvoicedate.AddDays(CInt(miscValues("invoiceDateDays").ToString))]</InArgument>
                </Assign.Value>
              </Assign>
              <Assign DisplayName="Assign-4" sap2010:WorkflowViewState.IdRef="Assign_4">
                <Assign.To>
                  <OutArgument x:TypeArguments="x:String">[Threshold]</OutArgument>
                </Assign.To>
                <Assign.Value>
                  <InArgument x:TypeArguments="x:String">[newdateinv.ToString("yyyyMMdd")]</InArgument>
                </Assign.Value>
              </Assign>
              <If Condition="[LastRunDate=Threshold]" DisplayName="If-threshold reached" sap2010:WorkflowViewState.IdRef="If_5">
                <If.Then>
                  <If Condition="[status=&quot;PONOTRECEIVED&quot; and NOT commentStatus.ToLower.Contains(&quot;alert&quot;)]" DisplayName="If-status_Comment" sap2010:WorkflowViewState.IdRef="If_4">
                    <If.Then>
                      <If Condition="[miscValues(&quot;Action&quot;).tostring = &quot;Notification&quot;]" DisplayName="If-notification" sap2010:WorkflowViewState.IdRef="If_15">
                        <If.Then>
                          <Sequence DisplayName="Inv-Notification Sequence" sap2010:WorkflowViewState.IdRef="Sequence_19">
                            <ForEach x:TypeArguments="x:String" DisplayName="ForEach&lt;String&gt;" sap2010:WorkflowViewState.IdRef="ForEach`1_2" Values="[userIdentifierlist]">
                              <ActivityAction x:TypeArguments="x:String">
                                <ActivityAction.Argument>
                                  <DelegateInArgument x:TypeArguments="x:String" Name="userIdentifier" />
                                </ActivityAction.Argument>
                                <iaw:InvokeWorkflow OutputArguments="{x:Null}" ResponseCode="{x:Null}" Arguments="[New Dictionary(Of String, Object) From {{&quot;status&quot;,&quot;ALERT &quot; + status},{&quot;statusComments&quot;,&quot;ALERT!! Please receive the goods.&quot;},{&quot;distributionType&quot;,distributionType},{&quot;userIdentifier&quot;,userIdentifier},{&quot;emailSubject&quot;,emailSubject},{&quot;emailReceivedTime&quot;,emailReceivedTime},{&quot;tenantID&quot;,tenantID},{&quot;invoiceNumber&quot;,DictOcrValues(&quot;INVOICE_RECEIPT_ID&quot;).ToString},{&quot;poNumber&quot;,DictOcrValues(&quot;PO_NUMBER&quot;).ToString},{&quot;subTotal&quot;,DictOcrValues(&quot;SUBTOTAL&quot;).ToString},{&quot;total&quot;,DictOcrValues(&quot;TOTAL&quot;).ToString},{&quot;drillbackLink&quot;,&quot;LogicalId=lid://infor.m3.m3&amp;program=APS450&amp;fieldNames=W2OBKV,&quot;+DictOcrValues(&quot;INVOICE_RECEIPT_ID&quot;).ToString+&quot;,W3OBKV,,W1OBKV,&quot;+ division+&quot;&amp;includeStartPanel=True&amp;source=MForms&amp;requirePanel=True&amp;sortingOrder=2&amp;view=STD02-01&amp;tableName=FAPIBH&amp;keys=E5CONO,&quot;+miscValues(&quot;company&quot;).ToString+&quot;,E5DIVI,&quot;+division+&quot;,E5INBN,+&amp;parameters=XXSINO,&quot;+DictOcrValues(&quot;INVOICE_RECEIPT_ID&quot;).ToString+&quot;&amp;startpanel=B&quot;},{&quot;invoiceDate&quot;,DictOcrValues(&quot;INVOICE_RECEIPT_DATE&quot;).ToString},{&quot;tax&quot;,DictOcrValues(&quot;VAT_AMOUNT&quot;).ToString},{&quot;charges&quot;,DictOcrValues(&quot;SHIPPING_AND_HANDLING_AMOUNT&quot;).ToString},{&quot;discount&quot;,DictOcrValues(&quot;Invoice_Level_Discount_AMOUNT&quot;).ToString},{&quot;deliveryNote&quot;,DictOcrValues(&quot;DELIVERY_NOTE_DATA&quot;).ToString} ,{&quot;miscValues&quot;,miscValues},{&quot;APResp&quot;,APResp}}]" ContinueOnError="True" DisplayName="Send Notification" sap2010:WorkflowViewState.IdRef="InvokeWorkflow_9" WorkflowFile="[projectPath+&quot;\SendNotification.xaml&quot;]" />
                              </ActivityAction>
                            </ForEach>
                            <ias:Append_Line ErrorCode="{x:Null}" ContinueOnError="True" DisplayName="Append Line" sap2010:WorkflowViewState.IdRef="Append_Line_6" Line="Sent Notification after Duedate" Source="[logFile]" />
                          </Sequence>
                        </If.Then>
                        <If.Else>
                          <If Condition="[miscValues(&quot;Action&quot;).tostring = &quot;Registration&quot;]" DisplayName="If-reg" sap2010:WorkflowViewState.IdRef="If_14">
                            <If.Then>
                              <Assign sap2010:WorkflowViewState.IdRef="Assign_9">
                                <Assign.To>
                                  <OutArgument x:TypeArguments="x:Boolean">[ThresholdReached]</OutArgument>
                                </Assign.To>
                                <Assign.Value>
                                  <InArgument x:TypeArguments="x:Boolean">True</InArgument>
                                </Assign.Value>
                              </Assign>
                            </If.Then>
                          </If>
                        </If.Else>
                      </If>
                    </If.Then>
                  </If>
                </If.Then>
              </If>
            </Sequence>
          </If.Then>
          <If.Else>
            <If Condition="[miscValues(&quot;ExpectedReceiptDate&quot;).tostring.ToLower =&quot;true&quot;]" DisplayName="If-requesteddeliverydate" sap2010:WorkflowViewState.IdRef="If_9">
              <If.Then>
                <Sequence DisplayName="Call ExpectedReceiptDate API" sap2010:WorkflowViewState.IdRef="Sequence_5">
                  <Sequence.Variables>
                    <Variable x:TypeArguments="iru:ResponseObject" Name="respValueForDueDate" />
                    <Variable x:TypeArguments="x:Int32" Name="respCodeForDueDate" />
                    <Variable x:TypeArguments="x:String" Name="ExpectedReceiptDateAPIOutput" />
                    <Variable x:TypeArguments="iru:ResponseObject" Name="respValueForrequesteddeliveryDate" />
                    <Variable x:TypeArguments="x:Int32" Name="respCodeForrequesteddeliveryDate" />
                    <Variable x:TypeArguments="x:String" Name="PO_Number" />
                  </Sequence.Variables>
                  <Assign DisplayName="Assign-1" sap2010:WorkflowViewState.IdRef="Assign_83">
                    <Assign.To>
                      <OutArgument x:TypeArguments="x:String">[PO_Number]</OutArgument>
                    </Assign.To>
                    <Assign.Value>
                      <InArgument x:TypeArguments="x:String">[DictOcrValues("PO_NUMBER").tostring]</InArgument>
                    </Assign.Value>
                  </Assign>
                  <iai:IONAPIRequestWizard FileAttachments="{x:Null}" PostData="{x:Null}" ResponseCode="{x:Null}" ContentType="application_json" ContinueOnError="True" DisplayName="PPS200MI - IONAPI Request for requested delivery date" HttpMethod="GET" sap2010:WorkflowViewState.IdRef="IONAPIRequestWizard_3" Response="[respValueForrequesteddeliveryDate]" StatusCode="[respCodeForrequesteddeliveryDate]" Url="[tenantID+&quot;M3/m3api-rest/v2/execute/PPS200MI/GetHead&quot;]">
                    <iai:IONAPIRequestWizard.Headers>
                      <scg:List x:TypeArguments="scg:List(x:String)" Capacity="4">
                        <scg:List x:TypeArguments="x:String" Capacity="4">
                          <x:String>Accept</x:String>
                        </scg:List>
                        <scg:List x:TypeArguments="x:String" Capacity="4">
                          <x:String>application/json</x:String>
                        </scg:List>
                      </scg:List>
                    </iai:IONAPIRequestWizard.Headers>
                    <iai:IONAPIRequestWizard.QueryParameters>
                      <scg:List x:TypeArguments="scg:List(x:String)" Capacity="4">
                        <scg:List x:TypeArguments="x:String" Capacity="8">
                          <x:String>extendedresult</x:String>
                          <x:String>format</x:String>
                          <x:String>righttrim</x:String>
                          <x:String>excludeempty</x:String>
                          <x:String>dateformat</x:String>
                          <x:String>PUNO</x:String>
                        </scg:List>
                        <scg:List x:TypeArguments="x:String" Capacity="8">
                          <x:String>false</x:String>
                          <x:String>PRETTY</x:String>
                          <x:String>true</x:String>
                          <x:String>false</x:String>
                          <x:String>YMD8</x:String>
                          <x:String>PO_Number</x:String>
                        </scg:List>
                      </scg:List>
                    </iai:IONAPIRequestWizard.QueryParameters>
                  </iai:IONAPIRequestWizard>
                  <If Condition="[respCodeForrequesteddeliveryDate = 200]" DisplayName="If-pps200invokeworkflow" sap2010:WorkflowViewState.IdRef="If_8">
                    <If.Then>
                      <Sequence sap2010:WorkflowViewState.IdRef="Sequence_4">
                        <Sequence.Variables>
                          <Variable x:TypeArguments="x:String" Name="ExceptionStatus" />
                          <Variable x:TypeArguments="x:String" Name="dwdt" />
                        </Sequence.Variables>
                        <Assign DisplayName="Assign-receiptout" sap2010:WorkflowViewState.IdRef="Assign_42">
                          <Assign.To>
                            <OutArgument x:TypeArguments="njl:JToken">[out1]</OutArgument>
                          </Assign.To>
                          <Assign.Value>
                            <InArgument x:TypeArguments="njl:JToken">[JToken.Parse(respValueForrequesteddeliveryDate.ReadAsText)]</InArgument>
                          </Assign.Value>
                        </Assign>
                        <If Condition="[out1(&quot;results&quot;)(0)(&quot;records&quot;).ToString = &quot;[]&quot;]" DisplayName="If-PPS200-executed" sap2010:WorkflowViewState.IdRef="If_27">
                          <If.Then>
                            <Sequence DisplayName="PPS200_fail_Sequence" sap2010:WorkflowViewState.IdRef="Sequence_13">
                              <Assign sap2010:WorkflowViewState.IdRef="Assign_45">
                                <Assign.To>
                                  <OutArgument x:TypeArguments="x:String">[commentStatus]</OutArgument>
                                </Assign.To>
                                <Assign.Value>
                                  <InArgument x:TypeArguments="x:String">["Requested delivery date is not obtained for the PO Number " + PO_Number+"."]</InArgument>
                                </Assign.Value>
                              </Assign>
                              <Assign sap2010:WorkflowViewState.IdRef="Assign_46">
                                <Assign.To>
                                  <OutArgument x:TypeArguments="x:String">[Status]</OutArgument>
                                </Assign.To>
                                <Assign.Value>
                                  <InArgument x:TypeArguments="x:String">["FAILURE"]</InArgument>
                                </Assign.Value>
                              </Assign>
                              <ias:Append_Line ErrorCode="{x:Null}" ContinueOnError="True" DisplayName="Append Line" sap2010:WorkflowViewState.IdRef="Append_Line_1" Line="[commentStatus]" Source="[logfile]" />
                            </Sequence>
                          </If.Then>
                          <If.Else>
                            <Sequence DisplayName="PPS200_SUCCESS_Sequence" sap2010:WorkflowViewState.IdRef="Sequence_15">
                              <Assign DisplayName="Assign-1" sap2010:WorkflowViewState.IdRef="Assign_44">
                                <Assign.To>
                                  <OutArgument x:TypeArguments="x:String">[dwdt]</OutArgument>
                                </Assign.To>
                                <Assign.Value>
                                  <InArgument x:TypeArguments="x:String">[out1("results")(0)("records")(0)("DWDT").ToString]</InArgument>
                                </Assign.Value>
                              </Assign>
                              <Assign DisplayName="Assign-2" sap2010:WorkflowViewState.IdRef="Assign_5">
                                <Assign.To>
                                  <OutArgument x:TypeArguments="x:String">[ExpectedReceiptDateAPIOutput]</OutArgument>
                                </Assign.To>
                                <Assign.Value>
                                  <InArgument x:TypeArguments="x:String">[dwdt]</InArgument>
                                </Assign.Value>
                              </Assign>
                              <Assign DisplayName="Assign-3" sap2010:WorkflowViewState.IdRef="Assign_50">
                                <Assign.To>
                                  <OutArgument x:TypeArguments="s:DateTime">[newreqdate]</OutArgument>
                                </Assign.To>
                                <Assign.Value>
                                  <InArgument x:TypeArguments="s:DateTime">[DateTime.ParseExact(ExpectedReceiptDateAPIOutput, {"dd/MM/yyyy","yyyy-MM-dd","MM/dd/yyyy","yyyyMMdd"}, System.Globalization.CultureInfo.InvariantCulture, Globalization.DateTimeStyles.None)]</InArgument>
                                </Assign.Value>
                              </Assign>
                              <Assign DisplayName="Assign-4" sap2010:WorkflowViewState.IdRef="Assign_51">
                                <Assign.To>
                                  <OutArgument x:TypeArguments="s:DateTime">[newdatereq]</OutArgument>
                                </Assign.To>
                                <Assign.Value>
                                  <InArgument x:TypeArguments="s:DateTime">[newreqdate.AddDays(CInt(miscValues("ExpectedReceiptDateDays").ToString))]</InArgument>
                                </Assign.Value>
                              </Assign>
                              <Assign DisplayName="Assign-5" sap2010:WorkflowViewState.IdRef="Assign_52">
                                <Assign.To>
                                  <OutArgument x:TypeArguments="x:String">[Threshold]</OutArgument>
                                </Assign.To>
                                <Assign.Value>
                                  <InArgument x:TypeArguments="x:String">[newdatereq.ToString("yyyyMMdd")]</InArgument>
                                </Assign.Value>
                              </Assign>
                              <If Condition="[LastRunDate=Threshold]" DisplayName="If-threshold reached" sap2010:WorkflowViewState.IdRef="If_7">
                                <If.Then>
                                  <If Condition="[status=&quot;PONOTRECEIVED&quot; and NOT commentStatus.ToLower.Contains(&quot;alert&quot;)]" DisplayName="If-status_Comment" sap2010:WorkflowViewState.IdRef="If_6">
                                    <If.Then>
                                      <If Condition="[miscValues(&quot;Action&quot;).tostring = &quot;Notification&quot;]" DisplayName="If-notification" sap2010:WorkflowViewState.IdRef="If_17">
                                        <If.Then>
                                          <Sequence DisplayName="Expect_notification_Sequence" sap2010:WorkflowViewState.IdRef="Sequence_20">
                                            <ForEach x:TypeArguments="x:String" DisplayName="ForEach&lt;String&gt;" sap2010:WorkflowViewState.IdRef="ForEach`1_3" Values="[userIdentifierlist]">
                                              <ActivityAction x:TypeArguments="x:String">
                                                <ActivityAction.Argument>
                                                  <DelegateInArgument x:TypeArguments="x:String" Name="userIdentifier" />
                                                </ActivityAction.Argument>
                                                <iaw:InvokeWorkflow OutputArguments="{x:Null}" ResponseCode="{x:Null}" Arguments="[New Dictionary(Of String, Object) From {{&quot;status&quot;,&quot;ALERT &quot; + status},{&quot;statusComments&quot;,&quot;ALERT!! Please receive the goods.&quot;},{&quot;distributionType&quot;,distributionType},{&quot;userIdentifier&quot;,userIdentifier},{&quot;emailSubject&quot;,emailSubject},{&quot;emailReceivedTime&quot;,emailReceivedTime},{&quot;tenantID&quot;,tenantID},{&quot;invoiceNumber&quot;,DictOcrValues(&quot;INVOICE_RECEIPT_ID&quot;).ToString},{&quot;poNumber&quot;,DictOcrValues(&quot;PO_NUMBER&quot;).ToString},{&quot;subTotal&quot;,DictOcrValues(&quot;SUBTOTAL&quot;).ToString},{&quot;total&quot;,DictOcrValues(&quot;TOTAL&quot;).ToString},{&quot;drillbackLink&quot;,&quot;LogicalId=lid://infor.m3.m3&amp;program=APS450&amp;fieldNames=W2OBKV,&quot;+DictOcrValues(&quot;INVOICE_RECEIPT_ID&quot;).ToString+&quot;,W3OBKV,,W1OBKV,&quot;+ division+&quot;&amp;includeStartPanel=True&amp;source=MForms&amp;requirePanel=True&amp;sortingOrder=2&amp;view=STD02-01&amp;tableName=FAPIBH&amp;keys=E5CONO,&quot;+miscValues(&quot;company&quot;).ToString+&quot;,E5DIVI,&quot;+division+&quot;,E5INBN,+&amp;parameters=XXSINO,&quot;+DictOcrValues(&quot;INVOICE_RECEIPT_ID&quot;).ToString+&quot;&amp;startpanel=B&quot;},{&quot;invoiceDate&quot;,DictOcrValues(&quot;INVOICE_RECEIPT_DATE&quot;).ToString},{&quot;tax&quot;,DictOcrValues(&quot;VAT_AMOUNT&quot;).ToString},{&quot;charges&quot;,DictOcrValues(&quot;SHIPPING_AND_HANDLING_AMOUNT&quot;).ToString},{&quot;discount&quot;,DictOcrValues(&quot;Invoice_Level_Discount_AMOUNT&quot;).ToString},{&quot;deliveryNote&quot;,DictOcrValues(&quot;DELIVERY_NOTE_DATA&quot;).ToString} ,{&quot;miscValues&quot;,miscValues},{&quot;APResp&quot;,APResp}}]" ContinueOnError="True" DisplayName="Send Notification" sap2010:WorkflowViewState.IdRef="InvokeWorkflow_10" WorkflowFile="[projectPath+&quot;\SendNotification.xaml&quot;]" />
                                              </ActivityAction>
                                            </ForEach>
                                            <ias:Append_Line ErrorCode="{x:Null}" ContinueOnError="True" DisplayName="Append Line" sap2010:WorkflowViewState.IdRef="Append_Line_7" Line="Sent Notification after Duedate" Source="[logFile]" />
                                          </Sequence>
                                        </If.Then>
                                        <If.Else>
                                          <If Condition="[miscValues(&quot;Action&quot;).tostring = &quot;Registration&quot;]" DisplayName="If-reg" sap2010:WorkflowViewState.IdRef="If_16">
                                            <If.Then>
                                              <Assign sap2010:WorkflowViewState.IdRef="Assign_10">
                                                <Assign.To>
                                                  <OutArgument x:TypeArguments="x:Boolean">[ThresholdReached]</OutArgument>
                                                </Assign.To>
                                                <Assign.Value>
                                                  <InArgument x:TypeArguments="x:Boolean">True</InArgument>
                                                </Assign.Value>
                                              </Assign>
                                            </If.Then>
                                          </If>
                                        </If.Else>
                                      </If>
                                    </If.Then>
                                  </If>
                                </If.Then>
                              </If>
                            </Sequence>
                          </If.Else>
                        </If>
                      </Sequence>
                    </If.Then>
                    <If.Else>
                      <Sequence sap2010:WorkflowViewState.IdRef="Sequence_14">
                        <Assign sap2010:WorkflowViewState.IdRef="Assign_47">
                          <Assign.To>
                            <OutArgument x:TypeArguments="x:String">[commentStatus]</OutArgument>
                          </Assign.To>
                          <Assign.Value>
                            <InArgument x:TypeArguments="x:String">["Received an IONAPI error while fetching Requested delivery date for the PO Number " + PO_Number+"."]</InArgument>
                          </Assign.Value>
                        </Assign>
                        <Assign sap2010:WorkflowViewState.IdRef="Assign_48">
                          <Assign.To>
                            <OutArgument x:TypeArguments="x:String">[Status]</OutArgument>
                          </Assign.To>
                          <Assign.Value>
                            <InArgument x:TypeArguments="x:String">["FAILURE"]</InArgument>
                          </Assign.Value>
                        </Assign>
                        <ias:Append_Line ErrorCode="{x:Null}" ContinueOnError="True" DisplayName="Append Line" sap2010:WorkflowViewState.IdRef="Append_Line_2" Line="[commentStatus]" Source="[logfile]" />
                      </Sequence>
                    </If.Else>
                  </If>
                </Sequence>
              </If.Then>
            </If>
          </If.Else>
        </If>
      </If.Else>
    </If>
    <sads:DebugSymbol.Symbol>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</sads:DebugSymbol.Symbol>
  </Sequence>
  <sap2010:WorkflowViewState.ViewStateManager>
    <sap2010:ViewStateManager>
      <sap2010:ViewStateData Id="Assign_8" sap:VirtualizedContainerService.HintSize="3137,60" />
      <sap2010:ViewStateData Id="Assign_87" sap:VirtualizedContainerService.HintSize="3137,60" />
      <sap2010:ViewStateData Id="Assign_39" sap:VirtualizedContainerService.HintSize="3137,84">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsAnnotationDocked">True</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="Assign_41" sap:VirtualizedContainerService.HintSize="3137,60" />
      <sap2010:ViewStateData Id="Assign_101" sap:VirtualizedContainerService.HintSize="3137,60" />
      <sap2010:ViewStateData Id="Assign_88" sap:VirtualizedContainerService.HintSize="1092,60" />
      <sap2010:ViewStateData Id="Append_Line_3" sap:VirtualizedContainerService.HintSize="200,22" />
      <sap2010:ViewStateData Id="Assign_89" sap:VirtualizedContainerService.HintSize="242,60" />
      <sap2010:ViewStateData Id="Assign_91" sap:VirtualizedContainerService.HintSize="242,60" />
      <sap2010:ViewStateData Id="Sequence_17" sap:VirtualizedContainerService.HintSize="264,284">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">True</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="If_28" sap:VirtualizedContainerService.HintSize="1092,434" />
      <sap2010:ViewStateData Id="InvokeWorkflow_8" sap:VirtualizedContainerService.HintSize="200,22" />
      <sap2010:ViewStateData Id="ForEach`1_1" sap:VirtualizedContainerService.HintSize="287,209" />
      <sap2010:ViewStateData Id="Append_Line_4" sap:VirtualizedContainerService.HintSize="287,22" />
      <sap2010:ViewStateData Id="Sequence_18" sap:VirtualizedContainerService.HintSize="309,395">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">True</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="Assign_26" sap:VirtualizedContainerService.HintSize="242,60" />
      <sap2010:ViewStateData Id="If_23" sap:VirtualizedContainerService.HintSize="464,210" />
      <sap2010:ViewStateData Id="If_24" sap:VirtualizedContainerService.HintSize="798,545" />
      <sap2010:ViewStateData Id="Sequence_11" sap:VirtualizedContainerService.HintSize="820,669">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">True</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="If_25" sap:VirtualizedContainerService.HintSize="945,819">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">True</x:Boolean>
            <x:Boolean x:Key="IsPinned">False</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="Sequence_16" sap:VirtualizedContainerService.HintSize="967,943">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">True</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="If_26" sap:VirtualizedContainerService.HintSize="1092,1093">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">True</x:Boolean>
            <x:Boolean x:Key="IsPinned">False</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="Sequence_12" sap:VirtualizedContainerService.HintSize="1114,1791">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">True</x:Boolean>
            <x:Boolean x:Key="IsPinned">False</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="Sequence_2" sap:VirtualizedContainerService.HintSize="200,52">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">False</x:Boolean>
            <x:Boolean x:Key="IsPinned">False</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="Assign_3" sap:VirtualizedContainerService.HintSize="1048,60" />
      <sap2010:ViewStateData Id="Assign_35" sap:VirtualizedContainerService.HintSize="1048,60" />
      <sap2010:ViewStateData Id="Assign_37" sap:VirtualizedContainerService.HintSize="1048,60" />
      <sap2010:ViewStateData Id="Assign_4" sap:VirtualizedContainerService.HintSize="1048,60" />
      <sap2010:ViewStateData Id="InvokeWorkflow_9" sap:VirtualizedContainerService.HintSize="200,22" />
      <sap2010:ViewStateData Id="ForEach`1_2" sap:VirtualizedContainerService.HintSize="287,209" />
      <sap2010:ViewStateData Id="Append_Line_6" sap:VirtualizedContainerService.HintSize="287,22" />
      <sap2010:ViewStateData Id="Sequence_19" sap:VirtualizedContainerService.HintSize="309,395">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">True</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="Assign_9" sap:VirtualizedContainerService.HintSize="242,60" />
      <sap2010:ViewStateData Id="If_14" sap:VirtualizedContainerService.HintSize="464,210" />
      <sap2010:ViewStateData Id="If_15" sap:VirtualizedContainerService.HintSize="798,545" />
      <sap2010:ViewStateData Id="If_4" sap:VirtualizedContainerService.HintSize="923,695" />
      <sap2010:ViewStateData Id="If_5" sap:VirtualizedContainerService.HintSize="1048,845" />
      <sap2010:ViewStateData Id="Sequence_3" sap:VirtualizedContainerService.HintSize="1070,1369">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">True</x:Boolean>
            <x:Boolean x:Key="IsPinned">False</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="Assign_83" sap:VirtualizedContainerService.HintSize="1670,60" />
      <sap2010:ViewStateData Id="IONAPIRequestWizard_3" sap:VirtualizedContainerService.HintSize="1670,22" />
      <sap2010:ViewStateData Id="Assign_42" sap:VirtualizedContainerService.HintSize="1359,60" />
      <sap2010:ViewStateData Id="Assign_45" sap:VirtualizedContainerService.HintSize="242,60" />
      <sap2010:ViewStateData Id="Assign_46" sap:VirtualizedContainerService.HintSize="242,60" />
      <sap2010:ViewStateData Id="Append_Line_1" sap:VirtualizedContainerService.HintSize="242,22" />
      <sap2010:ViewStateData Id="Sequence_13" sap:VirtualizedContainerService.HintSize="264,346">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">True</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="Assign_44" sap:VirtualizedContainerService.HintSize="1048,60" />
      <sap2010:ViewStateData Id="Assign_5" sap:VirtualizedContainerService.HintSize="1048,60" />
      <sap2010:ViewStateData Id="Assign_50" sap:VirtualizedContainerService.HintSize="1048,60" />
      <sap2010:ViewStateData Id="Assign_51" sap:VirtualizedContainerService.HintSize="1048,60" />
      <sap2010:ViewStateData Id="Assign_52" sap:VirtualizedContainerService.HintSize="1048,60" />
      <sap2010:ViewStateData Id="InvokeWorkflow_10" sap:VirtualizedContainerService.HintSize="200,22" />
      <sap2010:ViewStateData Id="ForEach`1_3" sap:VirtualizedContainerService.HintSize="287,209" />
      <sap2010:ViewStateData Id="Append_Line_7" sap:VirtualizedContainerService.HintSize="287,22" />
      <sap2010:ViewStateData Id="Sequence_20" sap:VirtualizedContainerService.HintSize="309,395">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">True</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="Assign_10" sap:VirtualizedContainerService.HintSize="242,60" />
      <sap2010:ViewStateData Id="If_16" sap:VirtualizedContainerService.HintSize="464,210" />
      <sap2010:ViewStateData Id="If_17" sap:VirtualizedContainerService.HintSize="798,545" />
      <sap2010:ViewStateData Id="If_6" sap:VirtualizedContainerService.HintSize="923,695" />
      <sap2010:ViewStateData Id="If_7" sap:VirtualizedContainerService.HintSize="1048,845">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">True</x:Boolean>
            <x:Boolean x:Key="IsPinned">False</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="Sequence_15" sap:VirtualizedContainerService.HintSize="1070,1469">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">True</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="If_27" sap:VirtualizedContainerService.HintSize="1359,1619" />
      <sap2010:ViewStateData Id="Sequence_4" sap:VirtualizedContainerService.HintSize="1381,1843">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">True</x:Boolean>
            <x:Boolean x:Key="IsPinned">False</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="Assign_47" sap:VirtualizedContainerService.HintSize="242,60" />
      <sap2010:ViewStateData Id="Assign_48" sap:VirtualizedContainerService.HintSize="242,60" />
      <sap2010:ViewStateData Id="Append_Line_2" sap:VirtualizedContainerService.HintSize="242,22" />
      <sap2010:ViewStateData Id="Sequence_14" sap:VirtualizedContainerService.HintSize="264,346">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">True</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="If_8" sap:VirtualizedContainerService.HintSize="1670,1993" />
      <sap2010:ViewStateData Id="Sequence_5" sap:VirtualizedContainerService.HintSize="1692,2279">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">True</x:Boolean>
            <x:Boolean x:Key="IsPinned">False</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="If_9" sap:VirtualizedContainerService.HintSize="1817,2429" />
      <sap2010:ViewStateData Id="If_10" sap:VirtualizedContainerService.HintSize="2912,2579" />
      <sap2010:ViewStateData Id="If_11" sap:VirtualizedContainerService.HintSize="3137,2729" />
      <sap2010:ViewStateData Id="Sequence_6" sap:VirtualizedContainerService.HintSize="3159,3377">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">True</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="RehostedWorkflowDesigner.Workflow_1" sap:VirtualizedContainerService.HintSize="3199,3538" />
    </sap2010:ViewStateManager>
  </sap2010:WorkflowViewState.ViewStateManager>
</Activity>