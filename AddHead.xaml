﻿<Activity mc:Ignorable="sap sap2010 sads" x:Class="RehostedWorkflowDesigner.Workflow" this:Workflow.division="SK1" this:Workflow.tenantID="https://mingle-ionapi.eu1.inforcloudsuite.com/IKEAINDUSTRY_DEV/"
 xmlns="http://schemas.microsoft.com/netfx/2009/xaml/activities"
 xmlns:iai="clr-namespace:Infor.Activities.IONAPI;assembly=Infor.Activities.IONAPI"
 xmlns:ias="clr-namespace:Infor.Activities.Sys;assembly=Infor.Activities.Sys"
 xmlns:iru="clr-namespace:Infor.RPA.Utilities;assembly=Infor.RPA.Utilities"
 xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006"
 xmlns:mva="clr-namespace:Microsoft.VisualBasic.Activities;assembly=System.Activities"
 xmlns:njl="clr-namespace:Newtonsoft.Json.Linq;assembly=Newtonsoft.Json"
 xmlns:sads="http://schemas.microsoft.com/netfx/2010/xaml/activities/debugger"
 xmlns:sap="http://schemas.microsoft.com/netfx/2009/xaml/activities/presentation"
 xmlns:sap2010="http://schemas.microsoft.com/netfx/2010/xaml/activities/presentation"
 xmlns:scg="clr-namespace:System.Collections.Generic;assembly=mscorlib"
 xmlns:sco="clr-namespace:System.Collections.ObjectModel;assembly=mscorlib"
 xmlns:this="clr-namespace:RehostedWorkflowDesigner"
 xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml">
  <x:Members>
    <x:Property Name="logfile" Type="InArgument(x:String)" />
    <x:Property Name="SupplierNo" Type="InArgument(x:String)" />
    <x:Property Name="ivdate" Type="InArgument(x:String)" />
    <x:Property Name="division" Type="InArgument(x:String)" />
    <x:Property Name="sino" Type="InArgument(x:String)" />
    <x:Property Name="cucd" Type="InArgument(x:String)" />
    <x:Property Name="tepy" Type="InArgument(x:String)" />
    <x:Property Name="pyme" Type="InArgument(x:String)" />
    <x:Property Name="cuam" Type="InArgument(x:String)" />
    <x:Property Name="inbnValue" Type="OutArgument(x:String)" />
    <x:Property Name="bkid" Type="InArgument(x:String)" />
    <x:Property Name="Status" Type="OutArgument(x:String)" />
    <x:Property Name="commentStatus" Type="OutArgument(x:String)" />
    <x:Property Name="tenantID" Type="InArgument(x:String)" />
    <x:Property Name="authUser" Type="InArgument(x:String)" />
    <x:Property Name="correlationID" Type="InArgument(x:String)" />
    <x:Property Name="discountTerms" Type="InArgument(x:String)" />
    <x:Property Name="miscValues" Type="InArgument(scg:Dictionary(x:String, x:Object))" />
    <x:Property Name="txap" Type="InArgument(x:String)" />
    <x:Property Name="DictOcrValues" Type="InArgument(scg:Dictionary(x:String, x:Object))" />
    <x:Property Name="duedate" Type="InArgument(x:String)" />
    <x:Property Name="geoc" Type="InArgument(x:String)" />
  </x:Members>
  <mva:VisualBasic.Settings>
    <x:Null />
  </mva:VisualBasic.Settings>
  <sap2010:WorkflowViewState.IdRef>RehostedWorkflowDesigner.Workflow_1</sap2010:WorkflowViewState.IdRef>
  <TextExpression.NamespacesForImplementation>
    <sco:Collection x:TypeArguments="x:String">
      <x:String>System.Activities</x:String>
      <x:String>System.Activities.Statements</x:String>
      <x:String>System.Activities.Expressions</x:String>
      <x:String>System.Activities.Validation</x:String>
      <x:String>System.Activities.XamlIntegration</x:String>
      <x:String>System.Windows.Controls</x:String>
      <x:String>System.Windows.Documents</x:String>
      <x:String>System.Windows.Shapes</x:String>
      <x:String>System.Windows.Shell</x:String>
      <x:String>System.Windows.Navigation</x:String>
      <x:String>System.Windows.Data</x:String>
      <x:String>System.Windows</x:String>
      <x:String>System.Windows.Controls.Primitives</x:String>
      <x:String>System.Windows.Media.Animation</x:String>
      <x:String>System.Windows.Input</x:String>
      <x:String>System.Windows.Media</x:String>
      <x:String>System.Diagnostics</x:String>
      <x:String>System.Windows.Automation</x:String>
      <x:String>System.Windows.Media.TextFormatting</x:String>
      <x:String>System.Windows.Ink</x:String>
      <x:String>System.Windows.Media.Effects</x:String>
      <x:String>System.Windows.Media.Imaging</x:String>
      <x:String>System.Windows.Media.Media3D</x:String>
      <x:String>System.Windows.Controls.Ribbon</x:String>
      <x:String>System.Windows.Forms.Integration</x:String>
      <x:String>Infor.Activities.Email</x:String>
      <x:String>Infor.Activities.IONAPI</x:String>
      <x:String>Infor.Activities.Sys</x:String>
      <x:String>Infor.RPA.Utilities</x:String>
      <x:String>Microsoft.VisualBasic.Activities</x:String>
      <x:String>Newtonsoft.Json</x:String>
      <x:String>Newtonsoft.Json.Linq</x:String>
      <x:String>System</x:String>
      <x:String>System.Activities.Debugger.Symbol</x:String>
      <x:String>System.Collections.Generic</x:String>
      <x:String>System.Windows.Markup</x:String>
      <x:String>Microsoft.VisualBasic.CompilerServices</x:String>
    </sco:Collection>
  </TextExpression.NamespacesForImplementation>
  <TextExpression.ReferencesForImplementation>
    <sco:Collection x:TypeArguments="AssemblyReference">
      <AssemblyReference>System.Activities</AssemblyReference>
      <AssemblyReference>PresentationFramework</AssemblyReference>
      <AssemblyReference>WindowsBase</AssemblyReference>
      <AssemblyReference>PresentationCore</AssemblyReference>
      <AssemblyReference>System.Windows.Controls.Ribbon</AssemblyReference>
      <AssemblyReference>WindowsFormsIntegration</AssemblyReference>
      <AssemblyReference>Infor.Activities.Email</AssemblyReference>
      <AssemblyReference>Infor.Activities.IONAPI</AssemblyReference>
      <AssemblyReference>Infor.Activities.Sys</AssemblyReference>
      <AssemblyReference>Infor.RPA.Utilities</AssemblyReference>
      <AssemblyReference>Newtonsoft.Json</AssemblyReference>
      <AssemblyReference>System.ServiceModel</AssemblyReference>
      <AssemblyReference>System</AssemblyReference>
      <AssemblyReference>System.Core</AssemblyReference>
      <AssemblyReference>mscorlib</AssemblyReference>
      <AssemblyReference>System.Runtime.WindowsRuntime</AssemblyReference>
      <AssemblyReference>Microsoft.Bcl.AsyncInterfaces</AssemblyReference>
      <AssemblyReference>System.Xaml</AssemblyReference>
      <AssemblyReference>System.ComponentModel.Composition</AssemblyReference>
      <AssemblyReference>System.Memory</AssemblyReference>
      <AssemblyReference>Microsoft.VisualBasic</AssemblyReference>
    </sco:Collection>
  </TextExpression.ReferencesForImplementation>
  <Sequence DisplayName="AddHead_Sequence_1" sap2010:WorkflowViewState.IdRef="Sequence_5">
    <Sequence.Variables>
      <Variable x:TypeArguments="iru:ResponseObject" Name="respObj5" />
      <Variable x:TypeArguments="x:Int32" Name="StatusCode5" />
      <Variable x:TypeArguments="njl:JToken" Name="out5" />
      <Variable x:TypeArguments="x:Boolean" Name="InvoiceAlreadyExists" />
      <Variable x:TypeArguments="x:String" Name="strPain" />
      <Variable x:TypeArguments="x:String" Name="company" />
      <Variable x:TypeArguments="x:Int32" Name="StatusCode3" />
      <Variable x:TypeArguments="iru:ResponseObject" Name="respObj3" />
      <Variable x:TypeArguments="x:Boolean" Default="False" Name="Exp" />
    </Sequence.Variables>
    <Assign sap2010:WorkflowViewState.IdRef="Assign_12">
      <Assign.To>
        <OutArgument x:TypeArguments="x:String">[strPain]</OutArgument>
      </Assign.To>
      <Assign.Value>
        <InArgument x:TypeArguments="x:String">[DictOcrValues("REFERENCE").Tostring]</InArgument>
      </Assign.Value>
    </Assign>
    <If Condition="[miscValues(&quot;customerduedate&quot;).ToString.ToLower = &quot;true&quot; and duedate &lt;&gt; &quot;&quot;]" sap2010:WorkflowViewState.IdRef="If_15">
      <If.Then>
        <Assign sap2010:WorkflowViewState.IdRef="Assign_13">
          <Assign.To>
            <OutArgument x:TypeArguments="x:String">[duedate]</OutArgument>
          </Assign.To>
          <Assign.Value>
            <InArgument x:TypeArguments="x:String">[CType(duedate, Date).ToString("yyyyMMdd")]</InArgument>
          </Assign.Value>
        </Assign>
      </If.Then>
      <If.Else>
        <Assign sap2010:WorkflowViewState.IdRef="Assign_14">
          <Assign.To>
            <OutArgument x:TypeArguments="x:String">[duedate]</OutArgument>
          </Assign.To>
          <Assign.Value>
            <InArgument x:TypeArguments="x:String">
              <Literal x:TypeArguments="x:String" Value="" />
            </InArgument>
          </Assign.Value>
        </Assign>
      </If.Else>
    </If>
    <Assign sap2010:WorkflowViewState.IdRef="Assign_15">
      <Assign.To>
        <OutArgument x:TypeArguments="x:String">[company]</OutArgument>
      </Assign.To>
      <Assign.Value>
        <InArgument x:TypeArguments="x:String">[miscValues("company").ToString]</InArgument>
      </Assign.Value>
    </Assign>
    <Assign sap2010:WorkflowViewState.IdRef="Assign_18">
      <Assign.To>
        <OutArgument x:TypeArguments="x:Boolean">[Exp]</OutArgument>
      </Assign.To>
      <Assign.Value>
        <InArgument x:TypeArguments="x:Boolean">False</InArgument>
      </Assign.Value>
    </Assign>
    <If Condition="[miscValues(&quot;mandateGEOC&quot;).ToString.ToLower = &quot;true&quot;]" sap2010:WorkflowViewState.IdRef="If_16">
      <If.Then>
        <If Condition="[geoc = &quot;&quot; or geoc = &quot;0&quot; or miscValues(&quot;geoc&quot;).ToString = &quot;&quot;]" sap2010:WorkflowViewState.IdRef="If_17">
          <If.Then>
            <Sequence sap2010:WorkflowViewState.IdRef="Sequence_14">
              <iai:IONAPIRequestWizard FileAttachments="{x:Null}" PostData="{x:Null}" ResponseCode="{x:Null}" ContentType="application_json" ContinueOnError="True" DisplayName="MNS100MI - IONAPI Request for vendor name" HttpMethod="GET" sap2010:WorkflowViewState.IdRef="IONAPIRequestWizard_15" Response="[respObj3]" StatusCode="[StatusCode3]" Url="[TenantID + &quot;M3/m3api-rest/v2/execute/MNS100MI/GetBasicData&quot;]">
                <iai:IONAPIRequestWizard.Headers>
                  <scg:List x:TypeArguments="scg:List(x:String)" Capacity="4">
                    <scg:List x:TypeArguments="x:String" Capacity="4">
                      <x:String>Accept</x:String>
                    </scg:List>
                    <scg:List x:TypeArguments="x:String" Capacity="4">
                      <x:String>application/json</x:String>
                    </scg:List>
                  </scg:List>
                </iai:IONAPIRequestWizard.Headers>
                <iai:IONAPIRequestWizard.QueryParameters>
                  <scg:List x:TypeArguments="scg:List(x:String)" Capacity="4">
                    <scg:List x:TypeArguments="x:String" Capacity="8">
                      <x:String>extendedresult</x:String>
                      <x:String>format</x:String>
                      <x:String>righttrim</x:String>
                      <x:String>excludeempty</x:String>
                      <x:String>dateformat</x:String>
                      <x:String>DIVI</x:String>
                      <x:String>CONO</x:String>
                    </scg:List>
                    <scg:List x:TypeArguments="x:String" Capacity="8">
                      <x:String>false</x:String>
                      <x:String>PRETTY</x:String>
                      <x:String>true</x:String>
                      <x:String>false</x:String>
                      <x:String>YMD8</x:String>
                      <x:String>division</x:String>
                      <x:String>company</x:String>
                    </scg:List>
                  </scg:List>
                </iai:IONAPIRequestWizard.QueryParameters>
              </iai:IONAPIRequestWizard>
              <If Condition="[StatusCode3 = 200]" sap2010:WorkflowViewState.IdRef="If_18">
                <If.Then>
                  <If Condition="[respObj3.ReadAsJson(&quot;results&quot;)(0)(&quot;records&quot;).ToString = &quot;[]&quot;]" sap2010:WorkflowViewState.IdRef="If_19">
                    <If.Then>
                      <Sequence sap2010:WorkflowViewState.IdRef="Sequence_16">
                        <Assign DisplayName="AddHead_Assign_24_inbnValue" sap2010:WorkflowViewState.IdRef="Assign_20">
                          <Assign.To>
                            <OutArgument x:TypeArguments="x:String">[commentStatus]</OutArgument>
                          </Assign.To>
                          <Assign.Value>
                            <InArgument x:TypeArguments="x:String">[(respObj3.ReadAsJson("results")(0)("errorMessage")).ToString]</InArgument>
                          </Assign.Value>
                        </Assign>
                        <Assign DisplayName="AddHead_Assign_29_Status" sap2010:WorkflowViewState.IdRef="Assign_21">
                          <Assign.To>
                            <OutArgument x:TypeArguments="x:String">[Status]</OutArgument>
                          </Assign.To>
                          <Assign.Value>
                            <InArgument x:TypeArguments="x:String">NEEDSVERIFICATION</InArgument>
                          </Assign.Value>
                        </Assign>
                        <ias:Append_Line ErrorCode="{x:Null}" ContinueOnError="True" DisplayName="AddHead_Append_Line_30" sap2010:WorkflowViewState.IdRef="Append_Line_3" Line="[commentStatus]" Source="[logfile]" />
                        <Assign sap2010:WorkflowViewState.IdRef="Assign_22">
                          <Assign.To>
                            <OutArgument x:TypeArguments="x:Boolean">[Exp]</OutArgument>
                          </Assign.To>
                          <Assign.Value>
                            <InArgument x:TypeArguments="x:Boolean">True</InArgument>
                          </Assign.Value>
                        </Assign>
                      </Sequence>
                    </If.Then>
                    <If.Else>
                      <Sequence sap2010:WorkflowViewState.IdRef="Sequence_15">
                        <If Condition="[respObj3.ReadAsJson(&quot;results&quot;)(0)(&quot;records&quot;)(0)(&quot;TATM&quot;).ToString = &quot;2&quot; OR respObj3.ReadAsJson(&quot;results&quot;)(0)(&quot;records&quot;)(0)(&quot;TATM&quot;).ToString = &quot;3&quot;]" sap2010:WorkflowViewState.IdRef="If_20">
                          <If.Then>
                            <Assign sap2010:WorkflowViewState.IdRef="Assign_17">
                              <Assign.To>
                                <OutArgument x:TypeArguments="x:String">[geoc]</OutArgument>
                              </Assign.To>
                              <Assign.Value>
                                <InArgument x:TypeArguments="x:String">[respObj3.ReadAsJson("results")(0)("records")(0)("GEOC").ToString]</InArgument>
                              </Assign.Value>
                            </Assign>
                          </If.Then>
                        </If>
                        <If Condition="[geoc = &quot;&quot; OR geoc = &quot;0&quot; OR NOT (respObj3.ReadAsJson(&quot;results&quot;)(0)(&quot;records&quot;)(0)(&quot;TATM&quot;).ToString = &quot;2&quot; OR respObj3.ReadAsJson(&quot;results&quot;)(0)(&quot;records&quot;)(0)(&quot;TATM&quot;).ToString = &quot;3&quot;)]" sap2010:WorkflowViewState.IdRef="If_21">
                          <If.Then>
                            <Assign sap2010:WorkflowViewState.IdRef="Assign_19">
                              <Assign.To>
                                <OutArgument x:TypeArguments="x:Boolean">[Exp]</OutArgument>
                              </Assign.To>
                              <Assign.Value>
                                <InArgument x:TypeArguments="x:Boolean">True</InArgument>
                              </Assign.Value>
                            </Assign>
                          </If.Then>
                        </If>
                      </Sequence>
                    </If.Else>
                  </If>
                </If.Then>
              </If>
            </Sequence>
          </If.Then>
        </If>
      </If.Then>
    </If>
    <If Condition="[NOT Exp]" sap2010:WorkflowViewState.IdRef="If_22">
      <If.Then>
        <Sequence sap2010:WorkflowViewState.IdRef="Sequence_17">
          <If Condition="[not String.IsNullOrWhiteSpace(duedate)]" sap2010:WorkflowViewState.IdRef="If_14">
            <If.Then>
              <Sequence sap2010:WorkflowViewState.IdRef="Sequence_10">
                <If Condition="[miscValues(&quot;handleCashDiscount&quot;).ToString.tolower = &quot;true&quot;]" DisplayName="AddHead_If_2" sap2010:WorkflowViewState.IdRef="If_10">
                  <If.Then>
                    <Sequence DisplayName="AddHead_Sequence_3" sap2010:WorkflowViewState.IdRef="Sequence_8">
                      <iai:IONAPIRequestWizard FileAttachments="{x:Null}" PostData="{x:Null}" ResponseCode="{x:Null}" ContentType="application_json" ContinueOnError="True" DisplayName="AddHead_IONAPIRequestWizard_4" HttpMethod="GET" sap2010:WorkflowViewState.IdRef="IONAPIRequestWizard_7" Response="[respObj5]" StatusCode="[StatusCode5]" Url="[TenantID + &quot;M3/m3api-rest/v2/execute/APS450MI/AddHead&quot;]">
                        <iai:IONAPIRequestWizard.Headers>
                          <scg:List x:TypeArguments="scg:List(x:String)" Capacity="4">
                            <scg:List x:TypeArguments="x:String" Capacity="4">
                              <x:String>Accept</x:String>
                            </scg:List>
                            <scg:List x:TypeArguments="x:String" Capacity="4">
                              <x:String>application/json</x:String>
                            </scg:List>
                          </scg:List>
                        </iai:IONAPIRequestWizard.Headers>
                        <iai:IONAPIRequestWizard.QueryParameters>
                          <scg:List x:TypeArguments="scg:List(x:String)" Capacity="4">
                            <scg:List x:TypeArguments="x:String" Capacity="32">
                              <x:String>SUNO</x:String>
                              <x:String>IVDT</x:String>
                              <x:String>DIVI</x:String>
                              <x:String>SINO</x:String>
                              <x:String>CUCD</x:String>
                              <x:String>TEPY</x:String>
                              <x:String>PYME</x:String>
                              <x:String>CUAM</x:String>
                              <x:String>IMCD</x:String>
                              <x:String>CRTP</x:String>
                              <x:String>dateformat</x:String>
                              <x:String>excludeempty</x:String>
                              <x:String>righttrim</x:String>
                              <x:String>format</x:String>
                              <x:String>extendedresult</x:String>
                              <x:String>CORI</x:String>
                              <x:String>TECD</x:String>
                              <x:String>TXAP</x:String>
                              <x:String>PAIN</x:String>
                              <x:String>DUDT</x:String>
                              <x:String>DEDA</x:String>
                            </scg:List>
                            <scg:List x:TypeArguments="x:String" Capacity="32">
                              <x:String>SupplierNo</x:String>
                              <x:String>ivdate</x:String>
                              <x:String>division</x:String>
                              <x:String>sino</x:String>
                              <x:String>cucd</x:String>
                              <x:String>tepy</x:String>
                              <x:String>pyme</x:String>
                              <x:String>cuam</x:String>
                              <x:String>1</x:String>
                              <x:String>1</x:String>
                              <x:String>YMD8</x:String>
                              <x:String>false</x:String>
                              <x:String>true</x:String>
                              <x:String>PRETTY</x:String>
                              <x:String>false</x:String>
                              <x:String>correlationID</x:String>
                              <x:String>discountTerms</x:String>
                              <x:String>txap</x:String>
                              <x:String>strPain</x:String>
                              <x:String>duedate</x:String>
                              <x:String>ivdate</x:String>
                            </scg:List>
                          </scg:List>
                        </iai:IONAPIRequestWizard.QueryParameters>
                      </iai:IONAPIRequestWizard>
                      <If Condition="[StatusCode5 = 200  and respObj5.ReadAsText.Contains(&quot;Bank account identity must be entered&quot;)]" DisplayName="AddHead_If_5" sap2010:WorkflowViewState.IdRef="If_8">
                        <If.Then>
                          <iai:IONAPIRequestWizard FileAttachments="{x:Null}" PostData="{x:Null}" ResponseCode="{x:Null}" ContentType="application_json" ContinueOnError="True" DisplayName="AddHead_IONAPIRequestWizard_6" HttpMethod="GET" sap2010:WorkflowViewState.IdRef="IONAPIRequestWizard_8" Response="[respObj5]" StatusCode="[StatusCode5]" Url="[TenantID + &quot;M3/m3api-rest/v2/execute/APS450MI/AddHead&quot;]">
                            <iai:IONAPIRequestWizard.Headers>
                              <scg:List x:TypeArguments="scg:List(x:String)" Capacity="4">
                                <scg:List x:TypeArguments="x:String" Capacity="4">
                                  <x:String>Accept</x:String>
                                </scg:List>
                                <scg:List x:TypeArguments="x:String" Capacity="4">
                                  <x:String>application/json</x:String>
                                </scg:List>
                              </scg:List>
                            </iai:IONAPIRequestWizard.Headers>
                            <iai:IONAPIRequestWizard.QueryParameters>
                              <scg:List x:TypeArguments="scg:List(x:String)" Capacity="4">
                                <scg:List x:TypeArguments="x:String" Capacity="32">
                                  <x:String>SUNO</x:String>
                                  <x:String>IVDT</x:String>
                                  <x:String>DIVI</x:String>
                                  <x:String>SINO</x:String>
                                  <x:String>CUCD</x:String>
                                  <x:String>TEPY</x:String>
                                  <x:String>PYME</x:String>
                                  <x:String>CUAM</x:String>
                                  <x:String>IMCD</x:String>
                                  <x:String>CRTP</x:String>
                                  <x:String>dateformat</x:String>
                                  <x:String>excludeempty</x:String>
                                  <x:String>righttrim</x:String>
                                  <x:String>format</x:String>
                                  <x:String>extendedresult</x:String>
                                  <x:String>BKID</x:String>
                                  <x:String>CORI</x:String>
                                  <x:String>TECD</x:String>
                                  <x:String>TXAP</x:String>
                                  <x:String>PAIN</x:String>
                                  <x:String>DUDT</x:String>
                                  <x:String>DEDA</x:String>
                                </scg:List>
                                <scg:List x:TypeArguments="x:String" Capacity="32">
                                  <x:String>SupplierNo</x:String>
                                  <x:String>ivdate</x:String>
                                  <x:String>division</x:String>
                                  <x:String>sino</x:String>
                                  <x:String>cucd</x:String>
                                  <x:String>tepy</x:String>
                                  <x:String>pyme</x:String>
                                  <x:String>cuam</x:String>
                                  <x:String>1</x:String>
                                  <x:String>1</x:String>
                                  <x:String>YMD8</x:String>
                                  <x:String>false</x:String>
                                  <x:String>true</x:String>
                                  <x:String>PRETTY</x:String>
                                  <x:String>false</x:String>
                                  <x:String>bkid</x:String>
                                  <x:String>correlationID</x:String>
                                  <x:String>discountTerms</x:String>
                                  <x:String>txap</x:String>
                                  <x:String>strPain</x:String>
                                  <x:String>duedate</x:String>
                                  <x:String>ivdate</x:String>
                                </scg:List>
                              </scg:List>
                            </iai:IONAPIRequestWizard.QueryParameters>
                          </iai:IONAPIRequestWizard>
                        </If.Then>
                      </If>
                    </Sequence>
                  </If.Then>
                  <If.Else>
                    <Sequence DisplayName="AddHead_Sequence_7" sap2010:WorkflowViewState.IdRef="Sequence_9">
                      <iai:IONAPIRequestWizard FileAttachments="{x:Null}" PostData="{x:Null}" ResponseCode="{x:Null}" ContentType="application_json" ContinueOnError="True" DisplayName="AddHead_IONAPIRequestWizard_8" HttpMethod="GET" sap2010:WorkflowViewState.IdRef="IONAPIRequestWizard_9" Response="[respObj5]" StatusCode="[StatusCode5]" Url="[TenantID + &quot;M3/m3api-rest/v2/execute/APS450MI/AddHead&quot;]">
                        <iai:IONAPIRequestWizard.Headers>
                          <scg:List x:TypeArguments="scg:List(x:String)" Capacity="4">
                            <scg:List x:TypeArguments="x:String" Capacity="4">
                              <x:String>Accept</x:String>
                            </scg:List>
                            <scg:List x:TypeArguments="x:String" Capacity="4">
                              <x:String>application/json</x:String>
                            </scg:List>
                          </scg:List>
                        </iai:IONAPIRequestWizard.Headers>
                        <iai:IONAPIRequestWizard.QueryParameters>
                          <scg:List x:TypeArguments="scg:List(x:String)" Capacity="4">
                            <scg:List x:TypeArguments="x:String" Capacity="32">
                              <x:String>SUNO</x:String>
                              <x:String>IVDT</x:String>
                              <x:String>DIVI</x:String>
                              <x:String>SINO</x:String>
                              <x:String>CUCD</x:String>
                              <x:String>TEPY</x:String>
                              <x:String>PYME</x:String>
                              <x:String>CUAM</x:String>
                              <x:String>IMCD</x:String>
                              <x:String>CRTP</x:String>
                              <x:String>dateformat</x:String>
                              <x:String>excludeempty</x:String>
                              <x:String>righttrim</x:String>
                              <x:String>format</x:String>
                              <x:String>extendedresult</x:String>
                              <x:String>CORI</x:String>
                              <x:String>TXAP</x:String>
                              <x:String>PAIN</x:String>
                              <x:String>DUDT</x:String>
                              <x:String>DEDA</x:String>
                            </scg:List>
                            <scg:List x:TypeArguments="x:String" Capacity="32">
                              <x:String>SupplierNo</x:String>
                              <x:String>ivdate</x:String>
                              <x:String>division</x:String>
                              <x:String>sino</x:String>
                              <x:String>cucd</x:String>
                              <x:String>tepy</x:String>
                              <x:String>pyme</x:String>
                              <x:String>cuam</x:String>
                              <x:String>1</x:String>
                              <x:String>1</x:String>
                              <x:String>YMD8</x:String>
                              <x:String>false</x:String>
                              <x:String>true</x:String>
                              <x:String>PRETTY</x:String>
                              <x:String>false</x:String>
                              <x:String>correlationID</x:String>
                              <x:String>txap</x:String>
                              <x:String>strPain</x:String>
                              <x:String>duedate</x:String>
                              <x:String>ivdate</x:String>
                            </scg:List>
                          </scg:List>
                        </iai:IONAPIRequestWizard.QueryParameters>
                      </iai:IONAPIRequestWizard>
                      <If Condition="[StatusCode5 = 200  and respObj5.ReadAsText.Contains(&quot;Bank account identity must be entered&quot;)]" DisplayName="AddHead_If_9" sap2010:WorkflowViewState.IdRef="If_9">
                        <If.Then>
                          <iai:IONAPIRequestWizard FileAttachments="{x:Null}" PostData="{x:Null}" ResponseCode="{x:Null}" ContentType="application_json" ContinueOnError="True" DisplayName="AddHead_IONAPIRequestWizard_10" HttpMethod="GET" sap2010:WorkflowViewState.IdRef="IONAPIRequestWizard_10" Response="[respObj5]" StatusCode="[StatusCode5]" Url="[TenantID + &quot;M3/m3api-rest/v2/execute/APS450MI/AddHead&quot;]">
                            <iai:IONAPIRequestWizard.Headers>
                              <scg:List x:TypeArguments="scg:List(x:String)" Capacity="4">
                                <scg:List x:TypeArguments="x:String" Capacity="4">
                                  <x:String>Accept</x:String>
                                </scg:List>
                                <scg:List x:TypeArguments="x:String" Capacity="4">
                                  <x:String>application/json</x:String>
                                </scg:List>
                              </scg:List>
                            </iai:IONAPIRequestWizard.Headers>
                            <iai:IONAPIRequestWizard.QueryParameters>
                              <scg:List x:TypeArguments="scg:List(x:String)" Capacity="4">
                                <scg:List x:TypeArguments="x:String" Capacity="32">
                                  <x:String>SUNO</x:String>
                                  <x:String>IVDT</x:String>
                                  <x:String>DIVI</x:String>
                                  <x:String>SINO</x:String>
                                  <x:String>CUCD</x:String>
                                  <x:String>TEPY</x:String>
                                  <x:String>PYME</x:String>
                                  <x:String>CUAM</x:String>
                                  <x:String>IMCD</x:String>
                                  <x:String>CRTP</x:String>
                                  <x:String>dateformat</x:String>
                                  <x:String>excludeempty</x:String>
                                  <x:String>righttrim</x:String>
                                  <x:String>format</x:String>
                                  <x:String>extendedresult</x:String>
                                  <x:String>BKID</x:String>
                                  <x:String>CORI</x:String>
                                  <x:String>TXAP</x:String>
                                  <x:String>PAIN</x:String>
                                  <x:String>DUDT</x:String>
                                  <x:String>DEDA</x:String>
                                </scg:List>
                                <scg:List x:TypeArguments="x:String" Capacity="32">
                                  <x:String>SupplierNo</x:String>
                                  <x:String>ivdate</x:String>
                                  <x:String>division</x:String>
                                  <x:String>sino</x:String>
                                  <x:String>cucd</x:String>
                                  <x:String>tepy</x:String>
                                  <x:String>pyme</x:String>
                                  <x:String>cuam</x:String>
                                  <x:String>1</x:String>
                                  <x:String>1</x:String>
                                  <x:String>YMD8</x:String>
                                  <x:String>false</x:String>
                                  <x:String>true</x:String>
                                  <x:String>PRETTY</x:String>
                                  <x:String>false</x:String>
                                  <x:String>bkid</x:String>
                                  <x:String>correlationID</x:String>
                                  <x:String>txap</x:String>
                                  <x:String>strPain</x:String>
                                  <x:String>duedate</x:String>
                                  <x:String>ivdate</x:String>
                                </scg:List>
                              </scg:List>
                            </iai:IONAPIRequestWizard.QueryParameters>
                          </iai:IONAPIRequestWizard>
                        </If.Then>
                      </If>
                    </Sequence>
                  </If.Else>
                </If>
              </Sequence>
            </If.Then>
            <If.Else>
              <Sequence sap2010:WorkflowViewState.IdRef="Sequence_13">
                <If Condition="[miscValues(&quot;handleCashDiscount&quot;).ToString.tolower = &quot;true&quot;]" DisplayName="AddHead_If_2" sap2010:WorkflowViewState.IdRef="If_13">
                  <If.Then>
                    <Sequence DisplayName="AddHead_Sequence_3" sap2010:WorkflowViewState.IdRef="Sequence_11">
                      <iai:IONAPIRequestWizard FileAttachments="{x:Null}" PostData="{x:Null}" ResponseCode="{x:Null}" ContentType="application_json" ContinueOnError="True" DisplayName="AddHead_IONAPIRequestWizard_4" HttpMethod="GET" sap2010:WorkflowViewState.IdRef="IONAPIRequestWizard_11" Response="[respObj5]" StatusCode="[StatusCode5]" Url="[TenantID + &quot;M3/m3api-rest/v2/execute/APS450MI/AddHead&quot;]">
                        <iai:IONAPIRequestWizard.Headers>
                          <scg:List x:TypeArguments="scg:List(x:String)" Capacity="4">
                            <scg:List x:TypeArguments="x:String" Capacity="4">
                              <x:String>Accept</x:String>
                            </scg:List>
                            <scg:List x:TypeArguments="x:String" Capacity="4">
                              <x:String>application/json</x:String>
                            </scg:List>
                          </scg:List>
                        </iai:IONAPIRequestWizard.Headers>
                        <iai:IONAPIRequestWizard.QueryParameters>
                          <scg:List x:TypeArguments="scg:List(x:String)" Capacity="4">
                            <scg:List x:TypeArguments="x:String" Capacity="32">
                              <x:String>SUNO</x:String>
                              <x:String>IVDT</x:String>
                              <x:String>DIVI</x:String>
                              <x:String>SINO</x:String>
                              <x:String>CUCD</x:String>
                              <x:String>TEPY</x:String>
                              <x:String>PYME</x:String>
                              <x:String>CUAM</x:String>
                              <x:String>IMCD</x:String>
                              <x:String>CRTP</x:String>
                              <x:String>dateformat</x:String>
                              <x:String>excludeempty</x:String>
                              <x:String>righttrim</x:String>
                              <x:String>format</x:String>
                              <x:String>extendedresult</x:String>
                              <x:String>CORI</x:String>
                              <x:String>TECD</x:String>
                              <x:String>TXAP</x:String>
                              <x:String>PAIN</x:String>
                              <x:String>DEDA</x:String>
                            </scg:List>
                            <scg:List x:TypeArguments="x:String" Capacity="32">
                              <x:String>SupplierNo</x:String>
                              <x:String>ivdate</x:String>
                              <x:String>division</x:String>
                              <x:String>sino</x:String>
                              <x:String>cucd</x:String>
                              <x:String>tepy</x:String>
                              <x:String>pyme</x:String>
                              <x:String>cuam</x:String>
                              <x:String>1</x:String>
                              <x:String>1</x:String>
                              <x:String>YMD8</x:String>
                              <x:String>false</x:String>
                              <x:String>true</x:String>
                              <x:String>PRETTY</x:String>
                              <x:String>false</x:String>
                              <x:String>correlationID</x:String>
                              <x:String>discountTerms</x:String>
                              <x:String>txap</x:String>
                              <x:String>strPain</x:String>
                              <x:String>ivdate</x:String>
                            </scg:List>
                          </scg:List>
                        </iai:IONAPIRequestWizard.QueryParameters>
                      </iai:IONAPIRequestWizard>
                      <If Condition="[StatusCode5 = 200  and respObj5.ReadAsText.Contains(&quot;Bank account identity must be entered&quot;)]" DisplayName="AddHead_If_5" sap2010:WorkflowViewState.IdRef="If_11">
                        <If.Then>
                          <iai:IONAPIRequestWizard FileAttachments="{x:Null}" PostData="{x:Null}" ResponseCode="{x:Null}" ContentType="application_json" ContinueOnError="True" DisplayName="AddHead_IONAPIRequestWizard_6" HttpMethod="GET" sap2010:WorkflowViewState.IdRef="IONAPIRequestWizard_12" Response="[respObj5]" StatusCode="[StatusCode5]" Url="[TenantID + &quot;M3/m3api-rest/v2/execute/APS450MI/AddHead&quot;]">
                            <iai:IONAPIRequestWizard.Headers>
                              <scg:List x:TypeArguments="scg:List(x:String)" Capacity="4">
                                <scg:List x:TypeArguments="x:String" Capacity="4">
                                  <x:String>Accept</x:String>
                                </scg:List>
                                <scg:List x:TypeArguments="x:String" Capacity="4">
                                  <x:String>application/json</x:String>
                                </scg:List>
                              </scg:List>
                            </iai:IONAPIRequestWizard.Headers>
                            <iai:IONAPIRequestWizard.QueryParameters>
                              <scg:List x:TypeArguments="scg:List(x:String)" Capacity="4">
                                <scg:List x:TypeArguments="x:String" Capacity="32">
                                  <x:String>SUNO</x:String>
                                  <x:String>IVDT</x:String>
                                  <x:String>DIVI</x:String>
                                  <x:String>SINO</x:String>
                                  <x:String>CUCD</x:String>
                                  <x:String>TEPY</x:String>
                                  <x:String>PYME</x:String>
                                  <x:String>CUAM</x:String>
                                  <x:String>IMCD</x:String>
                                  <x:String>CRTP</x:String>
                                  <x:String>dateformat</x:String>
                                  <x:String>excludeempty</x:String>
                                  <x:String>righttrim</x:String>
                                  <x:String>format</x:String>
                                  <x:String>extendedresult</x:String>
                                  <x:String>BKID</x:String>
                                  <x:String>CORI</x:String>
                                  <x:String>TECD</x:String>
                                  <x:String>TXAP</x:String>
                                  <x:String>PAIN</x:String>
                                  <x:String>DEDA</x:String>
                                </scg:List>
                                <scg:List x:TypeArguments="x:String" Capacity="32">
                                  <x:String>SupplierNo</x:String>
                                  <x:String>ivdate</x:String>
                                  <x:String>division</x:String>
                                  <x:String>sino</x:String>
                                  <x:String>cucd</x:String>
                                  <x:String>tepy</x:String>
                                  <x:String>pyme</x:String>
                                  <x:String>cuam</x:String>
                                  <x:String>1</x:String>
                                  <x:String>1</x:String>
                                  <x:String>YMD8</x:String>
                                  <x:String>false</x:String>
                                  <x:String>true</x:String>
                                  <x:String>PRETTY</x:String>
                                  <x:String>false</x:String>
                                  <x:String>bkid</x:String>
                                  <x:String>correlationID</x:String>
                                  <x:String>discountTerms</x:String>
                                  <x:String>txap</x:String>
                                  <x:String>strPain</x:String>
                                  <x:String>ivdate</x:String>
                                </scg:List>
                              </scg:List>
                            </iai:IONAPIRequestWizard.QueryParameters>
                          </iai:IONAPIRequestWizard>
                        </If.Then>
                      </If>
                    </Sequence>
                  </If.Then>
                  <If.Else>
                    <Sequence DisplayName="AddHead_Sequence_7" sap2010:WorkflowViewState.IdRef="Sequence_12">
                      <iai:IONAPIRequestWizard FileAttachments="{x:Null}" PostData="{x:Null}" ResponseCode="{x:Null}" ContentType="application_json" ContinueOnError="True" DisplayName="AddHead_IONAPIRequestWizard_8" HttpMethod="GET" sap2010:WorkflowViewState.IdRef="IONAPIRequestWizard_13" Response="[respObj5]" StatusCode="[StatusCode5]" Url="[TenantID + &quot;M3/m3api-rest/v2/execute/APS450MI/AddHead&quot;]">
                        <iai:IONAPIRequestWizard.Headers>
                          <scg:List x:TypeArguments="scg:List(x:String)" Capacity="4">
                            <scg:List x:TypeArguments="x:String" Capacity="4">
                              <x:String>Accept</x:String>
                            </scg:List>
                            <scg:List x:TypeArguments="x:String" Capacity="4">
                              <x:String>application/json</x:String>
                            </scg:List>
                          </scg:List>
                        </iai:IONAPIRequestWizard.Headers>
                        <iai:IONAPIRequestWizard.QueryParameters>
                          <scg:List x:TypeArguments="scg:List(x:String)" Capacity="4">
                            <scg:List x:TypeArguments="x:String" Capacity="32">
                              <x:String>SUNO</x:String>
                              <x:String>IVDT</x:String>
                              <x:String>DIVI</x:String>
                              <x:String>SINO</x:String>
                              <x:String>CUCD</x:String>
                              <x:String>TEPY</x:String>
                              <x:String>PYME</x:String>
                              <x:String>CUAM</x:String>
                              <x:String>IMCD</x:String>
                              <x:String>CRTP</x:String>
                              <x:String>dateformat</x:String>
                              <x:String>excludeempty</x:String>
                              <x:String>righttrim</x:String>
                              <x:String>format</x:String>
                              <x:String>extendedresult</x:String>
                              <x:String>CORI</x:String>
                              <x:String>TXAP</x:String>
                              <x:String>PAIN</x:String>
                              <x:String>DEDA</x:String>
                            </scg:List>
                            <scg:List x:TypeArguments="x:String" Capacity="32">
                              <x:String>SupplierNo</x:String>
                              <x:String>ivdate</x:String>
                              <x:String>division</x:String>
                              <x:String>sino</x:String>
                              <x:String>cucd</x:String>
                              <x:String>tepy</x:String>
                              <x:String>pyme</x:String>
                              <x:String>cuam</x:String>
                              <x:String>1</x:String>
                              <x:String>1</x:String>
                              <x:String>YMD8</x:String>
                              <x:String>false</x:String>
                              <x:String>true</x:String>
                              <x:String>PRETTY</x:String>
                              <x:String>false</x:String>
                              <x:String>correlationID</x:String>
                              <x:String>txap</x:String>
                              <x:String>strPain</x:String>
                              <x:String>ivdate</x:String>
                            </scg:List>
                          </scg:List>
                        </iai:IONAPIRequestWizard.QueryParameters>
                      </iai:IONAPIRequestWizard>
                      <If Condition="[StatusCode5 = 200  and respObj5.ReadAsText.Contains(&quot;Bank account identity must be entered&quot;)]" DisplayName="AddHead_If_9" sap2010:WorkflowViewState.IdRef="If_12">
                        <If.Then>
                          <iai:IONAPIRequestWizard FileAttachments="{x:Null}" PostData="{x:Null}" ResponseCode="{x:Null}" ContentType="application_json" ContinueOnError="True" DisplayName="AddHead_IONAPIRequestWizard_10" HttpMethod="GET" sap2010:WorkflowViewState.IdRef="IONAPIRequestWizard_14" Response="[respObj5]" StatusCode="[StatusCode5]" Url="[TenantID + &quot;M3/m3api-rest/v2/execute/APS450MI/AddHead&quot;]">
                            <iai:IONAPIRequestWizard.Headers>
                              <scg:List x:TypeArguments="scg:List(x:String)" Capacity="4">
                                <scg:List x:TypeArguments="x:String" Capacity="4">
                                  <x:String>Accept</x:String>
                                </scg:List>
                                <scg:List x:TypeArguments="x:String" Capacity="4">
                                  <x:String>application/json</x:String>
                                </scg:List>
                              </scg:List>
                            </iai:IONAPIRequestWizard.Headers>
                            <iai:IONAPIRequestWizard.QueryParameters>
                              <scg:List x:TypeArguments="scg:List(x:String)" Capacity="4">
                                <scg:List x:TypeArguments="x:String" Capacity="32">
                                  <x:String>SUNO</x:String>
                                  <x:String>IVDT</x:String>
                                  <x:String>DIVI</x:String>
                                  <x:String>SINO</x:String>
                                  <x:String>CUCD</x:String>
                                  <x:String>TEPY</x:String>
                                  <x:String>PYME</x:String>
                                  <x:String>CUAM</x:String>
                                  <x:String>IMCD</x:String>
                                  <x:String>CRTP</x:String>
                                  <x:String>dateformat</x:String>
                                  <x:String>excludeempty</x:String>
                                  <x:String>righttrim</x:String>
                                  <x:String>format</x:String>
                                  <x:String>extendedresult</x:String>
                                  <x:String>BKID</x:String>
                                  <x:String>CORI</x:String>
                                  <x:String>TXAP</x:String>
                                  <x:String>PAIN</x:String>
                                  <x:String>DEDA</x:String>
                                </scg:List>
                                <scg:List x:TypeArguments="x:String" Capacity="32">
                                  <x:String>SupplierNo</x:String>
                                  <x:String>ivdate</x:String>
                                  <x:String>division</x:String>
                                  <x:String>sino</x:String>
                                  <x:String>cucd</x:String>
                                  <x:String>tepy</x:String>
                                  <x:String>pyme</x:String>
                                  <x:String>cuam</x:String>
                                  <x:String>1</x:String>
                                  <x:String>1</x:String>
                                  <x:String>YMD8</x:String>
                                  <x:String>false</x:String>
                                  <x:String>true</x:String>
                                  <x:String>PRETTY</x:String>
                                  <x:String>false</x:String>
                                  <x:String>bkid</x:String>
                                  <x:String>correlationID</x:String>
                                  <x:String>txap</x:String>
                                  <x:String>strPain</x:String>
                                  <x:String>ivdate</x:String>
                                </scg:List>
                              </scg:List>
                            </iai:IONAPIRequestWizard.QueryParameters>
                          </iai:IONAPIRequestWizard>
                        </If.Then>
                      </If>
                    </Sequence>
                  </If.Else>
                </If>
              </Sequence>
            </If.Else>
          </If>
          <If Condition="[StatusCode5 = 200  and respObj5.ReadAsText.Contains(&quot;The field must not be set&quot;)]" sap2010:WorkflowViewState.IdRef="If_23">
            <If.Then>
              <If Condition="[(respObj5.ReadAsJson(&quot;results&quot;)(0)(&quot;errorField&quot;)).ToString = &quot;DEDA&quot;]" sap2010:WorkflowViewState.IdRef="If_31">
                <If.Then>
                  <If Condition="[not String.IsNullOrWhiteSpace(duedate)]" sap2010:WorkflowViewState.IdRef="If_30">
                    <If.Then>
                      <Sequence sap2010:WorkflowViewState.IdRef="Sequence_21">
                        <If Condition="[miscValues(&quot;handleCashDiscount&quot;).ToString.tolower = &quot;true&quot;]" DisplayName="AddHead_If_2" sap2010:WorkflowViewState.IdRef="If_26">
                          <If.Then>
                            <Sequence DisplayName="AddHead_Sequence_3" sap2010:WorkflowViewState.IdRef="Sequence_19">
                              <iai:IONAPIRequestWizard FileAttachments="{x:Null}" PostData="{x:Null}" ResponseCode="{x:Null}" ContentType="application_json" ContinueOnError="True" DisplayName="AddHead_IONAPIRequestWizard_4" HttpMethod="GET" sap2010:WorkflowViewState.IdRef="IONAPIRequestWizard_16" Response="[respObj5]" StatusCode="[StatusCode5]" Url="[TenantID + &quot;M3/m3api-rest/v2/execute/APS450MI/AddHead&quot;]">
                                <iai:IONAPIRequestWizard.Headers>
                                  <scg:List x:TypeArguments="scg:List(x:String)" Capacity="4">
                                    <scg:List x:TypeArguments="x:String" Capacity="4">
                                      <x:String>Accept</x:String>
                                    </scg:List>
                                    <scg:List x:TypeArguments="x:String" Capacity="4">
                                      <x:String>application/json</x:String>
                                    </scg:List>
                                  </scg:List>
                                </iai:IONAPIRequestWizard.Headers>
                                <iai:IONAPIRequestWizard.QueryParameters>
                                  <scg:List x:TypeArguments="scg:List(x:String)" Capacity="4">
                                    <scg:List x:TypeArguments="x:String" Capacity="32">
                                      <x:String>SUNO</x:String>
                                      <x:String>IVDT</x:String>
                                      <x:String>DIVI</x:String>
                                      <x:String>SINO</x:String>
                                      <x:String>CUCD</x:String>
                                      <x:String>TEPY</x:String>
                                      <x:String>PYME</x:String>
                                      <x:String>CUAM</x:String>
                                      <x:String>IMCD</x:String>
                                      <x:String>CRTP</x:String>
                                      <x:String>dateformat</x:String>
                                      <x:String>excludeempty</x:String>
                                      <x:String>righttrim</x:String>
                                      <x:String>format</x:String>
                                      <x:String>extendedresult</x:String>
                                      <x:String>CORI</x:String>
                                      <x:String>TECD</x:String>
                                      <x:String>TXAP</x:String>
                                      <x:String>PAIN</x:String>
                                      <x:String>DUDT</x:String>
                                    </scg:List>
                                    <scg:List x:TypeArguments="x:String" Capacity="32">
                                      <x:String>SupplierNo</x:String>
                                      <x:String>ivdate</x:String>
                                      <x:String>division</x:String>
                                      <x:String>sino</x:String>
                                      <x:String>cucd</x:String>
                                      <x:String>tepy</x:String>
                                      <x:String>pyme</x:String>
                                      <x:String>cuam</x:String>
                                      <x:String>1</x:String>
                                      <x:String>1</x:String>
                                      <x:String>YMD8</x:String>
                                      <x:String>false</x:String>
                                      <x:String>true</x:String>
                                      <x:String>PRETTY</x:String>
                                      <x:String>false</x:String>
                                      <x:String>correlationID</x:String>
                                      <x:String>discountTerms</x:String>
                                      <x:String>txap</x:String>
                                      <x:String>strPain</x:String>
                                      <x:String>duedate</x:String>
                                    </scg:List>
                                  </scg:List>
                                </iai:IONAPIRequestWizard.QueryParameters>
                              </iai:IONAPIRequestWizard>
                              <If Condition="[StatusCode5 = 200  and respObj5.ReadAsText.Contains(&quot;Bank account identity must be entered&quot;)]" DisplayName="AddHead_If_5" sap2010:WorkflowViewState.IdRef="If_24">
                                <If.Then>
                                  <iai:IONAPIRequestWizard FileAttachments="{x:Null}" PostData="{x:Null}" ResponseCode="{x:Null}" ContentType="application_json" ContinueOnError="True" DisplayName="AddHead_IONAPIRequestWizard_6" HttpMethod="GET" sap2010:WorkflowViewState.IdRef="IONAPIRequestWizard_17" Response="[respObj5]" StatusCode="[StatusCode5]" Url="[TenantID + &quot;M3/m3api-rest/v2/execute/APS450MI/AddHead&quot;]">
                                    <iai:IONAPIRequestWizard.Headers>
                                      <scg:List x:TypeArguments="scg:List(x:String)" Capacity="4">
                                        <scg:List x:TypeArguments="x:String" Capacity="4">
                                          <x:String>Accept</x:String>
                                        </scg:List>
                                        <scg:List x:TypeArguments="x:String" Capacity="4">
                                          <x:String>application/json</x:String>
                                        </scg:List>
                                      </scg:List>
                                    </iai:IONAPIRequestWizard.Headers>
                                    <iai:IONAPIRequestWizard.QueryParameters>
                                      <scg:List x:TypeArguments="scg:List(x:String)" Capacity="4">
                                        <scg:List x:TypeArguments="x:String" Capacity="32">
                                          <x:String>SUNO</x:String>
                                          <x:String>IVDT</x:String>
                                          <x:String>DIVI</x:String>
                                          <x:String>SINO</x:String>
                                          <x:String>CUCD</x:String>
                                          <x:String>TEPY</x:String>
                                          <x:String>PYME</x:String>
                                          <x:String>CUAM</x:String>
                                          <x:String>IMCD</x:String>
                                          <x:String>CRTP</x:String>
                                          <x:String>dateformat</x:String>
                                          <x:String>excludeempty</x:String>
                                          <x:String>righttrim</x:String>
                                          <x:String>format</x:String>
                                          <x:String>extendedresult</x:String>
                                          <x:String>BKID</x:String>
                                          <x:String>CORI</x:String>
                                          <x:String>TECD</x:String>
                                          <x:String>TXAP</x:String>
                                          <x:String>PAIN</x:String>
                                          <x:String>DUDT</x:String>
                                        </scg:List>
                                        <scg:List x:TypeArguments="x:String" Capacity="32">
                                          <x:String>SupplierNo</x:String>
                                          <x:String>ivdate</x:String>
                                          <x:String>division</x:String>
                                          <x:String>sino</x:String>
                                          <x:String>cucd</x:String>
                                          <x:String>tepy</x:String>
                                          <x:String>pyme</x:String>
                                          <x:String>cuam</x:String>
                                          <x:String>1</x:String>
                                          <x:String>1</x:String>
                                          <x:String>YMD8</x:String>
                                          <x:String>false</x:String>
                                          <x:String>true</x:String>
                                          <x:String>PRETTY</x:String>
                                          <x:String>false</x:String>
                                          <x:String>bkid</x:String>
                                          <x:String>correlationID</x:String>
                                          <x:String>discountTerms</x:String>
                                          <x:String>txap</x:String>
                                          <x:String>strPain</x:String>
                                          <x:String>duedate</x:String>
                                        </scg:List>
                                      </scg:List>
                                    </iai:IONAPIRequestWizard.QueryParameters>
                                  </iai:IONAPIRequestWizard>
                                </If.Then>
                              </If>
                            </Sequence>
                          </If.Then>
                          <If.Else>
                            <Sequence DisplayName="AddHead_Sequence_7" sap2010:WorkflowViewState.IdRef="Sequence_20">
                              <iai:IONAPIRequestWizard FileAttachments="{x:Null}" PostData="{x:Null}" ResponseCode="{x:Null}" ContentType="application_json" ContinueOnError="True" DisplayName="AddHead_IONAPIRequestWizard_8" HttpMethod="GET" sap2010:WorkflowViewState.IdRef="IONAPIRequestWizard_18" Response="[respObj5]" StatusCode="[StatusCode5]" Url="[TenantID + &quot;M3/m3api-rest/v2/execute/APS450MI/AddHead&quot;]">
                                <iai:IONAPIRequestWizard.Headers>
                                  <scg:List x:TypeArguments="scg:List(x:String)" Capacity="4">
                                    <scg:List x:TypeArguments="x:String" Capacity="4">
                                      <x:String>Accept</x:String>
                                    </scg:List>
                                    <scg:List x:TypeArguments="x:String" Capacity="4">
                                      <x:String>application/json</x:String>
                                    </scg:List>
                                  </scg:List>
                                </iai:IONAPIRequestWizard.Headers>
                                <iai:IONAPIRequestWizard.QueryParameters>
                                  <scg:List x:TypeArguments="scg:List(x:String)" Capacity="4">
                                    <scg:List x:TypeArguments="x:String" Capacity="32">
                                      <x:String>SUNO</x:String>
                                      <x:String>IVDT</x:String>
                                      <x:String>DIVI</x:String>
                                      <x:String>SINO</x:String>
                                      <x:String>CUCD</x:String>
                                      <x:String>TEPY</x:String>
                                      <x:String>PYME</x:String>
                                      <x:String>CUAM</x:String>
                                      <x:String>IMCD</x:String>
                                      <x:String>CRTP</x:String>
                                      <x:String>dateformat</x:String>
                                      <x:String>excludeempty</x:String>
                                      <x:String>righttrim</x:String>
                                      <x:String>format</x:String>
                                      <x:String>extendedresult</x:String>
                                      <x:String>CORI</x:String>
                                      <x:String>TXAP</x:String>
                                      <x:String>PAIN</x:String>
                                      <x:String>DUDT</x:String>
                                    </scg:List>
                                    <scg:List x:TypeArguments="x:String" Capacity="32">
                                      <x:String>SupplierNo</x:String>
                                      <x:String>ivdate</x:String>
                                      <x:String>division</x:String>
                                      <x:String>sino</x:String>
                                      <x:String>cucd</x:String>
                                      <x:String>tepy</x:String>
                                      <x:String>pyme</x:String>
                                      <x:String>cuam</x:String>
                                      <x:String>1</x:String>
                                      <x:String>1</x:String>
                                      <x:String>YMD8</x:String>
                                      <x:String>false</x:String>
                                      <x:String>true</x:String>
                                      <x:String>PRETTY</x:String>
                                      <x:String>false</x:String>
                                      <x:String>correlationID</x:String>
                                      <x:String>txap</x:String>
                                      <x:String>strPain</x:String>
                                      <x:String>duedate</x:String>
                                    </scg:List>
                                  </scg:List>
                                </iai:IONAPIRequestWizard.QueryParameters>
                              </iai:IONAPIRequestWizard>
                              <If Condition="[StatusCode5 = 200  and respObj5.ReadAsText.Contains(&quot;Bank account identity must be entered&quot;)]" DisplayName="AddHead_If_9" sap2010:WorkflowViewState.IdRef="If_25">
                                <If.Then>
                                  <iai:IONAPIRequestWizard FileAttachments="{x:Null}" PostData="{x:Null}" ResponseCode="{x:Null}" ContentType="application_json" ContinueOnError="True" DisplayName="AddHead_IONAPIRequestWizard_10" HttpMethod="GET" sap2010:WorkflowViewState.IdRef="IONAPIRequestWizard_19" Response="[respObj5]" StatusCode="[StatusCode5]" Url="[TenantID + &quot;M3/m3api-rest/v2/execute/APS450MI/AddHead&quot;]">
                                    <iai:IONAPIRequestWizard.Headers>
                                      <scg:List x:TypeArguments="scg:List(x:String)" Capacity="4">
                                        <scg:List x:TypeArguments="x:String" Capacity="4">
                                          <x:String>Accept</x:String>
                                        </scg:List>
                                        <scg:List x:TypeArguments="x:String" Capacity="4">
                                          <x:String>application/json</x:String>
                                        </scg:List>
                                      </scg:List>
                                    </iai:IONAPIRequestWizard.Headers>
                                    <iai:IONAPIRequestWizard.QueryParameters>
                                      <scg:List x:TypeArguments="scg:List(x:String)" Capacity="4">
                                        <scg:List x:TypeArguments="x:String" Capacity="32">
                                          <x:String>SUNO</x:String>
                                          <x:String>IVDT</x:String>
                                          <x:String>DIVI</x:String>
                                          <x:String>SINO</x:String>
                                          <x:String>CUCD</x:String>
                                          <x:String>TEPY</x:String>
                                          <x:String>PYME</x:String>
                                          <x:String>CUAM</x:String>
                                          <x:String>IMCD</x:String>
                                          <x:String>CRTP</x:String>
                                          <x:String>dateformat</x:String>
                                          <x:String>excludeempty</x:String>
                                          <x:String>righttrim</x:String>
                                          <x:String>format</x:String>
                                          <x:String>extendedresult</x:String>
                                          <x:String>BKID</x:String>
                                          <x:String>CORI</x:String>
                                          <x:String>TXAP</x:String>
                                          <x:String>PAIN</x:String>
                                          <x:String>DUDT</x:String>
                                        </scg:List>
                                        <scg:List x:TypeArguments="x:String" Capacity="32">
                                          <x:String>SupplierNo</x:String>
                                          <x:String>ivdate</x:String>
                                          <x:String>division</x:String>
                                          <x:String>sino</x:String>
                                          <x:String>cucd</x:String>
                                          <x:String>tepy</x:String>
                                          <x:String>pyme</x:String>
                                          <x:String>cuam</x:String>
                                          <x:String>1</x:String>
                                          <x:String>1</x:String>
                                          <x:String>YMD8</x:String>
                                          <x:String>false</x:String>
                                          <x:String>true</x:String>
                                          <x:String>PRETTY</x:String>
                                          <x:String>false</x:String>
                                          <x:String>bkid</x:String>
                                          <x:String>correlationID</x:String>
                                          <x:String>txap</x:String>
                                          <x:String>strPain</x:String>
                                          <x:String>duedate</x:String>
                                        </scg:List>
                                      </scg:List>
                                    </iai:IONAPIRequestWizard.QueryParameters>
                                  </iai:IONAPIRequestWizard>
                                </If.Then>
                              </If>
                            </Sequence>
                          </If.Else>
                        </If>
                      </Sequence>
                    </If.Then>
                    <If.Else>
                      <Sequence sap2010:WorkflowViewState.IdRef="Sequence_24">
                        <If Condition="[miscValues(&quot;handleCashDiscount&quot;).ToString.tolower = &quot;true&quot;]" DisplayName="AddHead_If_2" sap2010:WorkflowViewState.IdRef="If_29">
                          <If.Then>
                            <Sequence DisplayName="AddHead_Sequence_3" sap2010:WorkflowViewState.IdRef="Sequence_22">
                              <iai:IONAPIRequestWizard FileAttachments="{x:Null}" PostData="{x:Null}" ResponseCode="{x:Null}" ContentType="application_json" ContinueOnError="True" DisplayName="AddHead_IONAPIRequestWizard_4" HttpMethod="GET" sap2010:WorkflowViewState.IdRef="IONAPIRequestWizard_20" Response="[respObj5]" StatusCode="[StatusCode5]" Url="[TenantID + &quot;M3/m3api-rest/v2/execute/APS450MI/AddHead&quot;]">
                                <iai:IONAPIRequestWizard.Headers>
                                  <scg:List x:TypeArguments="scg:List(x:String)" Capacity="4">
                                    <scg:List x:TypeArguments="x:String" Capacity="4">
                                      <x:String>Accept</x:String>
                                    </scg:List>
                                    <scg:List x:TypeArguments="x:String" Capacity="4">
                                      <x:String>application/json</x:String>
                                    </scg:List>
                                  </scg:List>
                                </iai:IONAPIRequestWizard.Headers>
                                <iai:IONAPIRequestWizard.QueryParameters>
                                  <scg:List x:TypeArguments="scg:List(x:String)" Capacity="4">
                                    <scg:List x:TypeArguments="x:String" Capacity="32">
                                      <x:String>SUNO</x:String>
                                      <x:String>IVDT</x:String>
                                      <x:String>DIVI</x:String>
                                      <x:String>SINO</x:String>
                                      <x:String>CUCD</x:String>
                                      <x:String>TEPY</x:String>
                                      <x:String>PYME</x:String>
                                      <x:String>CUAM</x:String>
                                      <x:String>IMCD</x:String>
                                      <x:String>CRTP</x:String>
                                      <x:String>dateformat</x:String>
                                      <x:String>excludeempty</x:String>
                                      <x:String>righttrim</x:String>
                                      <x:String>format</x:String>
                                      <x:String>extendedresult</x:String>
                                      <x:String>CORI</x:String>
                                      <x:String>TECD</x:String>
                                      <x:String>TXAP</x:String>
                                      <x:String>PAIN</x:String>
                                    </scg:List>
                                    <scg:List x:TypeArguments="x:String" Capacity="32">
                                      <x:String>SupplierNo</x:String>
                                      <x:String>ivdate</x:String>
                                      <x:String>division</x:String>
                                      <x:String>sino</x:String>
                                      <x:String>cucd</x:String>
                                      <x:String>tepy</x:String>
                                      <x:String>pyme</x:String>
                                      <x:String>cuam</x:String>
                                      <x:String>1</x:String>
                                      <x:String>1</x:String>
                                      <x:String>YMD8</x:String>
                                      <x:String>false</x:String>
                                      <x:String>true</x:String>
                                      <x:String>PRETTY</x:String>
                                      <x:String>false</x:String>
                                      <x:String>correlationID</x:String>
                                      <x:String>discountTerms</x:String>
                                      <x:String>txap</x:String>
                                      <x:String>strPain</x:String>
                                    </scg:List>
                                  </scg:List>
                                </iai:IONAPIRequestWizard.QueryParameters>
                              </iai:IONAPIRequestWizard>
                              <If Condition="[StatusCode5 = 200  and respObj5.ReadAsText.Contains(&quot;Bank account identity must be entered&quot;)]" DisplayName="AddHead_If_5" sap2010:WorkflowViewState.IdRef="If_27">
                                <If.Then>
                                  <iai:IONAPIRequestWizard FileAttachments="{x:Null}" PostData="{x:Null}" ResponseCode="{x:Null}" ContentType="application_json" ContinueOnError="True" DisplayName="AddHead_IONAPIRequestWizard_6" HttpMethod="GET" sap2010:WorkflowViewState.IdRef="IONAPIRequestWizard_21" Response="[respObj5]" StatusCode="[StatusCode5]" Url="[TenantID + &quot;M3/m3api-rest/v2/execute/APS450MI/AddHead&quot;]">
                                    <iai:IONAPIRequestWizard.Headers>
                                      <scg:List x:TypeArguments="scg:List(x:String)" Capacity="4">
                                        <scg:List x:TypeArguments="x:String" Capacity="4">
                                          <x:String>Accept</x:String>
                                        </scg:List>
                                        <scg:List x:TypeArguments="x:String" Capacity="4">
                                          <x:String>application/json</x:String>
                                        </scg:List>
                                      </scg:List>
                                    </iai:IONAPIRequestWizard.Headers>
                                    <iai:IONAPIRequestWizard.QueryParameters>
                                      <scg:List x:TypeArguments="scg:List(x:String)" Capacity="4">
                                        <scg:List x:TypeArguments="x:String" Capacity="32">
                                          <x:String>SUNO</x:String>
                                          <x:String>IVDT</x:String>
                                          <x:String>DIVI</x:String>
                                          <x:String>SINO</x:String>
                                          <x:String>CUCD</x:String>
                                          <x:String>TEPY</x:String>
                                          <x:String>PYME</x:String>
                                          <x:String>CUAM</x:String>
                                          <x:String>IMCD</x:String>
                                          <x:String>CRTP</x:String>
                                          <x:String>dateformat</x:String>
                                          <x:String>excludeempty</x:String>
                                          <x:String>righttrim</x:String>
                                          <x:String>format</x:String>
                                          <x:String>extendedresult</x:String>
                                          <x:String>BKID</x:String>
                                          <x:String>CORI</x:String>
                                          <x:String>TECD</x:String>
                                          <x:String>TXAP</x:String>
                                          <x:String>PAIN</x:String>
                                        </scg:List>
                                        <scg:List x:TypeArguments="x:String" Capacity="32">
                                          <x:String>SupplierNo</x:String>
                                          <x:String>ivdate</x:String>
                                          <x:String>division</x:String>
                                          <x:String>sino</x:String>
                                          <x:String>cucd</x:String>
                                          <x:String>tepy</x:String>
                                          <x:String>pyme</x:String>
                                          <x:String>cuam</x:String>
                                          <x:String>1</x:String>
                                          <x:String>1</x:String>
                                          <x:String>YMD8</x:String>
                                          <x:String>false</x:String>
                                          <x:String>true</x:String>
                                          <x:String>PRETTY</x:String>
                                          <x:String>false</x:String>
                                          <x:String>bkid</x:String>
                                          <x:String>correlationID</x:String>
                                          <x:String>discountTerms</x:String>
                                          <x:String>txap</x:String>
                                          <x:String>strPain</x:String>
                                        </scg:List>
                                      </scg:List>
                                    </iai:IONAPIRequestWizard.QueryParameters>
                                  </iai:IONAPIRequestWizard>
                                </If.Then>
                              </If>
                            </Sequence>
                          </If.Then>
                          <If.Else>
                            <Sequence DisplayName="AddHead_Sequence_7" sap2010:WorkflowViewState.IdRef="Sequence_23">
                              <iai:IONAPIRequestWizard FileAttachments="{x:Null}" PostData="{x:Null}" ResponseCode="{x:Null}" ContentType="application_json" ContinueOnError="True" DisplayName="AddHead_IONAPIRequestWizard_8" HttpMethod="GET" sap2010:WorkflowViewState.IdRef="IONAPIRequestWizard_22" Response="[respObj5]" StatusCode="[StatusCode5]" Url="[TenantID + &quot;M3/m3api-rest/v2/execute/APS450MI/AddHead&quot;]">
                                <iai:IONAPIRequestWizard.Headers>
                                  <scg:List x:TypeArguments="scg:List(x:String)" Capacity="4">
                                    <scg:List x:TypeArguments="x:String" Capacity="4">
                                      <x:String>Accept</x:String>
                                    </scg:List>
                                    <scg:List x:TypeArguments="x:String" Capacity="4">
                                      <x:String>application/json</x:String>
                                    </scg:List>
                                  </scg:List>
                                </iai:IONAPIRequestWizard.Headers>
                                <iai:IONAPIRequestWizard.QueryParameters>
                                  <scg:List x:TypeArguments="scg:List(x:String)" Capacity="4">
                                    <scg:List x:TypeArguments="x:String" Capacity="32">
                                      <x:String>SUNO</x:String>
                                      <x:String>IVDT</x:String>
                                      <x:String>DIVI</x:String>
                                      <x:String>SINO</x:String>
                                      <x:String>CUCD</x:String>
                                      <x:String>TEPY</x:String>
                                      <x:String>PYME</x:String>
                                      <x:String>CUAM</x:String>
                                      <x:String>IMCD</x:String>
                                      <x:String>CRTP</x:String>
                                      <x:String>dateformat</x:String>
                                      <x:String>excludeempty</x:String>
                                      <x:String>righttrim</x:String>
                                      <x:String>format</x:String>
                                      <x:String>extendedresult</x:String>
                                      <x:String>CORI</x:String>
                                      <x:String>TXAP</x:String>
                                      <x:String>PAIN</x:String>
                                    </scg:List>
                                    <scg:List x:TypeArguments="x:String" Capacity="32">
                                      <x:String>SupplierNo</x:String>
                                      <x:String>ivdate</x:String>
                                      <x:String>division</x:String>
                                      <x:String>sino</x:String>
                                      <x:String>cucd</x:String>
                                      <x:String>tepy</x:String>
                                      <x:String>pyme</x:String>
                                      <x:String>cuam</x:String>
                                      <x:String>1</x:String>
                                      <x:String>1</x:String>
                                      <x:String>YMD8</x:String>
                                      <x:String>false</x:String>
                                      <x:String>true</x:String>
                                      <x:String>PRETTY</x:String>
                                      <x:String>false</x:String>
                                      <x:String>correlationID</x:String>
                                      <x:String>txap</x:String>
                                      <x:String>strPain</x:String>
                                    </scg:List>
                                  </scg:List>
                                </iai:IONAPIRequestWizard.QueryParameters>
                              </iai:IONAPIRequestWizard>
                              <If Condition="[StatusCode5 = 200  and respObj5.ReadAsText.Contains(&quot;Bank account identity must be entered&quot;)]" DisplayName="AddHead_If_9" sap2010:WorkflowViewState.IdRef="If_28">
                                <If.Then>
                                  <iai:IONAPIRequestWizard FileAttachments="{x:Null}" PostData="{x:Null}" ResponseCode="{x:Null}" ContentType="application_json" ContinueOnError="True" DisplayName="AddHead_IONAPIRequestWizard_10" HttpMethod="GET" sap2010:WorkflowViewState.IdRef="IONAPIRequestWizard_23" Response="[respObj5]" StatusCode="[StatusCode5]" Url="[TenantID + &quot;M3/m3api-rest/v2/execute/APS450MI/AddHead&quot;]">
                                    <iai:IONAPIRequestWizard.Headers>
                                      <scg:List x:TypeArguments="scg:List(x:String)" Capacity="4">
                                        <scg:List x:TypeArguments="x:String" Capacity="4">
                                          <x:String>Accept</x:String>
                                        </scg:List>
                                        <scg:List x:TypeArguments="x:String" Capacity="4">
                                          <x:String>application/json</x:String>
                                        </scg:List>
                                      </scg:List>
                                    </iai:IONAPIRequestWizard.Headers>
                                    <iai:IONAPIRequestWizard.QueryParameters>
                                      <scg:List x:TypeArguments="scg:List(x:String)" Capacity="4">
                                        <scg:List x:TypeArguments="x:String" Capacity="32">
                                          <x:String>SUNO</x:String>
                                          <x:String>IVDT</x:String>
                                          <x:String>DIVI</x:String>
                                          <x:String>SINO</x:String>
                                          <x:String>CUCD</x:String>
                                          <x:String>TEPY</x:String>
                                          <x:String>PYME</x:String>
                                          <x:String>CUAM</x:String>
                                          <x:String>IMCD</x:String>
                                          <x:String>CRTP</x:String>
                                          <x:String>dateformat</x:String>
                                          <x:String>excludeempty</x:String>
                                          <x:String>righttrim</x:String>
                                          <x:String>format</x:String>
                                          <x:String>extendedresult</x:String>
                                          <x:String>BKID</x:String>
                                          <x:String>CORI</x:String>
                                          <x:String>TXAP</x:String>
                                          <x:String>PAIN</x:String>
                                        </scg:List>
                                        <scg:List x:TypeArguments="x:String" Capacity="32">
                                          <x:String>SupplierNo</x:String>
                                          <x:String>ivdate</x:String>
                                          <x:String>division</x:String>
                                          <x:String>sino</x:String>
                                          <x:String>cucd</x:String>
                                          <x:String>tepy</x:String>
                                          <x:String>pyme</x:String>
                                          <x:String>cuam</x:String>
                                          <x:String>1</x:String>
                                          <x:String>1</x:String>
                                          <x:String>YMD8</x:String>
                                          <x:String>false</x:String>
                                          <x:String>true</x:String>
                                          <x:String>PRETTY</x:String>
                                          <x:String>false</x:String>
                                          <x:String>bkid</x:String>
                                          <x:String>correlationID</x:String>
                                          <x:String>txap</x:String>
                                          <x:String>strPain</x:String>
                                        </scg:List>
                                      </scg:List>
                                    </iai:IONAPIRequestWizard.QueryParameters>
                                  </iai:IONAPIRequestWizard>
                                </If.Then>
                              </If>
                            </Sequence>
                          </If.Else>
                        </If>
                      </Sequence>
                    </If.Else>
                  </If>
                </If.Then>
              </If>
            </If.Then>
          </If>
          <If Condition="[StatusCode5 = 200]" DisplayName="AddHead_If_11" sap2010:WorkflowViewState.IdRef="If_3">
            <If.Then>
              <Sequence DisplayName="AddHead_Sequence_12" sap2010:WorkflowViewState.IdRef="Sequence_3">
                <Sequence.Variables>
                  <Variable x:TypeArguments="x:Int32" Name="respout1" />
                  <Variable x:TypeArguments="x:String" Name="additionalChargeExcp" />
                  <Variable x:TypeArguments="x:Int32" Name="m" />
                </Sequence.Variables>
                <Assign DisplayName="AddHead_Assign_13_out5" sap2010:WorkflowViewState.IdRef="Assign_1">
                  <Assign.To>
                    <OutArgument x:TypeArguments="njl:JToken">[out5]</OutArgument>
                  </Assign.To>
                  <Assign.Value>
                    <InArgument x:TypeArguments="njl:JToken">[JToken.Parse(respObj5.ReadAsText)]</InArgument>
                  </Assign.Value>
                </Assign>
                <If Condition="[out5(&quot;results&quot;)(0)(&quot;records&quot;).ToString = &quot;[]&quot;]" DisplayName="AddHead_If_14" sap2010:WorkflowViewState.IdRef="If_2">
                  <If.Then>
                    <Sequence DisplayName="AddHead_Sequence_15" sap2010:WorkflowViewState.IdRef="Sequence_1">
                      <Assign DisplayName="AddHead_Assign_16_InvoiceAlreadyExists" sap2010:WorkflowViewState.IdRef="Assign_2">
                        <Assign.To>
                          <OutArgument x:TypeArguments="x:Boolean">[InvoiceAlreadyExists]</OutArgument>
                        </Assign.To>
                        <Assign.Value>
                          <InArgument x:TypeArguments="x:Boolean">True</InArgument>
                        </Assign.Value>
                      </Assign>
                      <Assign DisplayName="AddHead_Assign_17_inbnValue" sap2010:WorkflowViewState.IdRef="Assign_3">
                        <Assign.To>
                          <OutArgument x:TypeArguments="x:String">[inbnValue]</OutArgument>
                        </Assign.To>
                        <Assign.Value>
                          <InArgument x:TypeArguments="x:String">
                            <Literal x:TypeArguments="x:String" Value="" />
                          </InArgument>
                        </Assign.Value>
                      </Assign>
                      <Assign DisplayName="AddHead_Assign_18_commentStatus" sap2010:WorkflowViewState.IdRef="Assign_4">
                        <Assign.To>
                          <OutArgument x:TypeArguments="x:String">[commentStatus]</OutArgument>
                        </Assign.To>
                        <Assign.Value>
                          <InArgument x:TypeArguments="x:String">[(out5("results")(0)("errorMessage")).ToString]</InArgument>
                        </Assign.Value>
                      </Assign>
                      <If Condition="[respObj5.ReadAsText.Contains(&quot;Bank account identity must be entered&quot;)]" DisplayName="AddHead_If_19" sap2010:WorkflowViewState.IdRef="If_4">
                        <If.Then>
                          <Assign DisplayName="AddHead_Assign_20_Status" sap2010:WorkflowViewState.IdRef="Assign_11">
                            <Assign.To>
                              <OutArgument x:TypeArguments="x:String">[Status]</OutArgument>
                            </Assign.To>
                            <Assign.Value>
                              <InArgument x:TypeArguments="x:String">NEEDSVERIFICATION</InArgument>
                            </Assign.Value>
                          </Assign>
                        </If.Then>
                        <If.Else>
                          <Assign DisplayName="AddHead_Assign_21_Status" sap2010:WorkflowViewState.IdRef="Assign_5">
                            <Assign.To>
                              <OutArgument x:TypeArguments="x:String">[Status]</OutArgument>
                            </Assign.To>
                            <Assign.Value>
                              <InArgument x:TypeArguments="x:String">FAILURE</InArgument>
                            </Assign.Value>
                          </Assign>
                        </If.Else>
                      </If>
                    </Sequence>
                  </If.Then>
                  <If.Else>
                    <Sequence DisplayName="AddHead_Sequence_22" sap2010:WorkflowViewState.IdRef="Sequence_2">
                      <Assign DisplayName="AddHead_Assign_23_InvoiceAlreadyExists" sap2010:WorkflowViewState.IdRef="Assign_6">
                        <Assign.To>
                          <OutArgument x:TypeArguments="x:Boolean">[InvoiceAlreadyExists]</OutArgument>
                        </Assign.To>
                        <Assign.Value>
                          <InArgument x:TypeArguments="x:Boolean">False</InArgument>
                        </Assign.Value>
                      </Assign>
                      <Assign DisplayName="AddHead_Assign_24_inbnValue" sap2010:WorkflowViewState.IdRef="Assign_7">
                        <Assign.To>
                          <OutArgument x:TypeArguments="x:String">[inbnValue]</OutArgument>
                        </Assign.To>
                        <Assign.Value>
                          <InArgument x:TypeArguments="x:String">[(out5("results")(0)("records")(0)("INBN")).ToString]</InArgument>
                        </Assign.Value>
                      </Assign>
                      <Assign DisplayName="AddHead_Assign_25_commentStatus" sap2010:WorkflowViewState.IdRef="Assign_8">
                        <Assign.To>
                          <OutArgument x:TypeArguments="x:String">[commentStatus]</OutArgument>
                        </Assign.To>
                        <Assign.Value>
                          <InArgument x:TypeArguments="x:String">["Invoice Header Created"]</InArgument>
                        </Assign.Value>
                      </Assign>
                    </Sequence>
                  </If.Else>
                </If>
                <ias:Append_Line ErrorCode="{x:Null}" ContinueOnError="True" DisplayName="AddHead_Append_Line_26" sap2010:WorkflowViewState.IdRef="Append_Line_1" Line="[commentStatus]" Source="[logfile]" />
              </Sequence>
            </If.Then>
            <If.Else>
              <Sequence DisplayName="AddHead_Sequence_27" sap2010:WorkflowViewState.IdRef="Sequence_4">
                <Assign DisplayName="AddHead_Assign_28_commentStatus" sap2010:WorkflowViewState.IdRef="Assign_9">
                  <Assign.To>
                    <OutArgument x:TypeArguments="x:String">[commentStatus]</OutArgument>
                  </Assign.To>
                  <Assign.Value>
                    <InArgument x:TypeArguments="x:String">["Received an IONAPI error while adding Invoice header."]</InArgument>
                  </Assign.Value>
                </Assign>
                <Assign DisplayName="AddHead_Assign_29_Status" sap2010:WorkflowViewState.IdRef="Assign_10">
                  <Assign.To>
                    <OutArgument x:TypeArguments="x:String">[Status]</OutArgument>
                  </Assign.To>
                  <Assign.Value>
                    <InArgument x:TypeArguments="x:String">["FAILURE"]</InArgument>
                  </Assign.Value>
                </Assign>
                <ias:Append_Line ErrorCode="{x:Null}" ContinueOnError="True" DisplayName="AddHead_Append_Line_30" sap2010:WorkflowViewState.IdRef="Append_Line_2" Line="[commentStatus]" Source="[logfile]" />
              </Sequence>
            </If.Else>
          </If>
        </Sequence>
      </If.Then>
      <If.Else>
        <Sequence sap2010:WorkflowViewState.IdRef="Sequence_18">
          <Assign DisplayName="AddHead_Assign_24_inbnValue" sap2010:WorkflowViewState.IdRef="Assign_23">
            <Assign.To>
              <OutArgument x:TypeArguments="x:String">[commentStatus]</OutArgument>
            </Assign.To>
            <Assign.Value>
              <InArgument x:TypeArguments="x:String">[(respObj3.ReadAsJson("results")(0)("errorMessage")).ToString]</InArgument>
            </Assign.Value>
          </Assign>
          <Assign DisplayName="AddHead_Assign_29_Status" sap2010:WorkflowViewState.IdRef="Assign_24">
            <Assign.To>
              <OutArgument x:TypeArguments="x:String">[Status]</OutArgument>
            </Assign.To>
            <Assign.Value>
              <InArgument x:TypeArguments="x:String">NEEDSVERIFICATION</InArgument>
            </Assign.Value>
          </Assign>
          <ias:Append_Line ErrorCode="{x:Null}" ContinueOnError="True" DisplayName="AddHead_Append_Line_30" sap2010:WorkflowViewState.IdRef="Append_Line_4" Line="[commentStatus]" Source="[logfile]" />
          <Assign sap2010:WorkflowViewState.IdRef="Assign_25">
            <Assign.To>
              <OutArgument x:TypeArguments="x:Boolean">[Exp]</OutArgument>
            </Assign.To>
            <Assign.Value>
              <InArgument x:TypeArguments="x:Boolean">True</InArgument>
            </Assign.Value>
          </Assign>
        </Sequence>
      </If.Else>
    </If>
    <sads:DebugSymbol.Symbol>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</sads:DebugSymbol.Symbol>
  </Sequence>
  <sap2010:WorkflowViewState.ViewStateManager>
    <sap2010:ViewStateManager>
      <sap2010:ViewStateData Id="Assign_12" sap:VirtualizedContainerService.HintSize="1172,60" />
      <sap2010:ViewStateData Id="Assign_13" sap:VirtualizedContainerService.HintSize="242,60" />
      <sap2010:ViewStateData Id="Assign_14" sap:VirtualizedContainerService.HintSize="242,60" />
      <sap2010:ViewStateData Id="If_15" sap:VirtualizedContainerService.HintSize="1172,208" />
      <sap2010:ViewStateData Id="Assign_15" sap:VirtualizedContainerService.HintSize="1172,60" />
      <sap2010:ViewStateData Id="Assign_18" sap:VirtualizedContainerService.HintSize="1172,60" />
      <sap2010:ViewStateData Id="IONAPIRequestWizard_15" sap:VirtualizedContainerService.HintSize="900,22" />
      <sap2010:ViewStateData Id="Assign_20" sap:VirtualizedContainerService.HintSize="242,60" />
      <sap2010:ViewStateData Id="Assign_21" sap:VirtualizedContainerService.HintSize="242,60" />
      <sap2010:ViewStateData Id="Append_Line_3" sap:VirtualizedContainerService.HintSize="242,22" />
      <sap2010:ViewStateData Id="Assign_22" sap:VirtualizedContainerService.HintSize="242,60" />
      <sap2010:ViewStateData Id="Sequence_16" sap:VirtualizedContainerService.HintSize="264,446">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">True</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="Assign_17" sap:VirtualizedContainerService.HintSize="242,60" />
      <sap2010:ViewStateData Id="If_20" sap:VirtualizedContainerService.HintSize="464,208" />
      <sap2010:ViewStateData Id="Assign_19" sap:VirtualizedContainerService.HintSize="242,60" />
      <sap2010:ViewStateData Id="If_21" sap:VirtualizedContainerService.HintSize="464,208" />
      <sap2010:ViewStateData Id="Sequence_15" sap:VirtualizedContainerService.HintSize="486,580">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">True</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="If_19" sap:VirtualizedContainerService.HintSize="775,728" />
      <sap2010:ViewStateData Id="If_18" sap:VirtualizedContainerService.HintSize="900,876" />
      <sap2010:ViewStateData Id="Sequence_14" sap:VirtualizedContainerService.HintSize="922,1062">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">True</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="If_17" sap:VirtualizedContainerService.HintSize="1047,1210" />
      <sap2010:ViewStateData Id="If_16" sap:VirtualizedContainerService.HintSize="1172,1358">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">True</x:Boolean>
            <x:Boolean x:Key="IsPinned">False</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="IONAPIRequestWizard_7" sap:VirtualizedContainerService.HintSize="464,22" />
      <sap2010:ViewStateData Id="IONAPIRequestWizard_8" sap:VirtualizedContainerService.HintSize="200,22" />
      <sap2010:ViewStateData Id="If_8" sap:VirtualizedContainerService.HintSize="464,208" />
      <sap2010:ViewStateData Id="Sequence_8" sap:VirtualizedContainerService.HintSize="486,394">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">True</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="IONAPIRequestWizard_9" sap:VirtualizedContainerService.HintSize="464,22" />
      <sap2010:ViewStateData Id="IONAPIRequestWizard_10" sap:VirtualizedContainerService.HintSize="200,22" />
      <sap2010:ViewStateData Id="If_9" sap:VirtualizedContainerService.HintSize="464,208" />
      <sap2010:ViewStateData Id="Sequence_9" sap:VirtualizedContainerService.HintSize="486,394">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">True</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="If_10" sap:VirtualizedContainerService.HintSize="997,542">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">True</x:Boolean>
            <x:Boolean x:Key="IsPinned">False</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="Sequence_10" sap:VirtualizedContainerService.HintSize="1019,666">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">True</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="IONAPIRequestWizard_11" sap:VirtualizedContainerService.HintSize="464,22" />
      <sap2010:ViewStateData Id="IONAPIRequestWizard_12" sap:VirtualizedContainerService.HintSize="200,22" />
      <sap2010:ViewStateData Id="If_11" sap:VirtualizedContainerService.HintSize="464,208" />
      <sap2010:ViewStateData Id="Sequence_11" sap:VirtualizedContainerService.HintSize="486,394">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">True</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="IONAPIRequestWizard_13" sap:VirtualizedContainerService.HintSize="464,22" />
      <sap2010:ViewStateData Id="IONAPIRequestWizard_14" sap:VirtualizedContainerService.HintSize="200,22" />
      <sap2010:ViewStateData Id="If_12" sap:VirtualizedContainerService.HintSize="464,208" />
      <sap2010:ViewStateData Id="Sequence_12" sap:VirtualizedContainerService.HintSize="486,394">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">True</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="If_13" sap:VirtualizedContainerService.HintSize="997,542">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">True</x:Boolean>
            <x:Boolean x:Key="IsPinned">False</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="Sequence_13" sap:VirtualizedContainerService.HintSize="1019,666">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">True</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="If_14" sap:VirtualizedContainerService.HintSize="589,51">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">False</x:Boolean>
            <x:Boolean x:Key="IsPinned">False</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="IONAPIRequestWizard_16" sap:VirtualizedContainerService.HintSize="464,22" />
      <sap2010:ViewStateData Id="IONAPIRequestWizard_17" sap:VirtualizedContainerService.HintSize="200,22" />
      <sap2010:ViewStateData Id="If_24" sap:VirtualizedContainerService.HintSize="464,208" />
      <sap2010:ViewStateData Id="Sequence_19" sap:VirtualizedContainerService.HintSize="486,394">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">True</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="IONAPIRequestWizard_18" sap:VirtualizedContainerService.HintSize="464,22" />
      <sap2010:ViewStateData Id="IONAPIRequestWizard_19" sap:VirtualizedContainerService.HintSize="200,22" />
      <sap2010:ViewStateData Id="If_25" sap:VirtualizedContainerService.HintSize="464,208" />
      <sap2010:ViewStateData Id="Sequence_20" sap:VirtualizedContainerService.HintSize="486,394">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">True</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="If_26" sap:VirtualizedContainerService.HintSize="997,542">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">True</x:Boolean>
            <x:Boolean x:Key="IsPinned">False</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="Sequence_21" sap:VirtualizedContainerService.HintSize="1019,666">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">True</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="IONAPIRequestWizard_20" sap:VirtualizedContainerService.HintSize="464,22" />
      <sap2010:ViewStateData Id="IONAPIRequestWizard_21" sap:VirtualizedContainerService.HintSize="200,22" />
      <sap2010:ViewStateData Id="If_27" sap:VirtualizedContainerService.HintSize="464,208" />
      <sap2010:ViewStateData Id="Sequence_22" sap:VirtualizedContainerService.HintSize="486,394">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">True</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="IONAPIRequestWizard_22" sap:VirtualizedContainerService.HintSize="464,22" />
      <sap2010:ViewStateData Id="IONAPIRequestWizard_23" sap:VirtualizedContainerService.HintSize="200,22" />
      <sap2010:ViewStateData Id="If_28" sap:VirtualizedContainerService.HintSize="464,208" />
      <sap2010:ViewStateData Id="Sequence_23" sap:VirtualizedContainerService.HintSize="486,394">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">True</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="If_29" sap:VirtualizedContainerService.HintSize="997,542">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">True</x:Boolean>
            <x:Boolean x:Key="IsPinned">False</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="Sequence_24" sap:VirtualizedContainerService.HintSize="1019,666">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">True</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="If_30" sap:VirtualizedContainerService.HintSize="200,51">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">False</x:Boolean>
            <x:Boolean x:Key="IsPinned">False</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="If_31" sap:VirtualizedContainerService.HintSize="464,208" />
      <sap2010:ViewStateData Id="If_23" sap:VirtualizedContainerService.HintSize="589,356">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">True</x:Boolean>
            <x:Boolean x:Key="IsPinned">False</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="Assign_1" sap:VirtualizedContainerService.HintSize="820,60" />
      <sap2010:ViewStateData Id="Assign_2" sap:VirtualizedContainerService.HintSize="509,60" />
      <sap2010:ViewStateData Id="Assign_3" sap:VirtualizedContainerService.HintSize="509,60" />
      <sap2010:ViewStateData Id="Assign_4" sap:VirtualizedContainerService.HintSize="509,60" />
      <sap2010:ViewStateData Id="Assign_11" sap:VirtualizedContainerService.HintSize="242,60" />
      <sap2010:ViewStateData Id="Assign_5" sap:VirtualizedContainerService.HintSize="242,60" />
      <sap2010:ViewStateData Id="If_4" sap:VirtualizedContainerService.HintSize="509,208" />
      <sap2010:ViewStateData Id="Sequence_1" sap:VirtualizedContainerService.HintSize="531,632">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">True</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="Assign_6" sap:VirtualizedContainerService.HintSize="242,60" />
      <sap2010:ViewStateData Id="Assign_7" sap:VirtualizedContainerService.HintSize="242,60" />
      <sap2010:ViewStateData Id="Assign_8" sap:VirtualizedContainerService.HintSize="242,60" />
      <sap2010:ViewStateData Id="Sequence_2" sap:VirtualizedContainerService.HintSize="264,384">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">True</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="If_2" sap:VirtualizedContainerService.HintSize="820,780" />
      <sap2010:ViewStateData Id="Append_Line_1" sap:VirtualizedContainerService.HintSize="820,22" />
      <sap2010:ViewStateData Id="Sequence_3" sap:VirtualizedContainerService.HintSize="842,1066">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">True</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="Assign_9" sap:VirtualizedContainerService.HintSize="242,60" />
      <sap2010:ViewStateData Id="Assign_10" sap:VirtualizedContainerService.HintSize="242,60" />
      <sap2010:ViewStateData Id="Append_Line_2" sap:VirtualizedContainerService.HintSize="242,22" />
      <sap2010:ViewStateData Id="Sequence_4" sap:VirtualizedContainerService.HintSize="264,346">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">True</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="If_3" sap:VirtualizedContainerService.HintSize="589,51">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">False</x:Boolean>
            <x:Boolean x:Key="IsPinned">False</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="Sequence_17" sap:VirtualizedContainerService.HintSize="611,662">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">True</x:Boolean>
            <x:Boolean x:Key="IsPinned">False</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="Assign_23" sap:VirtualizedContainerService.HintSize="242,60" />
      <sap2010:ViewStateData Id="Assign_24" sap:VirtualizedContainerService.HintSize="242,60" />
      <sap2010:ViewStateData Id="Append_Line_4" sap:VirtualizedContainerService.HintSize="242,22" />
      <sap2010:ViewStateData Id="Assign_25" sap:VirtualizedContainerService.HintSize="242,60" />
      <sap2010:ViewStateData Id="Sequence_18" sap:VirtualizedContainerService.HintSize="200,51">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">False</x:Boolean>
            <x:Boolean x:Key="IsPinned">False</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="If_22" sap:VirtualizedContainerService.HintSize="1172,810" />
      <sap2010:ViewStateData Id="Sequence_5" sap:VirtualizedContainerService.HintSize="1194,2880">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">True</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="RehostedWorkflowDesigner.Workflow_1" sap:VirtualizedContainerService.HintSize="1234,2960" />
    </sap2010:ViewStateManager>
  </sap2010:WorkflowViewState.ViewStateManager>
</Activity>