﻿<Activity mc:Ignorable="sap sap2010 sads" x:Class="RehostedWorkflowDesigner.Workflow"
 xmlns="http://schemas.microsoft.com/netfx/2009/xaml/activities"
 xmlns:iai="clr-namespace:Infor.Activities.IONAPI;assembly=Infor.Activities.IONAPI"
 xmlns:ias="clr-namespace:Infor.Activities.Sys;assembly=Infor.Activities.Sys"
 xmlns:iru="clr-namespace:Infor.RPA.Utilities;assembly=Infor.RPA.Utilities"
 xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006"
 xmlns:mva="clr-namespace:Microsoft.VisualBasic.Activities;assembly=System.Activities"
 xmlns:njl="clr-namespace:Newtonsoft.Json.Linq;assembly=Newtonsoft.Json"
 xmlns:sads="http://schemas.microsoft.com/netfx/2010/xaml/activities/debugger"
 xmlns:sap="http://schemas.microsoft.com/netfx/2009/xaml/activities/presentation"
 xmlns:sap2010="http://schemas.microsoft.com/netfx/2010/xaml/activities/presentation"
 xmlns:scg="clr-namespace:System.Collections.Generic;assembly=mscorlib"
 xmlns:sco="clr-namespace:System.Collections.ObjectModel;assembly=mscorlib"
 xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml">
  <x:Members>
    <x:Property Name="documentName" Type="InArgument(x:String)" />
    <x:Property Name="logicalId" Type="InArgument(x:String)" />
    <x:Property Name="imsAPIUrl" Type="InArgument(x:String)" />
    <x:Property Name="tenantID" Type="InArgument(x:String)" />
    <x:Property Name="RPA_Process_ID" Type="InArgument(x:String)" />
    <x:Property Name="Status" Type="InArgument(x:String)" />
    <x:Property Name="Status_Display" Type="InArgument(x:String)" />
    <x:Property Name="Comments" Type="InArgument(x:String)" />
    <x:Property Name="Last_Run_Time" Type="InArgument(x:String)" />
    <x:Property Name="Process_Type" Type="InArgument(x:String)" />
    <x:Property Name="Name" Type="InArgument(x:String)" />
    <x:Property Name="ERP" Type="InArgument(x:String)" />
    <x:Property Name="Variation_ID" Type="InArgument(x:String)" />
    <x:Property Name="Updated_By" Type="InArgument(x:String)" />
    <x:Property Name="Additional1" Type="InArgument(x:String)" />
    <x:Property Name="Additional2" Type="InArgument(x:String)" />
    <x:Property Name="failureCount" Type="InArgument(x:String)" />
    <x:Property Name="logFile" Type="InArgument(x:String)" />
  </x:Members>
  <mva:VisualBasic.Settings>
    <x:Null />
  </mva:VisualBasic.Settings>
  <sap2010:WorkflowViewState.IdRef>RehostedWorkflowDesigner.Workflow_1</sap2010:WorkflowViewState.IdRef>
  <TextExpression.NamespacesForImplementation>
    <sco:Collection x:TypeArguments="x:String">
      <x:String>System.Activities</x:String>
      <x:String>System.Activities.Statements</x:String>
      <x:String>System.Activities.Expressions</x:String>
      <x:String>System.Activities.Validation</x:String>
      <x:String>System.Activities.XamlIntegration</x:String>
      <x:String>Infor.Activities.Email</x:String>
      <x:String>Infor.Activities.IONAPI</x:String>
      <x:String>Infor.Activities.Sys</x:String>
      <x:String>Infor.RPA.Utilities</x:String>
      <x:String>Microsoft.VisualBasic.Activities</x:String>
      <x:String>Newtonsoft.Json</x:String>
      <x:String>Newtonsoft.Json.Linq</x:String>
      <x:String>System</x:String>
      <x:String>System.Activities.Debugger.Symbol</x:String>
      <x:String>System.Collections.Generic</x:String>
      <x:String>System.Windows.Markup</x:String>
      <x:String>Microsoft.VisualBasic.CompilerServices</x:String>
    </sco:Collection>
  </TextExpression.NamespacesForImplementation>
  <TextExpression.ReferencesForImplementation>
    <sco:Collection x:TypeArguments="AssemblyReference">
      <AssemblyReference>System.Activities</AssemblyReference>
      <AssemblyReference>Infor.Activities.Email</AssemblyReference>
      <AssemblyReference>Infor.Activities.IONAPI</AssemblyReference>
      <AssemblyReference>Infor.Activities.Sys</AssemblyReference>
      <AssemblyReference>Infor.RPA.Utilities</AssemblyReference>
      <AssemblyReference>Newtonsoft.Json</AssemblyReference>
      <AssemblyReference>System.ServiceModel</AssemblyReference>
      <AssemblyReference>System</AssemblyReference>
      <AssemblyReference>System.Core</AssemblyReference>
      <AssemblyReference>mscorlib</AssemblyReference>
      <AssemblyReference>System.Runtime.WindowsRuntime</AssemblyReference>
      <AssemblyReference>Microsoft.Bcl.AsyncInterfaces</AssemblyReference>
      <AssemblyReference>PresentationFramework</AssemblyReference>
      <AssemblyReference>WindowsBase</AssemblyReference>
      <AssemblyReference>PresentationCore</AssemblyReference>
      <AssemblyReference>System.Xaml</AssemblyReference>
      <AssemblyReference>Microsoft.VisualBasic</AssemblyReference>
      <AssemblyReference>System.ComponentModel.Composition</AssemblyReference>
      <AssemblyReference>System.Memory</AssemblyReference>
    </sco:Collection>
  </TextExpression.ReferencesForImplementation>
  <Sequence DisplayName="SentToInvoiceProcessingResults_Headers_Sequence_MainSequence_1" sap2010:WorkflowViewState.IdRef="Sequence_2">
    <Sequence.Variables>
      <Variable x:TypeArguments="njl:JToken" Name="requestToken" />
      <Variable x:TypeArguments="x:String" Name="requestStr" />
      <Variable x:TypeArguments="iru:ResponseObject" Name="responseObject" />
      <Variable x:TypeArguments="x:Int32" Name="responseStatus" />
      <Variable x:TypeArguments="x:String" Name="value" />
      <Variable x:TypeArguments="njl:JToken" Name="jout" />
      <Variable x:TypeArguments="njl:JToken" Name="jout1" />
      <Variable x:TypeArguments="x:String" Name="varJtoken" />
      <Variable x:TypeArguments="iru:ResponseObject" Name="resp" />
      <Variable x:TypeArguments="njl:JToken" Name="uout" />
      <Variable x:TypeArguments="x:String" Name="userID" />
      <Variable x:TypeArguments="x:String" Name="createdBy" />
    </Sequence.Variables>
    <iai:IONAPIRequestWizard FileAttachments="{x:Null}" QueryParameters="{x:Null}" ResponseCode="{x:Null}" StatusCode="{x:Null}" ContentType="application_json" ContinueOnError="True" DisplayName="SentToInvoiceProcessingResults_Headers_IONAPIRequestWizard_resp_2" HttpMethod="GET" sap2010:WorkflowViewState.IdRef="IONAPIRequestWizard_4" PostData="[&quot;&quot;]" Response="[resp]" Url="[tenantID+&quot;ifsservice/usermgt/v2/users/me&quot;]">
      <iai:IONAPIRequestWizard.Headers>
        <scg:List x:TypeArguments="scg:List(x:String)" Capacity="4">
          <scg:List x:TypeArguments="x:String" Capacity="4">
            <x:String>Accept</x:String>
          </scg:List>
          <scg:List x:TypeArguments="x:String" Capacity="4">
            <x:String>application/json</x:String>
          </scg:List>
        </scg:List>
      </iai:IONAPIRequestWizard.Headers>
    </iai:IONAPIRequestWizard>
    <Assign DisplayName="SentToInvoiceProcessingResults_Headers_Assign_uout_3" sap2010:WorkflowViewState.IdRef="Assign_7">
      <Assign.To>
        <OutArgument x:TypeArguments="njl:JToken">[uout]</OutArgument>
      </Assign.To>
      <Assign.Value>
        <InArgument x:TypeArguments="njl:JToken">[JToken.Parse(resp.ReadAsText)]</InArgument>
      </Assign.Value>
    </Assign>
    <Assign DisplayName="SentToInvoiceProcessingResults_Headers_Assign_createdBy_4" sap2010:WorkflowViewState.IdRef="Assign_8">
      <Assign.To>
        <OutArgument x:TypeArguments="x:String">[createdBy]</OutArgument>
      </Assign.To>
      <Assign.Value>
        <InArgument x:TypeArguments="x:String">[(uout("response"))("userlist")(0)("userName").ToString]</InArgument>
      </Assign.Value>
    </Assign>
    <ias:Template_Apply ErrorCode="{x:Null}" ContinueOnError="True" DisplayName="SentToInvoiceProcessingResults_Headers_Template_Apply_value_5" sap2010:WorkflowViewState.IdRef="Template_Apply_3" Template="[&quot;{\&quot;&quot;RPA_Process_ID\&quot;&quot;:\&quot;&quot;{{%RPA_Process_ID%}}\&quot;&quot;,\&quot;&quot;Status\&quot;&quot;:\&quot;&quot;{{%Status%}}\&quot;&quot;,\&quot;&quot;Status_Display\&quot;&quot;:\&quot;&quot;{{%Status_Display%}}\&quot;&quot;,\&quot;&quot;Comments\&quot;&quot;:\&quot;&quot;{{%Comments%}}\&quot;&quot;,\&quot;&quot;Last_Run_Time\&quot;&quot;:\&quot;&quot;{{%Last_Run_Time%}}\&quot;&quot;,\&quot;&quot;Process_Type\&quot;&quot;:\&quot;&quot;{{%Process_Type%}}\&quot;&quot;,\&quot;&quot;Name\&quot;&quot;:\&quot;&quot;{{%Name%}}\&quot;&quot;,\&quot;&quot;ERP\&quot;&quot;:\&quot;&quot;{{%ERP%}}\&quot;&quot;,\&quot;&quot;Variation_ID\&quot;&quot;:\&quot;&quot;{{%Variation_ID%}}\&quot;&quot;,\&quot;&quot;createdBy\&quot;&quot;:\&quot;&quot;{{%createdBy%}}\&quot;&quot;,\&quot;&quot;Additional1\&quot;&quot;:\&quot;&quot;{{%Additional1%}}\&quot;&quot;,\&quot;&quot;Additional2\&quot;&quot;:\&quot;&quot;{{%Additional2%}}\&quot;&quot;,\&quot;&quot;Failure_Count\&quot;&quot;:\&quot;&quot;{{%failureCount%}}\&quot;&quot;,\&quot;&quot;Updated_By\&quot;&quot;:\&quot;&quot;{{%Updated_By%}}\&quot;&quot;}&quot;]" Text="[value]">
      <ias:Template_Apply.Values>
        <scg:List x:TypeArguments="scg:List(x:String)" Capacity="4">
          <scg:List x:TypeArguments="x:String" Capacity="16">
            <x:String>RPA_Process_ID</x:String>
            <x:String>Status</x:String>
            <x:String>Status_Display</x:String>
            <x:String>Comments</x:String>
            <x:String>Last_Run_Time</x:String>
            <x:String>Process_Type</x:String>
            <x:String>Name</x:String>
            <x:String>ERP</x:String>
            <x:String>Variation_ID</x:String>
            <x:String>createdBy</x:String>
            <x:String>Additional1</x:String>
            <x:String>Additional2</x:String>
            <x:String>failureCount</x:String>
            <x:String>Updated_By</x:String>
          </scg:List>
          <scg:List x:TypeArguments="x:String" Capacity="16">
            <x:String>RPA_Process_ID</x:String>
            <x:String>Status</x:String>
            <x:String>Status_Display</x:String>
            <x:String>Comments</x:String>
            <x:String>Last_Run_Time</x:String>
            <x:String>Process_Type</x:String>
            <x:String>Name</x:String>
            <x:String>ERP</x:String>
            <x:String>Variation_ID</x:String>
            <x:String>createdBy</x:String>
            <x:String>Additional1</x:String>
            <x:String>Additional2</x:String>
            <x:String>failureCount</x:String>
            <x:String>Updated_By</x:String>
          </scg:List>
        </scg:List>
      </ias:Template_Apply.Values>
    </ias:Template_Apply>
    <ias:Template_Apply ErrorCode="{x:Null}" ContinueOnError="True" DisplayName="SentToInvoiceProcessingResults_Headers_Template_Apply_requestStr_6" sap2010:WorkflowViewState.IdRef="Template_Apply_4" Template="[&quot;{  &quot;&quot;documentName&quot;&quot;: &quot;&quot;{{%documentName%}}&quot;&quot;,  &quot;&quot;messageId&quot;&quot;: &quot;&quot;msg#1234555&quot;&quot;, &quot;&quot;fromLogicalId&quot;&quot;: &quot;&quot;lid://{{%logicalId%}}&quot;&quot;, &quot;&quot;toLogicalId&quot;&quot;: &quot;&quot;lid://default&quot;&quot;,  &quot;&quot;document&quot;&quot;: {     &quot;&quot;value&quot;&quot;: &quot;&quot;{{%datalakeAttributes%}}&quot;&quot;,  &quot;&quot;encoding&quot;&quot;: &quot;&quot;NONE&quot;&quot;,  &quot;&quot;characterSet&quot;&quot;: &quot;&quot;UTF-8&quot;&quot; } }&quot;]" Text="[requestStr]">
      <ias:Template_Apply.Values>
        <scg:List x:TypeArguments="scg:List(x:String)" Capacity="4">
          <scg:List x:TypeArguments="x:String" Capacity="4">
            <x:String>documentName</x:String>
            <x:String>logicalId</x:String>
            <x:String>datalakeAttributes</x:String>
          </scg:List>
          <scg:List x:TypeArguments="x:String" Capacity="4">
            <x:String>documentName</x:String>
            <x:String>logicalId</x:String>
            <x:String>value</x:String>
          </scg:List>
        </scg:List>
      </ias:Template_Apply.Values>
    </ias:Template_Apply>
    <Assign DisplayName="SentToInvoiceProcessingResults_Headers_Assign_requestStr_7" sap2010:WorkflowViewState.IdRef="Assign_10">
      <Assign.To>
        <OutArgument x:TypeArguments="x:String">[requestStr]</OutArgument>
      </Assign.To>
      <Assign.Value>
        <InArgument x:TypeArguments="x:String">[requestStr.Replace("\n","")]</InArgument>
      </Assign.Value>
    </Assign>
    <ias:DeserializeJSON ErrorCode="{x:Null}" ContinueOnError="True" DisplayName="SentToInvoiceProcessingResults_Headers_DeserializeJSON_jout_8" sap2010:WorkflowViewState.IdRef="DeserializeJSON_2" JTokenObject="[jout]" JTokenString="[requestStr]" />
    <iai:IONAPIRequestWizard FileAttachments="{x:Null}" QueryParameters="{x:Null}" StatusCode="{x:Null}" ContentType="application_json" ContinueOnError="True" DisplayName="SentToInvoiceProcessingResults_Headers_IONAPIRequestWizard_responseObject_9" HttpMethod="POST" sap2010:WorkflowViewState.IdRef="IONAPIRequestWizard_5" PostData="[jout]" Response="[responseObject]" ResponseCode="[responseStatus]" Url="[imsAPIUrl]">
      <iai:IONAPIRequestWizard.Headers>
        <scg:List x:TypeArguments="scg:List(x:String)" Capacity="4">
          <scg:List x:TypeArguments="x:String" Capacity="4">
            <x:String>Accept</x:String>
          </scg:List>
          <scg:List x:TypeArguments="x:String" Capacity="4">
            <x:String>application/json</x:String>
          </scg:List>
        </scg:List>
      </iai:IONAPIRequestWizard.Headers>
    </iai:IONAPIRequestWizard>
    <If Condition="[responseStatus= 200]" DisplayName="SentToInvoiceProcessingResults_Headers_If_responseStatus_10" sap2010:WorkflowViewState.IdRef="If_4">
      <If.Then>
        <Sequence DisplayName="SentToInvoiceProcessingResults_Headers_Sequence_SuccessSequence_11" sap2010:WorkflowViewState.IdRef="Sequence_8">
          <Sequence.Variables>
            <Variable x:TypeArguments="njl:JToken" Name="reponseToken" />
          </Sequence.Variables>
          <Assign DisplayName="SentToInvoiceProcessingResults_Headers_Assign_reponseToken_12" sap2010:WorkflowViewState.IdRef="Assign_9">
            <Assign.To>
              <OutArgument x:TypeArguments="njl:JToken">[reponseToken]</OutArgument>
            </Assign.To>
            <Assign.Value>
              <InArgument x:TypeArguments="njl:JToken">[responseObject.readasjson]</InArgument>
            </Assign.Value>
          </Assign>
          <If Condition="[reponseToken.tostring.Contains(&quot;status&quot;) and reponseToken(&quot;status&quot;).tostring=&quot;OK&quot;]" DisplayName="SentToInvoiceProcessingResults_Headers_If_reponseToken_13" sap2010:WorkflowViewState.IdRef="If_3">
            <If.Then>
              <ias:Append_Line ErrorCode="{x:Null}" ContinueOnError="True" DisplayName="SentToInvoiceProcessingResults_Headers_Append_Line_logFile_14" sap2010:WorkflowViewState.IdRef="Append_Line_5" Line="Header Datalake Status : OK" Source="[logFile]" />
            </If.Then>
            <If.Else>
              <ias:Append_Line ErrorCode="{x:Null}" ContinueOnError="True" DisplayName="SentToInvoiceProcessingResults_Headers_Append_Line_logFile_15" sap2010:WorkflowViewState.IdRef="Append_Line_6" Line="Header Datalake Status : ERROR" Source="[logFile]" />
            </If.Else>
          </If>
        </Sequence>
      </If.Then>
    </If>
    <sads:DebugSymbol.Symbol>d3JDOlxVc2Vyc1xkbWl0dGFwYWxsaVxBcHBEYXRhXExvY2FsXEluZm9yUlBBXE0zSW52b2ljZVByb2Nlc3NpbmdHZW5BSVYzXFNlbnRUb0ludm9pY2VQcm9jZXNzaW5nUmVzdWx0c19IZWFkZXJzLnhhbWwqUwPjAQ4CAQFiBW0fAgE2bgV1DgIBMnYFfQ4CAS5+BaMBGgIBKqQBBbMBGgIBJrQBBbsBDgIBIbwBBbwB+gECARy9AQXIAR8CARTJAQXhAQoCAQJi2gJi6gICATti9AJi/AICATligQNiuAMCATdzMnNRAgE1cDNwOQIBM3swe2gCATF4MXg8AgEvfsMKfswKAgEsfs0Bfr0KAgErpAGrBqQBuQYCASikAdIBpAGlBgIBJ7kBMLkBTQIBJLYBMbYBPQIBIrwB6QG8AfcBAgEfvAHTAbwB2wECAR29Ac0CvQHVAgIBG70B/wK9AZEDAgEZvQHfAr0B8QICARe9AZYDvQGjAwIBFckBE8kBKgIBA8sBCd8BFAIBBc8BC9YBFAIBENcBC94BEAIBBtQBONQBUwIBE9EBOdEBRwIBEdcBGdcBjAECAQfZAQ/ZAYACAgEM3AEP3AGDAgIBCNkBzQHZAeoBAgEP2QHyAdkB/QECAQ3cAc0B3AHtAQIBC9wB9QHcAYACAgEJ</sads:DebugSymbol.Symbol>
  </Sequence>
  <sap2010:WorkflowViewState.ViewStateManager>
    <sap2010:ViewStateManager>
      <sap2010:ViewStateData Id="IONAPIRequestWizard_4" sap:VirtualizedContainerService.HintSize="611,22" />
      <sap2010:ViewStateData Id="Assign_7" sap:VirtualizedContainerService.HintSize="611,60" />
      <sap2010:ViewStateData Id="Assign_8" sap:VirtualizedContainerService.HintSize="611,60" />
      <sap2010:ViewStateData Id="Template_Apply_3" sap:VirtualizedContainerService.HintSize="611,22" />
      <sap2010:ViewStateData Id="Template_Apply_4" sap:VirtualizedContainerService.HintSize="611,22" />
      <sap2010:ViewStateData Id="Assign_10" sap:VirtualizedContainerService.HintSize="611,60" />
      <sap2010:ViewStateData Id="DeserializeJSON_2" sap:VirtualizedContainerService.HintSize="611,22" />
      <sap2010:ViewStateData Id="IONAPIRequestWizard_5" sap:VirtualizedContainerService.HintSize="611,22" />
      <sap2010:ViewStateData Id="Assign_9" sap:VirtualizedContainerService.HintSize="464,60" />
      <sap2010:ViewStateData Id="Append_Line_5" sap:VirtualizedContainerService.HintSize="200,22" />
      <sap2010:ViewStateData Id="Append_Line_6" sap:VirtualizedContainerService.HintSize="200,22" />
      <sap2010:ViewStateData Id="If_3" sap:VirtualizedContainerService.HintSize="464,208" />
      <sap2010:ViewStateData Id="Sequence_8" sap:VirtualizedContainerService.HintSize="486,432">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">True</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="If_4" sap:VirtualizedContainerService.HintSize="611,580">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">True</x:Boolean>
            <x:Boolean x:Key="IsPinned">False</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="Sequence_2" sap:VirtualizedContainerService.HintSize="633,1314">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">True</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="RehostedWorkflowDesigner.Workflow_1" sap:VirtualizedContainerService.HintSize="673,1434" />
    </sap2010:ViewStateManager>
  </sap2010:WorkflowViewState.ViewStateManager>
</Activity>